import{s as J,d as u,i as m,e as p,l as M,b as h,h as _,j as g,k as x,m as d,n as T,p as Q,q as A,r as w,t as W,v as N,w as P,x as C,y}from"../chunks/scheduler.D0cbHTIG.js";import{S as K,i as L,d as S,t as E,a as v,g as D,c as q,m as j,b as I,e as H,f as z}from"../chunks/index.YnsWT1Qn.js";import{p as X}from"../chunks/stores.DM8Uu_uA.js";import{A as Y,a as Z}from"../chunks/AccordionItem.cc1KqoI7.js";import{I as tt,C as et,f as F}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CjNovnpU.js";import"../chunks/inferColumnTypes.Bz8pcAS_.js";import"../chunks/entry.sFL81Vq-.js";function O(f){let t,o="Copied to clipboard",s,n;return{c(){t=d("p"),t.textContent=o,this.h()},l(a){t=_(a,"P",{class:!0,"data-svelte-h":!0}),A(t)!=="svelte-1u5nnc"&&(t.textContent=o),this.h()},h(){h(t,"class","absolute -bottom-14 right-0 text-sm bg-base-200 w-[17ch] text-center font-sans p-2 border border-base-300 rounded-sm")},m(a,l){m(a,t,l),n=!0},i(a){n||(a&&Q(()=>{n&&(s||(s=z(t,F,{duration:250},!0)),s.run(1))}),n=!0)},o(a){a&&(s||(s=z(t,F,{duration:250},!1)),s.run(0)),n=!1},d(a){a&&u(t),a&&s&&s.end()}}}function st(f){let t,o,s,n,a,l,i,e=f[0]&&O();return n=new tt({props:{src:et,class:"w-4 h-4"}}),{c(){t=d("div"),e&&e.c(),o=T(),s=d("button"),H(n.$$.fragment),this.h()},l(r){t=_(r,"DIV",{class:!0});var c=g(t);e&&e.l(c),o=x(c),s=_(c,"BUTTON",{class:!0,title:!0});var k=g(s);I(n.$$.fragment,k),k.forEach(u),c.forEach(u),this.h()},h(){h(s,"class","bg-base-200 border border-base-300 rounded-sm p-2 hover:bg-base-200/80 active:bg-base-200"),h(s,"title","Copy to Clipboard"),h(t,"class","relative")},m(r,c){m(r,t,c),e&&e.m(t,null),p(t,o),p(t,s),j(n,s,null),a=!0,l||(i=M(s,"click",f[1]),l=!0)},p(r,[c]){r[0]?e?c&1&&v(e,1):(e=O(),e.c(),v(e,1),e.m(t,o)):e&&(D(),E(e,1,1,()=>{e=null}),q())},i(r){a||(v(e),v(n.$$.fragment,r),a=!0)},o(r){E(e),E(n.$$.fragment,r),a=!1},d(r){r&&u(t),e&&e.d(),S(n),l=!1,i()}}}function rt(f,t,o){let{textToCopy:s=""}=t,n=!1;const a=()=>{navigator.clipboard.writeText(s),o(0,n=!0),setTimeout(()=>o(0,n=!1),1e3)};return f.$$set=l=>{"textToCopy"in l&&o(2,s=l.textToCopy)},[n,a,s]}class nt extends K{constructor(t){super(),L(this,t,rt,st,J,{textToCopy:2})}}function ot(f){let t,o="Unknown Error Encountered",s,n,a,l=f[0].status+"",i;return{c(){t=d("h1"),t.textContent=o,s=T(),n=d("span"),a=y("HTTP "),i=y(l),this.h()},l(e){t=_(e,"H1",{"data-svelte-h":!0}),A(t)!=="svelte-blh3ny"&&(t.textContent=o),s=x(e),n=_(e,"SPAN",{class:!0});var r=g(n);a=C(r,"HTTP "),i=C(r,l),r.forEach(u),this.h()},h(){h(n,"class","font-mono text-base")},m(e,r){m(e,t,r),m(e,s,r),m(e,n,r),p(n,a),p(n,i)},p(e,r){r&1&&l!==(l=e[0].status+"")&&P(i,l)},i:N,o:N,d(e){e&&(u(t),u(s),u(n))}}}function at(f){let t,o="Application Error",s,n,a,l,i=f[0].error.message&&R(f),e=(f[0].error.stack||f[0].error.cause)&&G(f);return{c(){t=d("h1"),t.textContent=o,s=T(),i&&i.c(),n=T(),e&&e.c(),a=w(),this.h()},l(r){t=_(r,"H1",{class:!0,"data-svelte-h":!0}),A(t)!=="svelte-zh66lr"&&(t.textContent=o),s=x(r),i&&i.l(r),n=x(r),e&&e.l(r),a=w(),this.h()},h(){h(t,"class","mt-0 mb-8 py-0")},m(r,c){m(r,t,c),m(r,s,c),i&&i.m(r,c),m(r,n,c),e&&e.m(r,c),m(r,a,c),l=!0},p(r,c){r[0].error.message?i?i.p(r,c):(i=R(r),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null),r[0].error.stack||r[0].error.cause?e?(e.p(r,c),c&1&&v(e,1)):(e=G(r),e.c(),v(e,1),e.m(a.parentNode,a)):e&&(D(),E(e,1,1,()=>{e=null}),q())},i(r){l||(v(e),l=!0)},o(r){E(e),l=!1},d(r){r&&(u(t),u(s),u(n),u(a)),i&&i.d(r),e&&e.d(r)}}}function lt(f){let t,o="Page Not Found",s,n,a,l=f[0].status+"",i,e,r,c=f[0].url.pathname+"",k,B;return{c(){t=d("h1"),t.textContent=o,s=T(),n=d("p"),a=d("span"),i=y(l),e=y(`: The page
		`),r=d("span"),k=y(c),B=y(" can't be found in the project."),this.h()},l(b){t=_(b,"H1",{class:!0,"data-svelte-h":!0}),A(t)!=="svelte-s9jbdv"&&(t.textContent=o),s=x(b),n=_(b,"P",{});var $=g(n);a=_($,"SPAN",{class:!0});var U=g(a);i=C(U,l),U.forEach(u),e=C($,`: The page
		`),r=_($,"SPAN",{class:!0});var V=g(r);k=C(V,c),V.forEach(u),B=C($," can't be found in the project."),$.forEach(u),this.h()},h(){h(t,"class","mt-0 mb-8 py-0"),h(a,"class","font-mono text-base"),h(r,"class","font-mono text-base bg-base-200")},m(b,$){m(b,t,$),m(b,s,$),m(b,n,$),p(n,a),p(a,i),p(n,e),p(n,r),p(r,k),p(n,B)},p(b,$){$&1&&l!==(l=b[0].status+"")&&P(i,l),$&1&&c!==(c=b[0].url.pathname+"")&&P(k,c)},i:N,o:N,d(b){b&&(u(t),u(s),u(n))}}}function R(f){let t,o,s=f[0].status+"",n,a,l=f[0].error.message+"",i;return{c(){t=d("p"),o=d("span"),n=y(s),a=y(":"),i=y(l),this.h()},l(e){t=_(e,"P",{class:!0});var r=g(t);o=_(r,"SPAN",{class:!0});var c=g(o);n=C(c,s),c.forEach(u),a=C(r,":"),i=C(r,l),r.forEach(u),this.h()},h(){h(o,"class","font-mono text-base"),h(t,"class","font-mono text-sm bg-base-200 px-2 py-2")},m(e,r){m(e,t,r),p(t,o),p(o,n),p(t,a),p(t,i)},p(e,r){r&1&&s!==(s=e[0].status+"")&&P(n,s),r&1&&l!==(l=e[0].error.message+"")&&P(i,l)},d(e){e&&u(t)}}}function G(f){let t,o;return t=new Y({props:{$$slots:{default:[ct]},$$scope:{ctx:f}}}),{c(){H(t.$$.fragment)},l(s){I(t.$$.fragment,s)},m(s,n){j(t,s,n),o=!0},p(s,n){const a={};n&10&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){o||(v(t.$$.fragment,s),o=!0)},o(s){E(t.$$.fragment,s),o=!1},d(s){S(t,s)}}}function it(f){let t,o,s,n,a,l,i;return s=new nt({props:{textToCopy:f[1]}}),{c(){t=d("div"),o=d("span"),H(s.$$.fragment),n=T(),a=d("pre"),l=y(f[1]),this.h()},l(e){t=_(e,"DIV",{class:!0});var r=g(t);o=_(r,"SPAN",{class:!0});var c=g(o);I(s.$$.fragment,c),c.forEach(u),n=x(r),a=_(r,"PRE",{class:!0});var k=g(a);l=C(k,f[1]),k.forEach(u),r.forEach(u),this.h()},h(){h(o,"class","absolute top-2 right-2"),h(a,"class","font-mono text-sm bg-base-200 px-2 py-2 overflow-auto"),h(t,"class","relative")},m(e,r){m(e,t,r),p(t,o),j(s,o,null),p(t,n),p(t,a),p(a,l),i=!0},p(e,r){const c={};r&2&&(c.textToCopy=e[1]),s.$set(c),(!i||r&2)&&P(l,e[1])},i(e){i||(v(s.$$.fragment,e),i=!0)},o(e){E(s.$$.fragment,e),i=!1},d(e){e&&u(t),S(s)}}}function ct(f){let t,o;return t=new Z({props:{title:"Error Details",$$slots:{default:[it]},$$scope:{ctx:f}}}),{c(){H(t.$$.fragment)},l(s){I(t.$$.fragment,s)},m(s,n){j(t,s,n),o=!0},p(s,n){const a={};n&10&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){o||(v(t.$$.fragment,s),o=!0)},o(s){E(t.$$.fragment,s),o=!1},d(s){S(t,s)}}}function ft(f){let t,o,s,n;const a=[lt,at,ot],l=[];function i(e,r){return e[0].status===404?0:e[0].status===500?1:2}return t=i(f),o=l[t]=a[t](f),{c(){o.c(),s=w()},l(e){o.l(e),s=w()},m(e,r){l[t].m(e,r),m(e,s,r),n=!0},p(e,[r]){let c=t;t=i(e),t===c?l[t].p(e,r):(D(),E(l[c],1,1,()=>{l[c]=null}),q(),o=l[t],o?o.p(e,r):(o=l[t]=a[t](e),o.c()),v(o,1),o.m(s.parentNode,s))},i(e){n||(v(o),n=!0)},o(e){E(o),n=!1},d(e){e&&u(s),l[t].d(e)}}}function ut(f,t,o){let s,n;W(f,X,l=>o(0,n=l));{const l=document.getElementById("__evidence_project_splash");l==null||l.remove()}const a=l=>{let i="";return l.stack&&(i+=l.stack),l.cause&&(i+=`

Caused By:
	`,i+=a(l.cause).split(`
`).join(`
	`)),i};return f.$$.update=()=>{f.$$.dirty&1&&o(1,s=a(n.error))},[n,s]}class vt extends K{constructor(t){super(),L(this,t,ut,ft,J,{})}}export{vt as component};
