import{s as J,d as u,i as m,e as p,l as M,b as h,h as _,j as g,k as x,m as d,n as T,p as Q,q as A,r as w,t as W,v as N,w as P,x as C,y}from"../chunks/scheduler.D0cbHTIG.js";import{S as K,i as L,d as S,t as E,a as v,g as D,c as q,m as j,b as I,e as H,f as z}from"../chunks/index.YnsWT1Qn.js";import{p as X}from"../chunks/stores.DM8Uu_uA.js";import{A as Y,a as Z}from"../chunks/AccordionItem.cc1KqoI7.js";import{I as tt,C as et,f as F}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CjNovnpU.js";import"../chunks/inferColumnTypes.Bz8pcAS_.js";import"../chunks/entry.sFL81Vq-.js";function O(t){let s,e,a,n="Copied to clipboard";return{c(){s=d("p"),s.textContent=n,this.h()},l(t){s=_(t,"P",{class:!0,"data-svelte-h":!0}),"svelte-1u5nnc"!==A(s)&&(s.textContent=n),this.h()},h(){h(s,"class","absolute -bottom-14 right-0 text-sm bg-base-200 w-[17ch] text-center font-sans p-2 border border-base-300 rounded-sm")},m(t,e){m(t,s,e),a=!0},i(t){a||(t&&Q((()=>{a&&(e||(e=z(s,F,{duration:250},!0)),e.run(1))})),a=!0)},o(t){t&&(e||(e=z(s,F,{duration:250},!1)),e.run(0)),a=!1},d(t){t&&u(s),t&&e&&e.end()}}}function st(t){let s,e,a,n,r,o,c,l=t[0]&&O();return n=new tt({props:{src:et,class:"w-4 h-4"}}),{c(){s=d("div"),l&&l.c(),e=T(),a=d("button"),H(n.$$.fragment),this.h()},l(t){s=_(t,"DIV",{class:!0});var r=g(s);l&&l.l(r),e=x(r),a=_(r,"BUTTON",{class:!0,title:!0});var o=g(a);I(n.$$.fragment,o),o.forEach(u),r.forEach(u),this.h()},h(){h(a,"class","bg-base-200 border border-base-300 rounded-sm p-2 hover:bg-base-200/80 active:bg-base-200"),h(a,"title","Copy to Clipboard"),h(s,"class","relative")},m(u,h){m(u,s,h),l&&l.m(s,null),p(s,e),p(s,a),j(n,a,null),r=!0,o||(c=M(a,"click",t[1]),o=!0)},p(t,[a]){t[0]?l?1&a&&v(l,1):(l=O(),l.c(),v(l,1),l.m(s,e)):l&&(D(),E(l,1,1,(()=>{l=null})),q())},i(t){r||(v(l),v(n.$$.fragment,t),r=!0)},o(t){E(l),E(n.$$.fragment,t),r=!1},d(t){t&&u(s),l&&l.d(),S(n),o=!1,c()}}}function rt(t,s,e){let{textToCopy:a=""}=s,n=!1;return t.$$set=t=>{"textToCopy"in t&&e(2,a=t.textToCopy)},[n,()=>{navigator.clipboard.writeText(a),e(0,n=!0),setTimeout((()=>e(0,n=!1)),1e3)},a]}class nt extends K{constructor(t){super(),L(this,t,rt,st,J,{textToCopy:2})}}function ot(t){let s,e,a,n,r,o="Unknown Error Encountered",c=t[0].status+"";return{c(){s=d("h1"),s.textContent=o,e=T(),a=d("span"),n=y("HTTP "),r=y(c),this.h()},l(t){s=_(t,"H1",{"data-svelte-h":!0}),"svelte-blh3ny"!==A(s)&&(s.textContent=o),e=x(t),a=_(t,"SPAN",{class:!0});var l=g(a);n=C(l,"HTTP "),r=C(l,c),l.forEach(u),this.h()},h(){h(a,"class","font-mono text-base")},m(t,o){m(t,s,o),m(t,e,o),m(t,a,o),p(a,n),p(a,r)},p(t,s){1&s&&c!==(c=t[0].status+"")&&P(r,c)},i:N,o:N,d(t){t&&(u(s),u(e),u(a))}}}function at(t){let s,e,a,n,r,o="Application Error",c=t[0].error.message&&R(t),l=(t[0].error.stack||t[0].error.cause)&&G(t);return{c(){s=d("h1"),s.textContent=o,e=T(),c&&c.c(),a=T(),l&&l.c(),n=w(),this.h()},l(t){s=_(t,"H1",{class:!0,"data-svelte-h":!0}),"svelte-zh66lr"!==A(s)&&(s.textContent=o),e=x(t),c&&c.l(t),a=x(t),l&&l.l(t),n=w(),this.h()},h(){h(s,"class","mt-0 mb-8 py-0")},m(t,o){m(t,s,o),m(t,e,o),c&&c.m(t,o),m(t,a,o),l&&l.m(t,o),m(t,n,o),r=!0},p(t,s){t[0].error.message?c?c.p(t,s):(c=R(t),c.c(),c.m(a.parentNode,a)):c&&(c.d(1),c=null),t[0].error.stack||t[0].error.cause?l?(l.p(t,s),1&s&&v(l,1)):(l=G(t),l.c(),v(l,1),l.m(n.parentNode,n)):l&&(D(),E(l,1,1,(()=>{l=null})),q())},i(t){r||(v(l),r=!0)},o(t){E(l),r=!1},d(t){t&&(u(s),u(e),u(a),u(n)),c&&c.d(t),l&&l.d(t)}}}function lt(t){let s,e,a,n,r,o,c,l,i,f="Page Not Found",$=t[0].status+"",v=t[0].url.pathname+"";return{c(){s=d("h1"),s.textContent=f,e=T(),a=d("p"),n=d("span"),r=y($),o=y(": The page\n\t\t"),c=d("span"),l=y(v),i=y(" can't be found in the project."),this.h()},l(t){s=_(t,"H1",{class:!0,"data-svelte-h":!0}),"svelte-s9jbdv"!==A(s)&&(s.textContent=f),e=x(t),a=_(t,"P",{});var p=g(a);n=_(p,"SPAN",{class:!0});var m=g(n);r=C(m,$),m.forEach(u),o=C(p,": The page\n\t\t"),c=_(p,"SPAN",{class:!0});var h=g(c);l=C(h,v),h.forEach(u),i=C(p," can't be found in the project."),p.forEach(u),this.h()},h(){h(s,"class","mt-0 mb-8 py-0"),h(n,"class","font-mono text-base"),h(c,"class","font-mono text-base bg-base-200")},m(t,u){m(t,s,u),m(t,e,u),m(t,a,u),p(a,n),p(n,r),p(a,o),p(a,c),p(c,l),p(a,i)},p(t,s){1&s&&$!==($=t[0].status+"")&&P(r,$),1&s&&v!==(v=t[0].url.pathname+"")&&P(l,v)},i:N,o:N,d(t){t&&(u(s),u(e),u(a))}}}function R(t){let s,e,a,n,r,o=t[0].status+"",c=t[0].error.message+"";return{c(){s=d("p"),e=d("span"),a=y(o),n=y(":"),r=y(c),this.h()},l(t){s=_(t,"P",{class:!0});var l=g(s);e=_(l,"SPAN",{class:!0});var p=g(e);a=C(p,o),p.forEach(u),n=C(l,":"),r=C(l,c),l.forEach(u),this.h()},h(){h(e,"class","font-mono text-base"),h(s,"class","font-mono text-sm bg-base-200 px-2 py-2")},m(t,o){m(t,s,o),p(s,e),p(e,a),p(s,n),p(s,r)},p(t,s){1&s&&o!==(o=t[0].status+"")&&P(a,o),1&s&&c!==(c=t[0].error.message+"")&&P(r,c)},d(t){t&&u(s)}}}function G(t){let s,e;return s=new Y({props:{$$slots:{default:[ct]},$$scope:{ctx:t}}}),{c(){H(s.$$.fragment)},l(t){I(s.$$.fragment,t)},m(t,a){j(s,t,a),e=!0},p(t,e){const a={};10&e&&(a.$$scope={dirty:e,ctx:t}),s.$set(a)},i(t){e||(v(s.$$.fragment,t),e=!0)},o(t){E(s.$$.fragment,t),e=!1},d(t){S(s,t)}}}function it(t){let s,e,a,n,r,o,c;return a=new nt({props:{textToCopy:t[1]}}),{c(){s=d("div"),e=d("span"),H(a.$$.fragment),n=T(),r=d("pre"),o=y(t[1]),this.h()},l(c){s=_(c,"DIV",{class:!0});var l=g(s);e=_(l,"SPAN",{class:!0});var p=g(e);I(a.$$.fragment,p),p.forEach(u),n=x(l),r=_(l,"PRE",{class:!0});var m=g(r);o=C(m,t[1]),m.forEach(u),l.forEach(u),this.h()},h(){h(e,"class","absolute top-2 right-2"),h(r,"class","font-mono text-sm bg-base-200 px-2 py-2 overflow-auto"),h(s,"class","relative")},m(t,l){m(t,s,l),p(s,e),j(a,e,null),p(s,n),p(s,r),p(r,o),c=!0},p(t,s){const e={};2&s&&(e.textToCopy=t[1]),a.$set(e),(!c||2&s)&&P(o,t[1])},i(t){c||(v(a.$$.fragment,t),c=!0)},o(t){E(a.$$.fragment,t),c=!1},d(t){t&&u(s),S(a)}}}function ct(t){let s,e;return s=new Z({props:{title:"Error Details",$$slots:{default:[it]},$$scope:{ctx:t}}}),{c(){H(s.$$.fragment)},l(t){I(s.$$.fragment,t)},m(t,a){j(s,t,a),e=!0},p(t,e){const a={};10&e&&(a.$$scope={dirty:e,ctx:t}),s.$set(a)},i(t){e||(v(s.$$.fragment,t),e=!0)},o(t){E(s.$$.fragment,t),e=!1},d(t){S(s,t)}}}function ft(t){let s,e,a,n;const r=[lt,at,ot],o=[];function c(t,s){return 404===t[0].status?0:500===t[0].status?1:2}return s=c(t),e=o[s]=r[s](t),{c(){e.c(),a=w()},l(t){e.l(t),a=w()},m(t,e){o[s].m(t,e),m(t,a,e),n=!0},p(t,[n]){let l=s;s=c(t),s===l?o[s].p(t,n):(D(),E(o[l],1,1,(()=>{o[l]=null})),q(),e=o[s],e?e.p(t,n):(e=o[s]=r[s](t),e.c()),v(e,1),e.m(a.parentNode,a))},i(t){n||(v(e),n=!0)},o(t){E(e),n=!1},d(t){t&&u(a),o[s].d(t)}}}function ut(t,s,e){let a,n;W(t,X,(t=>e(0,n=t)));{const t=document.getElementById("__evidence_project_splash");null==t||t.remove()}const r=t=>{let s="";return t.stack&&(s+=t.stack),t.cause&&(s+="\n\nCaused By:\n\t",s+=r(t.cause).split("\n").join("\n\t")),s};return t.$$.update=()=>{1&t.$$.dirty&&e(1,a=r(n.error))},[n,a]}class vt extends K{constructor(t){super(),L(this,t,ut,ft,J,{})}}export{vt as component};