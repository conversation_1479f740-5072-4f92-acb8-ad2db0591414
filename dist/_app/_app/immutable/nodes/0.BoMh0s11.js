/*! For license information please see 0.BoMh0s11.js.LICENSE.txt */
var Lg=Object.defineProperty,zc=t=>{throw TypeError(t)},Rg=(t,e,n)=>e in t?Lg(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Ja=(t,e,n)=>Rg(t,"symbol"!=typeof e?e+"":e,n),Wc=(t,e,n)=>e.has(t)||zc("Cannot "+n),Ar=(t,e,n)=>(Wc(t,e,"read from private field"),n?n.call(t):e.get(t)),no=(t,e,n)=>e.has(t)?zc("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),Qa=(t,e,n,r)=>(Wc(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),Hc=(t,e,n,r)=>({set _(r){Qa(t,e,r,n)},get _(){return Ar(t,e,r)}});import{_ as $n,d as xt,f as js,g as Bi,h as Mt,T as A,i as xr,j as Ug,k as Vg,a as Wt,l as vh,m as zg,q as Wg,n as Hg,u as xg,t as xc,o as qc,v as wh,w as Sh,x as qg,e as Yg,Q as Yc}from"../chunks/inferColumnTypes.Bz8pcAS_.js";import{p as ko}from"../chunks/profile.BW8tN6E9.js";import{R as ho,s as Zt,d as w,i as L,r as ut,a6 as Ue,a7 as te,a8 as Sr,o as ne,J as kn,c as ge,G as zn,u as _e,g as be,a as ye,ao as ji,l as Pt,H as Ir,h as q,j as J,m as Y,K as Kg,E as Ih,a5 as Jg,t as Te,a9 as Ge,p as En,w as Re,b as C,e as W,k as nt,x as vt,n as rt,y as wt,ag as Qg,v as Xt,ap as vs,F as Cr,A as Gs,q as vr,Q as Gg,ae as An,P as Fr,D as Xg,I as Zg,ab as Oh}from"../chunks/scheduler.D0cbHTIG.js";import{S as re,i as ie,t as E,a as I,g as jt,c as Lt,f as Dn,h as is,j as qi,d as ot,m as st,b as at,e as lt,p as kh}from"../chunks/index.YnsWT1Qn.js";import{l as ni,bj as $g,m as Nt,w as tn,t as Do,r as Kc,Z as Jc,x as an,a8 as Yn,A as ar,B as $t,a0 as Qc,H as ln,bk as t_,G as ro,y as e_,bl as wl,a6 as n_,n as io,a1 as lr,aa as Gc,v as Ls,z as Ga,q as Dh,k as gi,af as Xc,ag as Zc,bm as r_,bn as qr,N as rn,M as i_,K as o_,L as s_,ah as a_,O as tc,bo as l_,Q as jr,bp as $c,R as Ma,ak as Sl,bb as ii,e as he,u as Eh,o as c_,aj as u_,g as Ah,bq as f_,br as d_,I as Pn,bs as h_,bt as Xs,bu as m_,f as xo,bv as p_,bw as g_,bx as __,by as b_,bz as y_,bA as v_,bB as w_,bC as Il,bg as S_,bD as I_}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CjNovnpU.js";import{w as Ke,d as mo,b as O_,c as k_}from"../chunks/entry.sFL81Vq-.js";import{p as Na,n as Th}from"../chunks/stores.DM8Uu_uA.js";import{d as Ol}from"../chunks/index.rV6zwFgL.js";import{c as D_,h as $e,g as E_,d as A_,r as ci,a as T_,b as B_,G as P_,X as M_,B as N_,S as C_}from"../chunks/index.DmPYR7xL.js";import{A as F_,a as Xa}from"../chunks/AccordionItem.cc1KqoI7.js";const j_=new TextDecoder("utf-8"),kl=t=>j_.decode(t),L_=new TextEncoder,ec=t=>L_.encode(t),R_=t=>"number"==typeof t,U_=t=>"boolean"==typeof t,en=t=>"function"==typeof t,Un=t=>null!=t&&Object(t)===t,qo=t=>Un(t)&&en(t.then),Ca=t=>Un(t)&&en(t[Symbol.iterator]),nc=t=>Un(t)&&en(t[Symbol.asyncIterator]),Dl=t=>Un(t)&&Un(t.schema),Bh=t=>Un(t)&&"done"in t&&"value"in t,Ph=t=>Un(t)&&en(t.stat)&&R_(t.fd),Mh=t=>Un(t)&&rc(t.body),Nh=t=>"_getDOMStream"in t&&"_getNodeStream"in t,rc=t=>Un(t)&&en(t.cancel)&&en(t.getReader)&&!Nh(t),Ch=t=>Un(t)&&en(t.read)&&en(t.pipe)&&U_(t.readable)&&!Nh(t),V_=t=>Un(t)&&en(t.clear)&&en(t.bytes)&&en(t.position)&&en(t.setPosition)&&en(t.capacity)&&en(t.getBufferIdentifier)&&en(t.createLong),ic=typeof SharedArrayBuffer<"u"?SharedArrayBuffer:ArrayBuffer;function z_(t){const e=t[0]?[t[0]]:[];let n,r,i,o;for(let s,a,l=0,c=0,u=t.length;++l<u;)s=e[c],a=t[l],!s||!a||s.buffer!==a.buffer||a.byteOffset<s.byteOffset?a&&(e[++c]=a):(({byteOffset:n,byteLength:i}=s),({byteOffset:r,byteLength:o}=a),n+i<r||r+o<n?a&&(e[++c]=a):e[c]=new Uint8Array(s.buffer,n,r-n+o));return e}function tu(t,e,n=0,r=e.byteLength){const i=t.byteLength,o=new Uint8Array(t.buffer,t.byteOffset,i),s=new Uint8Array(e.buffer,e.byteOffset,Math.min(r,i));return o.set(s,n),t}function rr(t,e){const n=z_(t),r=n.reduce(((t,e)=>t+e.byteLength),0);let i,o,s,a=0,l=-1;const c=Math.min(e||Number.POSITIVE_INFINITY,r);for(const t=n.length;++l<t;){if(i=n[l],o=i.subarray(0,Math.min(i.length,c-a)),c<=a+o.length){o.length<i.length?n[l]=i.subarray(o.length):o.length===i.length&&l++,s?tu(s,o,a):s=o;break}tu(s||(s=new Uint8Array(c)),o,a),a+=o.length}return[s||new Uint8Array(0),n.slice(l),r-(s?s.byteLength:0)]}function fe(t,e){let n=Bh(e)?e.value:e;return n instanceof t?t===Uint8Array?new t(n.buffer,n.byteOffset,n.byteLength):n:n?("string"==typeof n&&(n=ec(n)),n instanceof ArrayBuffer||n instanceof ic?new t(n):V_(n)?fe(t,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new t(0):new t(n.buffer,n.byteOffset,n.byteLength/t.BYTES_PER_ELEMENT):t.from(n)):new t(0)}const oo=t=>fe(Int32Array,t),eu=t=>fe(BigInt64Array,t),Gt=t=>fe(Uint8Array,t),El=t=>(t.next(),t);function*W_(t,e){const n=function*(t){yield t},r="string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof ic?n(e):Ca(e)?e:n(e);return yield*El(function*(e){let n=null;do{n=e.next(yield fe(t,n))}while(!n.done)}(r[Symbol.iterator]())),new t}const H_=t=>W_(Uint8Array,t);function Fh(t,e){return $n(this,arguments,(function*(){if(qo(e))return yield xt(yield xt(yield*js(Bi(Fh(t,yield xt(e))))));const n=function(t){return $n(this,arguments,(function*(){yield yield xt(yield xt(t))}))},r="string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof ic?n(e):Ca(e)?function(t){return $n(this,arguments,(function*(){yield xt(yield*js(Bi(El(function*(t){let e=null;do{e=t.next(yield null==e?void 0:e.value)}while(!e.done)}(t[Symbol.iterator]())))))}))}(e):nc(e)?e:n(e);return yield xt(yield*js(Bi(El(function(e){return $n(this,arguments,(function*(){let n=null;do{n=yield xt(e.next(yield yield xt(fe(t,n))))}while(!n.done)}))}(r[Symbol.asyncIterator]()))))),yield xt(new t)}))}const x_=t=>Fh(Uint8Array,t);function q_(t,e){let n=0;const r=t.length;if(r!==e.length)return!1;if(r>0)do{if(t[n]!==e[n])return!1}while(++n<r);return!0}const wn={fromIterable:t=>ws(Y_(t)),fromAsyncIterable:t=>ws(K_(t)),fromDOMStream:t=>ws(J_(t)),fromNodeStream:t=>ws(G_(t)),toDOMStream(t,e){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(t,e){throw new Error('"toNodeStream" not available in this environment')}},ws=t=>(t.next(),t);function*Y_(t){let e,n,r,i,o=!1,s=[],a=0;({cmd:r,size:i}=(yield null)||{cmd:"read",size:0});const l=H_(t)[Symbol.iterator]();try{do{if(({done:e,value:n}=Number.isNaN(i-a)?l.next():l.next(i-a)),!e&&n.byteLength>0&&(s.push(n),a+=n.byteLength),e||i<=a)do{({cmd:r,size:i}=yield"peek"===r?rr(s,i)[0]:([n,s,a]=rr(s,i),n))}while(i<a)}while(!e)}catch(t){(o=!0)&&"function"==typeof l.throw&&l.throw(t)}finally{!1===o&&"function"==typeof l.return&&l.return(null)}return null}function K_(t){return $n(this,arguments,(function*(){let e,n,r,i,o=!1,s=[],a=0;({cmd:r,size:i}=(yield yield xt(null))||{cmd:"read",size:0});const l=x_(t)[Symbol.asyncIterator]();try{do{if(({done:e,value:n}=Number.isNaN(i-a)?yield xt(l.next()):yield xt(l.next(i-a))),!e&&n.byteLength>0&&(s.push(n),a+=n.byteLength),e||i<=a)do{({cmd:r,size:i}=yield yield xt("peek"===r?rr(s,i)[0]:([n,s,a]=rr(s,i),n)))}while(i<a)}while(!e)}catch(t){(o=!0)&&"function"==typeof l.throw&&(yield xt(l.throw(t)))}finally{!1===o&&"function"==typeof l.return&&(yield xt(l.return(new Uint8Array(0))))}return yield xt(null)}))}function J_(t){return $n(this,arguments,(function*(){let e,n,r,i=!1,o=!1,s=[],a=0;({cmd:n,size:r}=(yield yield xt(null))||{cmd:"read",size:0});const l=new Q_(t);try{do{if(({done:i,value:e}=Number.isNaN(r-a)?yield xt(l.read()):yield xt(l.read(r-a))),!i&&e.byteLength>0&&(s.push(Gt(e)),a+=e.byteLength),i||r<=a)do{({cmd:n,size:r}=yield yield xt("peek"===n?rr(s,r)[0]:([e,s,a]=rr(s,r),e)))}while(r<a)}while(!i)}catch(t){(o=!0)&&(yield xt(l.cancel(t)))}finally{!1===o?yield xt(l.cancel()):t.locked&&l.releaseLock()}return yield xt(null)}))}class Q_{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch((()=>{}))}get closed(){return this.reader?this.reader.closed.catch((()=>{})):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return Mt(this,void 0,void 0,(function*(){const{reader:e,source:n}=this;e&&(yield e.cancel(t).catch((()=>{}))),n&&n.locked&&this.releaseLock()}))}read(t){return Mt(this,void 0,void 0,(function*(){if(0===t)return{done:null==this.reader,value:new Uint8Array(0)};const e=yield this.reader.read();return!e.done&&(e.value=Gt(e)),e}))}}const Za=(t,e)=>{const n=t=>r([e,t]);let r;return[e,n,new Promise((i=>(r=i)&&t.once(e,n)))]};function G_(t){return $n(this,arguments,(function*(){const e=[];let n,r,i,o="error",s=!1,a=null,l=0,c=[];if(({cmd:n,size:r}=(yield yield xt(null))||{cmd:"read",size:0}),t.isTTY)return yield yield xt(new Uint8Array(0)),yield xt(null);try{e[0]=Za(t,"end"),e[1]=Za(t,"error");do{if(e[2]=Za(t,"readable"),[o,a]=yield xt(Promise.race(e.map((t=>t[2])))),"error"===o)break;if((s="end"===o)||(Number.isFinite(r-l)?(i=Gt(t.read(r-l)),i.byteLength<r-l&&(i=Gt(t.read()))):i=Gt(t.read()),i.byteLength>0&&(c.push(i),l+=i.byteLength)),s||r<=l)do{({cmd:n,size:r}=yield yield xt("peek"===n?rr(c,r)[0]:([i,c,l]=rr(c,r),i)))}while(r<l)}while(!s)}finally{yield xt((u=e,d="error"===o?a:null,i=c=null,new Promise(((e,n)=>{for(const[e,n]of u)t.off(e,n);try{const e=t.destroy;e&&e.call(t,d),d=void 0}catch(t){d=t||d}finally{null!=d?n(d):e()}}))))}var u,d;return yield xt(null)}))}var Fe,dn,Je,Tn,yt,ir;!function(t){t[t.V1=0]="V1",t[t.V2=1]="V2",t[t.V3=2]="V3",t[t.V4=3]="V4",t[t.V5=4]="V5"}(Fe||(Fe={})),function(t){t[t.Sparse=0]="Sparse",t[t.Dense=1]="Dense"}(dn||(dn={})),function(t){t[t.HALF=0]="HALF",t[t.SINGLE=1]="SINGLE",t[t.DOUBLE=2]="DOUBLE"}(Je||(Je={})),function(t){t[t.DAY=0]="DAY",t[t.MILLISECOND=1]="MILLISECOND"}(Tn||(Tn={})),function(t){t[t.SECOND=0]="SECOND",t[t.MILLISECOND=1]="MILLISECOND",t[t.MICROSECOND=2]="MICROSECOND",t[t.NANOSECOND=3]="NANOSECOND"}(yt||(yt={})),function(t){t[t.YEAR_MONTH=0]="YEAR_MONTH",t[t.DAY_TIME=1]="DAY_TIME",t[t.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(ir||(ir={}));const $a=2,Gn=4,dr=4,ae=4,Mr=new Int32Array(2),nu=new Float32Array(Mr.buffer),ru=new Float64Array(Mr.buffer),Ss=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0];var Al;!function(t){t[t.UTF8_BYTES=1]="UTF8_BYTES",t[t.UTF16_STRING=2]="UTF16_STRING"}(Al||(Al={}));let Li=class t{constructor(t){this.bytes_=t,this.position_=0,this.text_decoder_=new TextDecoder}static allocate(e){return new t(new Uint8Array(e))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return BigInt.asIntN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readUint64(t){return BigInt.asUintN(64,BigInt(this.readUint32(t))+(BigInt(this.readUint32(t+4))<<BigInt(32)))}readFloat32(t){return Mr[0]=this.readInt32(t),nu[0]}readFloat64(t){return Mr[Ss?0:1]=this.readInt32(t),Mr[Ss?1:0]=this.readInt32(t+4),ru[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,Number(BigInt.asIntN(32,e))),this.writeInt32(t+4,Number(BigInt.asIntN(32,e>>BigInt(32))))}writeUint64(t,e){this.writeUint32(t,Number(BigInt.asUintN(32,e))),this.writeUint32(t+4,Number(BigInt.asUintN(32,e>>BigInt(32))))}writeFloat32(t,e){nu[0]=e,this.writeInt32(t,Mr[0])}writeFloat64(t,e){ru[0]=e,this.writeInt32(t,Mr[Ss?0:1]),this.writeInt32(t+4,Mr[Ss?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+4+4)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<4;e++)t+=String.fromCharCode(this.readInt8(this.position_+4+e));return t}__offset(t,e){const n=t-this.readInt32(t);return e<this.readInt16(n)?this.readInt16(n+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);const n=this.readInt32(t);t+=4;const r=this.bytes_.subarray(t,t+n);return e===Al.UTF8_BYTES?r:this.text_decoder_.decode(r)}__union_with_string(t,e){return"string"==typeof t?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+4}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(4!=t.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let e=0;e<4;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+4+e))return!1;return!0}createScalarList(t,e){const n=[];for(let r=0;r<e;++r){const e=t(r);null!==e&&n.push(e)}return n}createObjList(t,e){const n=[];for(let r=0;r<e;++r){const e=t(r);null!==e&&n.push(e.unpack())}return n}},Lh=class t{constructor(t){let e;this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,this.text_encoder=new TextEncoder,e=t||1024,this.bb=Li.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(e,n){e>this.minalign&&(this.minalign=e);const r=1+~(this.bb.capacity()-this.space+n)&e-1;for(;this.space<r+e+n;){const e=this.bb.capacity();this.bb=t.growByteBuffer(this.bb),this.space+=this.bb.capacity()-e}this.pad(r)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,n){(this.force_defaults||e!=n)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,n){(this.force_defaults||e!=n)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,n){(this.force_defaults||e!=n)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,n){(this.force_defaults||e!==n)&&(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,n){(this.force_defaults||e!=n)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,n){e!=n&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new TypeError("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new TypeError("FlatBuffers: object serialization must not be nested.")}slot(t){null!==this.vtable&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const e=t.capacity();if(3221225472&e)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const n=e<<1,r=Li.allocate(n);return r.setPosition(n-e),r.bytes().set(t.bytes(),n-e),r}addOffset(t){this.prep(4,0),this.writeInt32(this.offset()-t+4)}startObject(t){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let e=this.vtable_in_use-1;for(;e>=0&&0==this.vtable[e];e--);const n=e+1;for(;e>=0;e--)this.addInt16(0!=this.vtable[e]?t-this.vtable[e]:0);this.addInt16(t-this.object_start);const r=2*(n+2);this.addInt16(r);let i=0;const o=this.space;t:for(e=0;e<this.vtables.length;e++){const t=this.bb.capacity()-this.vtables[e];if(r==this.bb.readInt16(t)){for(let e=2;e<r;e+=2)if(this.bb.readInt16(o+e)!=this.bb.readInt16(t+e))continue t;i=this.vtables[e];break}}return i?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,i-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,n){const r=n?4:0;if(e){const t=e;if(this.prep(this.minalign,8+r),4!=t.length)throw new TypeError("FlatBuffers: file identifier must be length 4");for(let e=3;e>=0;e--)this.writeInt8(t.charCodeAt(e))}this.prep(this.minalign,4+r),this.addOffset(t),r&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){const n=this.bb.capacity()-t,r=n-this.bb.readInt32(n);if(!(e<this.bb.readInt16(r)&&0!=this.bb.readInt16(r+e)))throw new TypeError("FlatBuffers: field "+e+" must be set")}startVector(t,e,n){this.notNested(),this.vector_num_elems=e,this.prep(4,t*e),this.prep(n,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(null==t)return 0;let e;return e=t instanceof Uint8Array?t:this.text_encoder.encode(t),this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length),this.bb.bytes().set(e,this.space),this.endVector()}createByteVector(t){return null==t?0:(this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length),this.bb.bytes().set(t,this.space),this.endVector())}createObjectOffset(t){return null===t?0:"string"==typeof t?this.createString(t):t.pack(this)}createObjectOffsetList(t){const e=[];for(let n=0;n<t.length;++n){const r=t[n];if(null===r)throw new TypeError("FlatBuffers: Argument for createObjectOffsetList cannot contain null.");e.push(this.createObjectOffset(r))}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t.slice().reverse()),this.endVector()}};var Zs,$s;!function(t){t[t.BUFFER=0]="BUFFER"}(Zs||(Zs={})),function(t){t[t.LZ4_FRAME=0]="LZ4_FRAME",t[t.ZSTD=1]="ZSTD"}($s||($s={}));class Nr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBodyCompression(t,e){return(e||new Nr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,e){return t.setPosition(t.position()+4),(e||new Nr).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):$s.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):Zs.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,e){t.addFieldInt8(0,e,$s.LZ4_FRAME)}static addMethod(t,e){t.addFieldInt8(1,e,Zs.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,e,n){return Nr.startBodyCompression(t),Nr.addCodec(t,e),Nr.addMethod(t,n),Nr.endBodyCompression(t)}}class Uh{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,e,n){return t.prep(8,16),t.writeInt64(BigInt(n??0)),t.writeInt64(BigInt(e??0)),t.offset()}}let Vh=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,e,n){return t.prep(8,16),t.writeInt64(BigInt(n??0)),t.writeInt64(BigInt(e??0)),t.offset()}},ur=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsRecordBatch(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}nodes(t,e){const n=this.bb.__offset(this.bb_pos,6);return n?(e||new Vh).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Uh).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const e=this.bb.__offset(this.bb_pos,10);return e?(t||new Nr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static addCompression(t,e){t.addFieldOffset(3,e,0)}static endRecordBatch(t){return t.endObject()}},_i=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDictionaryBatch(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}data(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new ur).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){return t.endObject()}};var Ri,ta;!function(t){t[t.Little=0]="Little",t[t.Big=1]="Big"}(Ri||(Ri={})),function(t){t[t.DenseArray=0]="DenseArray"}(ta||(ta={}));class gn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,e){return(e||new gn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,e){return t.setPosition(t.position()+4),(e||new gn).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){return t.endObject()}static createInt(t,e,n){return gn.startInt(t),gn.addBitWidth(t,e),gn.addIsSigned(t,n),gn.endInt(t)}}class hr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new hr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,e){return t.setPosition(t.position()+4),(e||new hr).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}indexType(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new gn).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):ta.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,e){t.addFieldInt64(0,e,BigInt("0"))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static addDictionaryKind(t,e){t.addFieldInt16(3,e,ta.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}class Le{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,e){return(e||new Le).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,e){return t.setPosition(t.position()+4),(e||new Le).__init(t.readInt32(t.position())+t.position(),t)}key(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,e,n){return Le.startKeyValue(t),Le.addKey(t,e),Le.addValue(t,n),Le.endKeyValue(t)}}let iu=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBinary(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(e){return t.startBinary(e),t.endBinary(e)}},ou=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsBool(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(e){return t.startBool(e),t.endBool(e)}},Rs=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDate(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Tn.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,Tn.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(e,n){return t.startDate(e),t.addUnit(e,n),t.endDate(e)}},yi=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDecimal(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static addBitWidth(t,e){t.addFieldInt32(2,e,128)}static endDecimal(t){return t.endObject()}static createDecimal(e,n,r,i){return t.startDecimal(e),t.addPrecision(e,n),t.addScale(e,r),t.addBitWidth(e,i),t.endDecimal(e)}},Us=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDuration(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsDuration(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.MILLISECOND}static startDuration(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,yt.MILLISECOND)}static endDuration(t){return t.endObject()}static createDuration(e,n){return t.startDuration(e),t.addUnit(e,n),t.endDuration(e)}},Vs=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFixedSizeBinary(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(e,n){return t.startFixedSizeBinary(e),t.addByteWidth(e,n),t.endFixedSizeBinary(e)}},zs=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsFixedSizeList(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(e,n){return t.startFixedSizeList(e),t.addListSize(e,n),t.endFixedSizeList(e)}};class Xn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new Xn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,e){return t.setPosition(t.position()+4),(e||new Xn).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Je.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,e){t.addFieldInt16(0,e,Je.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,e){return Xn.startFloatingPoint(t),Xn.addPrecision(t,e),Xn.endFloatingPoint(t)}}class Zn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new Zn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,e){return t.setPosition(t.position()+4),(e||new Zn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):ir.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,ir.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,e){return Zn.startInterval(t),Zn.addUnit(t,e),Zn.endInterval(t)}}let su=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeBinary(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsLargeBinary(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startLargeBinary(t){t.startObject(0)}static endLargeBinary(t){return t.endObject()}static createLargeBinary(e){return t.startLargeBinary(e),t.endLargeBinary(e)}},au=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsLargeUtf8(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsLargeUtf8(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startLargeUtf8(t){t.startObject(0)}static endLargeUtf8(t){return t.endObject()}static createLargeUtf8(e){return t.startLargeUtf8(e),t.endLargeUtf8(e)}},lu=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsList(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(e){return t.startList(e),t.endList(e)}},Ws=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsMap(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){return t.endObject()}static createMap(e,n){return t.startMap(e),t.addKeysSorted(e,n),t.endMap(e)}},cu=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsNull(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(e){return t.startNull(e),t.endNull(e)}};class ri{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,e){return(e||new ri).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,e){return t.setPosition(t.position()+4),(e||new ri).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return ri.startStruct_(t),ri.endStruct_(t)}}class Sn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new Sn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,e){return t.setPosition(t.position()+4),(e||new Sn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,yt.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){return t.endObject()}static createTime(t,e,n){return Sn.startTime(t),Sn.addUnit(t,e),Sn.addBitWidth(t,n),Sn.endTime(t)}}class In{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new In).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,e){return t.setPosition(t.position()+4),(e||new In).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yt.SECOND}timezone(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,yt.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,e,n){return In.startTimestamp(t),In.addUnit(t,e),In.addTimezone(t,n),In.endTimestamp(t)}}class un{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new un).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,e){return t.setPosition(t.position()+4),(e||new un).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):dn.Sparse}typeIds(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+4*t):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,e){t.addFieldInt16(0,e,dn.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addInt32(e[n]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){return t.endObject()}static createUnion(t,e,n){return un.startUnion(t),un.addMode(t,e),un.addTypeIds(t,n),un.endUnion(t)}}let uu=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsUtf8(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(e){return t.startUtf8(e),t.endUtf8(e)}};var pe;!function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.FloatingPoint=3]="FloatingPoint",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct_=13]="Struct_",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Duration=18]="Duration",t[t.LargeBinary=19]="LargeBinary",t[t.LargeUtf8=20]="LargeUtf8",t[t.LargeList=21]="LargeList",t[t.RunEndEncoded=22]="RunEndEncoded"}(pe||(pe={}));let vn=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsField(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}name(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):pe.NONE}type(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(t){const e=this.bb.__offset(this.bb_pos,12);return e?(t||new hr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}children(e,n){const r=this.bb.__offset(this.bb_pos,14);return r?(n||new t).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*e),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,16);return n?(e||new Le).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(t,e){t.addFieldInt8(2,e,pe.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){return t.endObject()}},Jn=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsSchema(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ri.Little}fields(t,e){const n=this.bb.__offset(this.bb_pos,6);return n?(e||new vn).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Le).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+8*t):BigInt(0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,e){t.addFieldInt16(0,e,Ri.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static addFeatures(t,e){t.addFieldOffset(3,e,0)}static createFeaturesVector(t,e){t.startVector(8,e.length,8);for(let n=e.length-1;n>=0;n--)t.addInt64(e[n]);return t.endVector()}static startFeaturesVector(t,e){t.startVector(8,e,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,e){t.finish(e)}static finishSizePrefixedSchemaBuffer(t,e){t.finish(e,void 0,!0)}static createSchema(e,n,r,i,o){return t.startSchema(e),t.addEndianness(e,n),t.addFields(e,r),t.addCustomMetadata(e,i),t.addFeatures(e,o),t.endSchema(e)}};var oe;!function(t){t[t.NONE=0]="NONE",t[t.Schema=1]="Schema",t[t.DictionaryBatch=2]="DictionaryBatch",t[t.RecordBatch=3]="RecordBatch",t[t.Tensor=4]="Tensor",t[t.SparseTensor=5]="SparseTensor"}(oe||(oe={}));const X_=void 0;function Yo(t){if(null===t)return"null";if(t===X_)return"undefined";switch(typeof t){case"number":case"bigint":return`${t}`;case"string":return`"${t}"`}return"function"==typeof t[Symbol.toPrimitive]?t[Symbol.toPrimitive]("string"):ArrayBuffer.isView(t)?t instanceof BigInt64Array||t instanceof BigUint64Array?`[${[...t].map((t=>Yo(t)))}]`:`[${t}]`:ArrayBuffer.isView(t)?`[${t}]`:JSON.stringify(t,((t,e)=>"bigint"==typeof e?`${e}`:e))}function Se(t){if("bigint"==typeof t&&(t<Number.MIN_SAFE_INTEGER||t>Number.MAX_SAFE_INTEGER))throw new TypeError(`${t} is not safe to convert to a number.`);return Number(t)}function zh(t,e){return Se(t/e)+Se(t%e)/Se(e)}const Z_=Symbol.for("isArrowBigNum");function Wn(t,...e){return 0===e.length?Object.setPrototypeOf(fe(this.TypedArray,t),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(t,...e),this.constructor.prototype)}function Pi(...t){return Wn.apply(this,t)}function Mi(...t){return Wn.apply(this,t)}function Ko(...t){return Wn.apply(this,t)}Wn.prototype[Z_]=!0,Wn.prototype.toJSON=function(){return`"${Jo(this)}"`},Wn.prototype.valueOf=function(t){return Wh(this,t)},Wn.prototype.toString=function(){return Jo(this)},Wn.prototype[Symbol.toPrimitive]=function(t="default"){switch(t){case"number":return Wh(this);case"string":return Jo(this);case"default":return eb(this)}return Jo(this)},Object.setPrototypeOf(Pi.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf(Mi.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(Ko.prototype,Object.create(Uint32Array.prototype)),Object.assign(Pi.prototype,Wn.prototype,{constructor:Pi,signed:!0,TypedArray:Int32Array,BigIntArray:BigInt64Array}),Object.assign(Mi.prototype,Wn.prototype,{constructor:Mi,signed:!1,TypedArray:Uint32Array,BigIntArray:BigUint64Array}),Object.assign(Ko.prototype,Wn.prototype,{constructor:Ko,signed:!0,TypedArray:Uint32Array,BigIntArray:BigUint64Array});const $_=BigInt(4294967296)*BigInt(4294967296),tb=$_-BigInt(1);function Wh(t,e){const{buffer:n,byteOffset:r,byteLength:i,signed:o}=t,s=new BigUint64Array(n,r,i/8),a=o&&s.at(-1)&BigInt(1)<<BigInt(63);let l=BigInt(0),c=0;if(a){for(const t of s)l|=(t^tb)*(BigInt(1)<<BigInt(64*c++));l*=BigInt(-1),l-=BigInt(1)}else for(const t of s)l|=t*(BigInt(1)<<BigInt(64*c++));if("number"==typeof e){const t=BigInt(Math.pow(10,e)),n=l%t;return Se(l/t)+Se(n)/Se(t)}return Se(l)}function Jo(t){if(8===t.byteLength)return`${new t.BigIntArray(t.buffer,t.byteOffset,1)[0]}`;if(!t.signed)return tl(t);let e=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2);if(new Int16Array([e.at(-1)])[0]>=0)return tl(t);e=e.slice();let n=1;for(let t=0;t<e.length;t++){const r=e[t],i=~r+n;e[t]=i,n&=0===r?1:0}return`-${tl(e)}`}function eb(t){return 8===t.byteLength?new t.BigIntArray(t.buffer,t.byteOffset,1)[0]:Jo(t)}function tl(t){let e="";const n=new Uint32Array(2);let r=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2);const i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer);let o=-1;const s=r.length-1;do{for(n[0]=r[o=0];o<s;)r[o++]=n[1]=n[0]/10,n[0]=(n[0]-10*n[1]<<16)+r[o];r[o]=n[1]=n[0]/10,n[0]=n[0]-10*n[1],e=`${n[0]}${e}`}while(i[0]||i[1]||i[2]||i[3]);return e??"0"}class oc{static new(t,e){switch(e){case!0:return new Pi(t);case!1:return new Mi(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case BigInt64Array:return new Pi(t)}return 16===t.byteLength?new Ko(t):new Mi(t)}static signed(t){return new Pi(t)}static unsigned(t){return new Mi(t)}static decimal(t){return new Ko(t)}constructor(t,e){return oc.new(t,e)}}var Hh,xh,qh,Yh,Kh,Jh,Qh,Gh,Xh,Zh,$h,tm,em,nm,rm,im,om,sm,am,lm,cm,um;class ft{static isNull(t){return(null==t?void 0:t.typeId)===A.Null}static isInt(t){return(null==t?void 0:t.typeId)===A.Int}static isFloat(t){return(null==t?void 0:t.typeId)===A.Float}static isBinary(t){return(null==t?void 0:t.typeId)===A.Binary}static isLargeBinary(t){return(null==t?void 0:t.typeId)===A.LargeBinary}static isUtf8(t){return(null==t?void 0:t.typeId)===A.Utf8}static isLargeUtf8(t){return(null==t?void 0:t.typeId)===A.LargeUtf8}static isBool(t){return(null==t?void 0:t.typeId)===A.Bool}static isDecimal(t){return(null==t?void 0:t.typeId)===A.Decimal}static isDate(t){return(null==t?void 0:t.typeId)===A.Date}static isTime(t){return(null==t?void 0:t.typeId)===A.Time}static isTimestamp(t){return(null==t?void 0:t.typeId)===A.Timestamp}static isInterval(t){return(null==t?void 0:t.typeId)===A.Interval}static isDuration(t){return(null==t?void 0:t.typeId)===A.Duration}static isList(t){return(null==t?void 0:t.typeId)===A.List}static isStruct(t){return(null==t?void 0:t.typeId)===A.Struct}static isUnion(t){return(null==t?void 0:t.typeId)===A.Union}static isFixedSizeBinary(t){return(null==t?void 0:t.typeId)===A.FixedSizeBinary}static isFixedSizeList(t){return(null==t?void 0:t.typeId)===A.FixedSizeList}static isMap(t){return(null==t?void 0:t.typeId)===A.Map}static isDictionary(t){return(null==t?void 0:t.typeId)===A.Dictionary}static isDenseUnion(t){return ft.isUnion(t)&&t.mode===dn.Dense}static isSparseUnion(t){return ft.isUnion(t)&&t.mode===dn.Sparse}constructor(t){this.typeId=t}}Hh=Symbol.toStringTag,ft[Hh]=(t=>(t.children=null,t.ArrayType=Array,t.OffsetArrayType=Int32Array,t[Symbol.toStringTag]="DataType"))(ft.prototype);class Lr extends ft{constructor(){super(A.Null)}toString(){return"Null"}}xh=Symbol.toStringTag,Lr[xh]=Lr.prototype[Symbol.toStringTag]="Null";class oi extends ft{constructor(t,e){super(A.Int),this.isSigned=t,this.bitWidth=e}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?BigInt64Array:BigUint64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}qh=Symbol.toStringTag,oi[qh]=(t=>(t.isSigned=null,t.bitWidth=null,t[Symbol.toStringTag]="Int"))(oi.prototype);class Qo extends oi{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(Qo.prototype,"ArrayType",{value:Int32Array});class ea extends ft{constructor(t){super(A.Float),this.precision=t}get ArrayType(){switch(this.precision){case Je.HALF:return Uint16Array;case Je.SINGLE:return Float32Array;case Je.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`Float${this.precision<<5||16}`}}Yh=Symbol.toStringTag,ea[Yh]=(t=>(t.precision=null,t[Symbol.toStringTag]="Float"))(ea.prototype);class na extends ft{constructor(){super(A.Binary)}toString(){return"Binary"}}Kh=Symbol.toStringTag,na[Kh]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Binary"))(na.prototype);class ra extends ft{constructor(){super(A.LargeBinary)}toString(){return"LargeBinary"}}Jh=Symbol.toStringTag,ra[Jh]=(t=>(t.ArrayType=Uint8Array,t.OffsetArrayType=BigInt64Array,t[Symbol.toStringTag]="LargeBinary"))(ra.prototype);class ia extends ft{constructor(){super(A.Utf8)}toString(){return"Utf8"}}Qh=Symbol.toStringTag,ia[Qh]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Utf8"))(ia.prototype);class oa extends ft{constructor(){super(A.LargeUtf8)}toString(){return"LargeUtf8"}}Gh=Symbol.toStringTag,oa[Gh]=(t=>(t.ArrayType=Uint8Array,t.OffsetArrayType=BigInt64Array,t[Symbol.toStringTag]="LargeUtf8"))(oa.prototype);class sa extends ft{constructor(){super(A.Bool)}toString(){return"Bool"}}Xh=Symbol.toStringTag,sa[Xh]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Bool"))(sa.prototype);class aa extends ft{constructor(t,e,n=128){super(A.Decimal),this.scale=t,this.precision=e,this.bitWidth=n}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}}Zh=Symbol.toStringTag,aa[Zh]=(t=>(t.scale=null,t.precision=null,t.ArrayType=Uint32Array,t[Symbol.toStringTag]="Decimal"))(aa.prototype);class la extends ft{constructor(t){super(A.Date),this.unit=t}toString(){return`Date${32*(this.unit+1)}<${Tn[this.unit]}>`}get ArrayType(){return this.unit===Tn.DAY?Int32Array:BigInt64Array}}$h=Symbol.toStringTag,la[$h]=(t=>(t.unit=null,t[Symbol.toStringTag]="Date"))(la.prototype);class ca extends ft{constructor(t,e){super(A.Time),this.unit=t,this.bitWidth=e}toString(){return`Time${this.bitWidth}<${yt[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return BigInt64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}}tm=Symbol.toStringTag,ca[tm]=(t=>(t.unit=null,t.bitWidth=null,t[Symbol.toStringTag]="Time"))(ca.prototype);class ua extends ft{constructor(t,e){super(A.Timestamp),this.unit=t,this.timezone=e}toString(){return`Timestamp<${yt[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}em=Symbol.toStringTag,ua[em]=(t=>(t.unit=null,t.timezone=null,t.ArrayType=BigInt64Array,t[Symbol.toStringTag]="Timestamp"))(ua.prototype);class fa extends ft{constructor(t){super(A.Interval),this.unit=t}toString(){return`Interval<${ir[this.unit]}>`}}nm=Symbol.toStringTag,fa[nm]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Interval"))(fa.prototype);class da extends ft{constructor(t){super(A.Duration),this.unit=t}toString(){return`Duration<${yt[this.unit]}>`}}rm=Symbol.toStringTag,da[rm]=(t=>(t.unit=null,t.ArrayType=BigInt64Array,t[Symbol.toStringTag]="Duration"))(da.prototype);class ha extends ft{constructor(t){super(A.List),this.children=[t]}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}im=Symbol.toStringTag,ha[im]=(t=>(t.children=null,t[Symbol.toStringTag]="List"))(ha.prototype);class nn extends ft{constructor(t){super(A.Struct),this.children=t}toString(){return`Struct<{${this.children.map((t=>`${t.name}:${t.type}`)).join(", ")}}>`}}om=Symbol.toStringTag,nn[om]=(t=>(t.children=null,t[Symbol.toStringTag]="Struct"))(nn.prototype);class ma extends ft{constructor(t,e,n){super(A.Union),this.mode=t,this.children=n,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce(((t,e,n)=>(t[e]=n)&&t||t),Object.create(null))}toString(){return`${this[Symbol.toStringTag]}<${this.children.map((t=>`${t.type}`)).join(" | ")}>`}}sm=Symbol.toStringTag,ma[sm]=(t=>(t.mode=null,t.typeIds=null,t.children=null,t.typeIdToChildIndex=null,t.ArrayType=Int8Array,t[Symbol.toStringTag]="Union"))(ma.prototype);class pa extends ft{constructor(t){super(A.FixedSizeBinary),this.byteWidth=t}toString(){return`FixedSizeBinary[${this.byteWidth}]`}}am=Symbol.toStringTag,pa[am]=(t=>(t.byteWidth=null,t.ArrayType=Uint8Array,t[Symbol.toStringTag]="FixedSizeBinary"))(pa.prototype);class ga extends ft{constructor(t,e){super(A.FixedSizeList),this.listSize=t,this.children=[e]}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}}lm=Symbol.toStringTag,ga[lm]=(t=>(t.children=null,t.listSize=null,t[Symbol.toStringTag]="FixedSizeList"))(ga.prototype);class _a extends ft{constructor(t,e=!1){var n,r,i;if(super(A.Map),this.children=[t],this.keysSorted=e,t&&(t.name="entries",null!==(n=null==t?void 0:t.type)&&void 0!==n&&n.children)){const e=null===(r=null==t?void 0:t.type)||void 0===r?void 0:r.children[0];e&&(e.name="key");const n=null===(i=null==t?void 0:t.type)||void 0===i?void 0:i.children[1];n&&(n.name="value")}}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map((t=>`${t.name}:${t.type}`)).join(", ")}}>`}}cm=Symbol.toStringTag,_a[cm]=(t=>(t.children=null,t.keysSorted=null,t[Symbol.toStringTag]="Map_"))(_a.prototype);const nb=(t=>()=>++t)(-1);class Ui extends ft{constructor(t,e,n,r){super(A.Dictionary),this.indices=e,this.dictionary=t,this.isOrdered=r||!1,this.id=null==n?nb():Se(n)}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}function fr(t){const e=t;switch(t.typeId){case A.Decimal:return t.bitWidth/32;case A.Interval:return 1+e.unit;case A.FixedSizeList:return e.listSize;case A.FixedSizeBinary:return e.byteWidth;default:return 1}}um=Symbol.toStringTag,Ui[um]=(t=>(t.id=null,t.indices=null,t.isOrdered=null,t.dictionary=null,t[Symbol.toStringTag]="Dictionary"))(Ui.prototype);class Ht{visitMany(t,...e){return t.map(((t,n)=>this.visit(t,...e.map((t=>t[n])))))}visit(...t){return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(t,e=!0){return rb(this,t,e)}getVisitFnByTypeId(t,e=!0){return Oi(this,t,e)}visitNull(t,...e){return null}visitBool(t,...e){return null}visitInt(t,...e){return null}visitFloat(t,...e){return null}visitUtf8(t,...e){return null}visitLargeUtf8(t,...e){return null}visitBinary(t,...e){return null}visitLargeBinary(t,...e){return null}visitFixedSizeBinary(t,...e){return null}visitDate(t,...e){return null}visitTimestamp(t,...e){return null}visitTime(t,...e){return null}visitDecimal(t,...e){return null}visitList(t,...e){return null}visitStruct(t,...e){return null}visitUnion(t,...e){return null}visitDictionary(t,...e){return null}visitInterval(t,...e){return null}visitDuration(t,...e){return null}visitFixedSizeList(t,...e){return null}visitMap(t,...e){return null}}function rb(t,e,n=!0){return"number"==typeof e?Oi(t,e,n):"string"==typeof e&&e in A?Oi(t,A[e],n):e&&e instanceof ft?Oi(t,fu(e),n):null!=e&&e.type&&e.type instanceof ft?Oi(t,fu(e.type),n):Oi(t,A.NONE,n)}function Oi(t,e,n=!0){let r=null;switch(e){case A.Null:r=t.visitNull;break;case A.Bool:r=t.visitBool;break;case A.Int:r=t.visitInt;break;case A.Int8:r=t.visitInt8||t.visitInt;break;case A.Int16:r=t.visitInt16||t.visitInt;break;case A.Int32:r=t.visitInt32||t.visitInt;break;case A.Int64:r=t.visitInt64||t.visitInt;break;case A.Uint8:r=t.visitUint8||t.visitInt;break;case A.Uint16:r=t.visitUint16||t.visitInt;break;case A.Uint32:r=t.visitUint32||t.visitInt;break;case A.Uint64:r=t.visitUint64||t.visitInt;break;case A.Float:r=t.visitFloat;break;case A.Float16:r=t.visitFloat16||t.visitFloat;break;case A.Float32:r=t.visitFloat32||t.visitFloat;break;case A.Float64:r=t.visitFloat64||t.visitFloat;break;case A.Utf8:r=t.visitUtf8;break;case A.LargeUtf8:r=t.visitLargeUtf8;break;case A.Binary:r=t.visitBinary;break;case A.LargeBinary:r=t.visitLargeBinary;break;case A.FixedSizeBinary:r=t.visitFixedSizeBinary;break;case A.Date:r=t.visitDate;break;case A.DateDay:r=t.visitDateDay||t.visitDate;break;case A.DateMillisecond:r=t.visitDateMillisecond||t.visitDate;break;case A.Timestamp:r=t.visitTimestamp;break;case A.TimestampSecond:r=t.visitTimestampSecond||t.visitTimestamp;break;case A.TimestampMillisecond:r=t.visitTimestampMillisecond||t.visitTimestamp;break;case A.TimestampMicrosecond:r=t.visitTimestampMicrosecond||t.visitTimestamp;break;case A.TimestampNanosecond:r=t.visitTimestampNanosecond||t.visitTimestamp;break;case A.Time:r=t.visitTime;break;case A.TimeSecond:r=t.visitTimeSecond||t.visitTime;break;case A.TimeMillisecond:r=t.visitTimeMillisecond||t.visitTime;break;case A.TimeMicrosecond:r=t.visitTimeMicrosecond||t.visitTime;break;case A.TimeNanosecond:r=t.visitTimeNanosecond||t.visitTime;break;case A.Decimal:r=t.visitDecimal;break;case A.List:r=t.visitList;break;case A.Struct:r=t.visitStruct;break;case A.Union:r=t.visitUnion;break;case A.DenseUnion:r=t.visitDenseUnion||t.visitUnion;break;case A.SparseUnion:r=t.visitSparseUnion||t.visitUnion;break;case A.Dictionary:r=t.visitDictionary;break;case A.Interval:r=t.visitInterval;break;case A.IntervalDayTime:r=t.visitIntervalDayTime||t.visitInterval;break;case A.IntervalYearMonth:r=t.visitIntervalYearMonth||t.visitInterval;break;case A.Duration:r=t.visitDuration;break;case A.DurationSecond:r=t.visitDurationSecond||t.visitDuration;break;case A.DurationMillisecond:r=t.visitDurationMillisecond||t.visitDuration;break;case A.DurationMicrosecond:r=t.visitDurationMicrosecond||t.visitDuration;break;case A.DurationNanosecond:r=t.visitDurationNanosecond||t.visitDuration;break;case A.FixedSizeList:r=t.visitFixedSizeList;break;case A.Map:r=t.visitMap}if("function"==typeof r)return r;if(!n)return()=>null;throw new Error(`Unrecognized type '${A[e]}'`)}function fu(t){switch(t.typeId){case A.Null:return A.Null;case A.Int:{const{bitWidth:e,isSigned:n}=t;switch(e){case 8:return n?A.Int8:A.Uint8;case 16:return n?A.Int16:A.Uint16;case 32:return n?A.Int32:A.Uint32;case 64:return n?A.Int64:A.Uint64}return A.Int}case A.Float:switch(t.precision){case Je.HALF:return A.Float16;case Je.SINGLE:return A.Float32;case Je.DOUBLE:return A.Float64}return A.Float;case A.Binary:return A.Binary;case A.LargeBinary:return A.LargeBinary;case A.Utf8:return A.Utf8;case A.LargeUtf8:return A.LargeUtf8;case A.Bool:return A.Bool;case A.Decimal:return A.Decimal;case A.Time:switch(t.unit){case yt.SECOND:return A.TimeSecond;case yt.MILLISECOND:return A.TimeMillisecond;case yt.MICROSECOND:return A.TimeMicrosecond;case yt.NANOSECOND:return A.TimeNanosecond}return A.Time;case A.Timestamp:switch(t.unit){case yt.SECOND:return A.TimestampSecond;case yt.MILLISECOND:return A.TimestampMillisecond;case yt.MICROSECOND:return A.TimestampMicrosecond;case yt.NANOSECOND:return A.TimestampNanosecond}return A.Timestamp;case A.Date:switch(t.unit){case Tn.DAY:return A.DateDay;case Tn.MILLISECOND:return A.DateMillisecond}return A.Date;case A.Interval:switch(t.unit){case ir.DAY_TIME:return A.IntervalDayTime;case ir.YEAR_MONTH:return A.IntervalYearMonth}return A.Interval;case A.Duration:switch(t.unit){case yt.SECOND:return A.DurationSecond;case yt.MILLISECOND:return A.DurationMillisecond;case yt.MICROSECOND:return A.DurationMicrosecond;case yt.NANOSECOND:return A.DurationNanosecond}return A.Duration;case A.Map:return A.Map;case A.List:return A.List;case A.Struct:return A.Struct;case A.Union:switch(t.mode){case dn.Dense:return A.DenseUnion;case dn.Sparse:return A.SparseUnion}return A.Union;case A.FixedSizeBinary:return A.FixedSizeBinary;case A.FixedSizeList:return A.FixedSizeList;case A.Dictionary:return A.Dictionary}throw new Error(`Unrecognized type '${A[t.typeId]}'`)}Ht.prototype.visitInt8=null,Ht.prototype.visitInt16=null,Ht.prototype.visitInt32=null,Ht.prototype.visitInt64=null,Ht.prototype.visitUint8=null,Ht.prototype.visitUint16=null,Ht.prototype.visitUint32=null,Ht.prototype.visitUint64=null,Ht.prototype.visitFloat16=null,Ht.prototype.visitFloat32=null,Ht.prototype.visitFloat64=null,Ht.prototype.visitDateDay=null,Ht.prototype.visitDateMillisecond=null,Ht.prototype.visitTimestampSecond=null,Ht.prototype.visitTimestampMillisecond=null,Ht.prototype.visitTimestampMicrosecond=null,Ht.prototype.visitTimestampNanosecond=null,Ht.prototype.visitTimeSecond=null,Ht.prototype.visitTimeMillisecond=null,Ht.prototype.visitTimeMicrosecond=null,Ht.prototype.visitTimeNanosecond=null,Ht.prototype.visitDenseUnion=null,Ht.prototype.visitSparseUnion=null,Ht.prototype.visitIntervalDayTime=null,Ht.prototype.visitIntervalYearMonth=null,Ht.prototype.visitDuration=null,Ht.prototype.visitDurationSecond=null,Ht.prototype.visitDurationMillisecond=null,Ht.prototype.visitDurationMicrosecond=null,Ht.prototype.visitDurationNanosecond=null;const fm=new Float64Array(1),ui=new Uint32Array(fm.buffer);function dm(t){const e=(31744&t)>>10,n=(1023&t)/1024,r=Math.pow(-1,(32768&t)>>15);switch(e){case 31:return r*(n?Number.NaN:1/0);case 0:return r*(n?6103515625e-14*n:0)}return r*Math.pow(2,e-15)*(1+n)}function ib(t){if(t!=t)return 32256;fm[0]=t;const e=(2147483648&ui[1])>>16&65535;let n=2146435072&ui[1],r=0;return n>=1089470464?ui[0]>0?n=31744:(n=(2080374784&n)>>16,r=(1048575&ui[1])>>10):n<=1056964608?(r=1048576+(1048575&ui[1]),r=1048576+(r<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,r=512+(1048575&ui[1])>>10),e|n|65535&r}class St extends Ht{}function kt(t){return(e,n,r)=>{if(e.setValid(n,null!=r))return t(e,n,r)}}const ob=(t,e,n)=>{t[e]=Math.floor(n/864e5)},hm=(t,e,n,r)=>{if(n+1<e.length){const i=Se(e[n]),o=Se(e[n+1]);t.set(r.subarray(0,o-i),i)}},sb=({offset:t,values:e},n,r)=>{const i=t+n;r?e[i>>3]|=1<<i%8:e[i>>3]&=~(1<<i%8)},Or=({values:t},e,n)=>{t[e]=n},sc=({values:t},e,n)=>{t[e]=n},mm=({values:t},e,n)=>{t[e]=ib(n)},ab=(t,e,n)=>{switch(t.type.precision){case Je.HALF:return mm(t,e,n);case Je.SINGLE:case Je.DOUBLE:return sc(t,e,n)}},pm=({values:t},e,n)=>{ob(t,e,n.valueOf())},gm=({values:t},e,n)=>{t[e]=BigInt(n)},lb=({stride:t,values:e},n,r)=>{e.set(r.subarray(0,t),t*n)},_m=({values:t,valueOffsets:e},n,r)=>hm(t,e,n,r),bm=({values:t,valueOffsets:e},n,r)=>hm(t,e,n,ec(r)),cb=(t,e,n)=>{t.type.unit===Tn.DAY?pm(t,e,n):gm(t,e,n)},ym=({values:t},e,n)=>{t[e]=BigInt(n/1e3)},vm=({values:t},e,n)=>{t[e]=BigInt(n)},wm=({values:t},e,n)=>{t[e]=BigInt(1e3*n)},Sm=({values:t},e,n)=>{t[e]=BigInt(1e6*n)},ub=(t,e,n)=>{switch(t.type.unit){case yt.SECOND:return ym(t,e,n);case yt.MILLISECOND:return vm(t,e,n);case yt.MICROSECOND:return wm(t,e,n);case yt.NANOSECOND:return Sm(t,e,n)}},Im=({values:t},e,n)=>{t[e]=n},Om=({values:t},e,n)=>{t[e]=n},km=({values:t},e,n)=>{t[e]=n},Dm=({values:t},e,n)=>{t[e]=n},fb=(t,e,n)=>{switch(t.type.unit){case yt.SECOND:return Im(t,e,n);case yt.MILLISECOND:return Om(t,e,n);case yt.MICROSECOND:return km(t,e,n);case yt.NANOSECOND:return Dm(t,e,n)}},db=({values:t,stride:e},n,r)=>{t.set(r.subarray(0,e),e*n)},hb=(t,e,n)=>{const r=t.children[0],i=t.valueOffsets,o=Bn.getVisitFn(r);if(Array.isArray(n))for(let t=-1,s=i[e],a=i[e+1];s<a;)o(r,s++,n[++t]);else for(let t=-1,s=i[e],a=i[e+1];s<a;)o(r,s++,n.get(++t))},mb=(t,e,n)=>{const r=t.children[0],{valueOffsets:i}=t,o=Bn.getVisitFn(r);let{[e]:s,[e+1]:a}=i;const l=n instanceof Map?n.entries():Object.entries(n);for(const t of l)if(o(r,s,t),++s>=a)break},pb=(t,e)=>(n,r,i,o)=>r&&n(r,t,e[o]),gb=(t,e)=>(n,r,i,o)=>r&&n(r,t,e.get(o)),_b=(t,e)=>(n,r,i,o)=>r&&n(r,t,e.get(i.name)),bb=(t,e)=>(n,r,i,o)=>r&&n(r,t,e[i.name]),yb=(t,e,n)=>{const r=t.type.children.map((t=>Bn.getVisitFn(t.type))),i=n instanceof Map?_b(e,n):n instanceof de?gb(e,n):Array.isArray(n)?pb(e,n):bb(e,n);t.type.children.forEach(((e,n)=>i(r[n],t.children[n],e,n)))},vb=(t,e,n)=>{t.type.mode===dn.Dense?Em(t,e,n):Am(t,e,n)},Em=(t,e,n)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],i=t.children[r];Bn.visit(i,t.valueOffsets[e],n)},Am=(t,e,n)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],i=t.children[r];Bn.visit(i,e,n)},wb=(t,e,n)=>{var r;null===(r=t.dictionary)||void 0===r||r.set(t.values[e],n)},Sb=(t,e,n)=>{t.type.unit===ir.DAY_TIME?Tm(t,e,n):Bm(t,e,n)},Tm=({values:t},e,n)=>{t.set(n.subarray(0,2),2*e)},Bm=({values:t},e,n)=>{t[e]=12*n[0]+n[1]%12},Pm=({values:t},e,n)=>{t[e]=n},Mm=({values:t},e,n)=>{t[e]=n},Nm=({values:t},e,n)=>{t[e]=n},Cm=({values:t},e,n)=>{t[e]=n},Ib=(t,e,n)=>{switch(t.type.unit){case yt.SECOND:return Pm(t,e,n);case yt.MILLISECOND:return Mm(t,e,n);case yt.MICROSECOND:return Nm(t,e,n);case yt.NANOSECOND:return Cm(t,e,n)}},Ob=(t,e,n)=>{const{stride:r}=t,i=t.children[0],o=Bn.getVisitFn(i);if(Array.isArray(n))for(let t=-1,s=e*r;++t<r;)o(i,s+t,n[t]);else for(let t=-1,s=e*r;++t<r;)o(i,s+t,n.get(t))};St.prototype.visitBool=kt(sb),St.prototype.visitInt=kt(Or),St.prototype.visitInt8=kt(Or),St.prototype.visitInt16=kt(Or),St.prototype.visitInt32=kt(Or),St.prototype.visitInt64=kt(Or),St.prototype.visitUint8=kt(Or),St.prototype.visitUint16=kt(Or),St.prototype.visitUint32=kt(Or),St.prototype.visitUint64=kt(Or),St.prototype.visitFloat=kt(ab),St.prototype.visitFloat16=kt(mm),St.prototype.visitFloat32=kt(sc),St.prototype.visitFloat64=kt(sc),St.prototype.visitUtf8=kt(bm),St.prototype.visitLargeUtf8=kt(bm),St.prototype.visitBinary=kt(_m),St.prototype.visitLargeBinary=kt(_m),St.prototype.visitFixedSizeBinary=kt(lb),St.prototype.visitDate=kt(cb),St.prototype.visitDateDay=kt(pm),St.prototype.visitDateMillisecond=kt(gm),St.prototype.visitTimestamp=kt(ub),St.prototype.visitTimestampSecond=kt(ym),St.prototype.visitTimestampMillisecond=kt(vm),St.prototype.visitTimestampMicrosecond=kt(wm),St.prototype.visitTimestampNanosecond=kt(Sm),St.prototype.visitTime=kt(fb),St.prototype.visitTimeSecond=kt(Im),St.prototype.visitTimeMillisecond=kt(Om),St.prototype.visitTimeMicrosecond=kt(km),St.prototype.visitTimeNanosecond=kt(Dm),St.prototype.visitDecimal=kt(db),St.prototype.visitList=kt(hb),St.prototype.visitStruct=kt(yb),St.prototype.visitUnion=kt(vb),St.prototype.visitDenseUnion=kt(Em),St.prototype.visitSparseUnion=kt(Am),St.prototype.visitDictionary=kt(wb),St.prototype.visitInterval=kt(Sb),St.prototype.visitIntervalDayTime=kt(Tm),St.prototype.visitIntervalYearMonth=kt(Bm),St.prototype.visitDuration=kt(Ib),St.prototype.visitDurationSecond=kt(Pm),St.prototype.visitDurationMillisecond=kt(Mm),St.prototype.visitDurationMicrosecond=kt(Nm),St.prototype.visitDurationNanosecond=kt(Cm),St.prototype.visitFixedSizeList=kt(Ob),St.prototype.visitMap=kt(mb);const Bn=new St,jn=Symbol.for("parent"),Ni=Symbol.for("rowIndex");class ac{constructor(t,e){return this[jn]=t,this[Ni]=e,new Proxy(this,new Db)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Ni],e=this[jn],n=e.type.children,r={};for(let i=-1,o=n.length;++i<o;)r[n[i].name]=hn.visit(e.children[i],t);return r}toString(){return`{${[...this].map((([t,e])=>`${Yo(t)}: ${Yo(e)}`)).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new kb(this[jn],this[Ni])}}class kb{constructor(t,e){this.childIndex=0,this.children=t.children,this.rowIndex=e,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,hn.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(ac.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[jn]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Ni]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class Db{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[jn].type.children.map((t=>t.name))}has(t,e){return-1!==t[jn].type.children.findIndex((t=>t.name===e))}getOwnPropertyDescriptor(t,e){if(-1!==t[jn].type.children.findIndex((t=>t.name===e)))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const n=t[jn].type.children.findIndex((t=>t.name===e));if(-1!==n){const r=hn.visit(t[jn].children[n],t[Ni]);return Reflect.set(t,e,r),r}}set(t,e,n){const r=t[jn].type.children.findIndex((t=>t.name===e));return-1!==r?(Bn.visit(t[jn].children[r],t[Ni],n),Reflect.set(t,e,n)):!(!Reflect.has(t,e)&&"symbol"!=typeof e)&&Reflect.set(t,e,n)}}class pt extends Ht{}function It(t){return(e,n)=>e.getValid(n)?t(e,n):null}const Eb=(t,e)=>864e5*t[e],Ab=(t,e)=>null,Fm=(t,e,n)=>{if(n+1>=e.length)return null;const r=Se(e[n]),i=Se(e[n+1]);return t.subarray(r,i)},Tb=({offset:t,values:e},n)=>{const r=t+n;return!!(e[r>>3]&1<<r%8)},jm=({values:t},e)=>Eb(t,e),Lm=({values:t},e)=>Se(t[e]),zr=({stride:t,values:e},n)=>e[t*n],Bb=({stride:t,values:e},n)=>dm(e[t*n]),Rm=({values:t},e)=>t[e],Pb=({stride:t,values:e},n)=>e.subarray(t*n,t*(n+1)),Um=({values:t,valueOffsets:e},n)=>Fm(t,e,n),Vm=({values:t,valueOffsets:e},n)=>{const r=Fm(t,e,n);return null!==r?kl(r):null},Mb=({values:t},e)=>t[e],Nb=({type:t,values:e},n)=>t.precision!==Je.HALF?e[n]:dm(e[n]),Cb=(t,e)=>t.type.unit===Tn.DAY?jm(t,e):Lm(t,e),zm=({values:t},e)=>1e3*Se(t[e]),Wm=({values:t},e)=>Se(t[e]),Hm=({values:t},e)=>zh(t[e],BigInt(1e3)),xm=({values:t},e)=>zh(t[e],BigInt(1e6)),Fb=(t,e)=>{switch(t.type.unit){case yt.SECOND:return zm(t,e);case yt.MILLISECOND:return Wm(t,e);case yt.MICROSECOND:return Hm(t,e);case yt.NANOSECOND:return xm(t,e)}},qm=({values:t},e)=>t[e],Ym=({values:t},e)=>t[e],Km=({values:t},e)=>t[e],Jm=({values:t},e)=>t[e],jb=(t,e)=>{switch(t.type.unit){case yt.SECOND:return qm(t,e);case yt.MILLISECOND:return Ym(t,e);case yt.MICROSECOND:return Km(t,e);case yt.NANOSECOND:return Jm(t,e)}},Lb=({values:t,stride:e},n)=>oc.decimal(t.subarray(e*n,e*(n+1))),Rb=(t,e)=>{const{valueOffsets:n,stride:r,children:i}=t,{[e*r]:o,[e*r+1]:s}=n,a=i[0].slice(o,s-o);return new de([a])},Ub=(t,e)=>{const{valueOffsets:n,children:r}=t,{[e]:i,[e+1]:o}=n,s=r[0];return new lc(s.slice(i,o-i))},Vb=(t,e)=>new ac(t,e),zb=(t,e)=>t.type.mode===dn.Dense?Qm(t,e):Gm(t,e),Qm=(t,e)=>{const n=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[n];return hn.visit(r,t.valueOffsets[e])},Gm=(t,e)=>{const n=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[n];return hn.visit(r,e)},Wb=(t,e)=>{var n;return null===(n=t.dictionary)||void 0===n?void 0:n.get(t.values[e])},Hb=(t,e)=>t.type.unit===ir.DAY_TIME?Xm(t,e):Zm(t,e),Xm=({values:t},e)=>t.subarray(2*e,2*(e+1)),Zm=({values:t},e)=>{const n=t[e],r=new Int32Array(2);return r[0]=Math.trunc(n/12),r[1]=Math.trunc(n%12),r},$m=({values:t},e)=>t[e],tp=({values:t},e)=>t[e],ep=({values:t},e)=>t[e],np=({values:t},e)=>t[e],xb=(t,e)=>{switch(t.type.unit){case yt.SECOND:return $m(t,e);case yt.MILLISECOND:return tp(t,e);case yt.MICROSECOND:return ep(t,e);case yt.NANOSECOND:return np(t,e)}},qb=(t,e)=>{const{stride:n,children:r}=t,i=r[0].slice(e*n,n);return new de([i])};pt.prototype.visitNull=It(Ab),pt.prototype.visitBool=It(Tb),pt.prototype.visitInt=It(Mb),pt.prototype.visitInt8=It(zr),pt.prototype.visitInt16=It(zr),pt.prototype.visitInt32=It(zr),pt.prototype.visitInt64=It(Rm),pt.prototype.visitUint8=It(zr),pt.prototype.visitUint16=It(zr),pt.prototype.visitUint32=It(zr),pt.prototype.visitUint64=It(Rm),pt.prototype.visitFloat=It(Nb),pt.prototype.visitFloat16=It(Bb),pt.prototype.visitFloat32=It(zr),pt.prototype.visitFloat64=It(zr),pt.prototype.visitUtf8=It(Vm),pt.prototype.visitLargeUtf8=It(Vm),pt.prototype.visitBinary=It(Um),pt.prototype.visitLargeBinary=It(Um),pt.prototype.visitFixedSizeBinary=It(Pb),pt.prototype.visitDate=It(Cb),pt.prototype.visitDateDay=It(jm),pt.prototype.visitDateMillisecond=It(Lm),pt.prototype.visitTimestamp=It(Fb),pt.prototype.visitTimestampSecond=It(zm),pt.prototype.visitTimestampMillisecond=It(Wm),pt.prototype.visitTimestampMicrosecond=It(Hm),pt.prototype.visitTimestampNanosecond=It(xm),pt.prototype.visitTime=It(jb),pt.prototype.visitTimeSecond=It(qm),pt.prototype.visitTimeMillisecond=It(Ym),pt.prototype.visitTimeMicrosecond=It(Km),pt.prototype.visitTimeNanosecond=It(Jm),pt.prototype.visitDecimal=It(Lb),pt.prototype.visitList=It(Rb),pt.prototype.visitStruct=It(Vb),pt.prototype.visitUnion=It(zb),pt.prototype.visitDenseUnion=It(Qm),pt.prototype.visitSparseUnion=It(Gm),pt.prototype.visitDictionary=It(Wb),pt.prototype.visitInterval=It(Hb),pt.prototype.visitIntervalDayTime=It(Xm),pt.prototype.visitIntervalYearMonth=It(Zm),pt.prototype.visitDuration=It(xb),pt.prototype.visitDurationSecond=It($m),pt.prototype.visitDurationMillisecond=It(tp),pt.prototype.visitDurationMicrosecond=It(ep),pt.prototype.visitDurationNanosecond=It(np),pt.prototype.visitFixedSizeList=It(qb),pt.prototype.visitMap=It(Ub);const hn=new pt,ki=Symbol.for("keys"),Ci=Symbol.for("vals"),Di=Symbol.for("kKeysAsStrings"),Pl=Symbol.for("_kKeysAsStrings");class lc{constructor(t){return this[ki]=new de([t.children[0]]).memoize(),this[Ci]=t.children[1],new Proxy(this,new Kb)}get[Di](){return this[Pl]||(this[Pl]=Array.from(this[ki].toArray(),String))}[Symbol.iterator](){return new Yb(this[ki],this[Ci])}get size(){return this[ki].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[ki],e=this[Ci],n={};for(let r=-1,i=t.length;++r<i;)n[t.get(r)]=hn.visit(e,r);return n}toString(){return`{${[...this].map((([t,e])=>`${Yo(t)}: ${Yo(e)}`)).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class Yb{constructor(t,e){this.keys=t,this.vals=e,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),hn.visit(this.vals,t)]})}}class Kb{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Di]}has(t,e){return t[Di].includes(e)}getOwnPropertyDescriptor(t,e){if(-1!==t[Di].indexOf(e))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const n=t[Di].indexOf(e);if(-1!==n){const r=hn.visit(Reflect.get(t,Ci),n);return Reflect.set(t,e,r),r}}set(t,e,n){const r=t[Di].indexOf(e);return-1!==r?(Bn.visit(Reflect.get(t,Ci),r,n),Reflect.set(t,e,n)):!!Reflect.has(t,e)&&Reflect.set(t,e,n)}}let du;function rp(t,e,n,r){const{length:i=0}=t;let o="number"!=typeof e?0:e,s="number"!=typeof n?i:n;return o<0&&(o=(o%i+i)%i),s<0&&(s=(s%i+i)%i),s<o&&(du=o,o=s,s=du),s>i&&(s=i),r?r(t,o,s):[o,s]}Object.defineProperties(lc.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[ki]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Ci]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Pl]:{writable:!0,enumerable:!1,configurable:!1,value:null}});const cc=(t,e)=>t<0?e+t:t,hu=t=>t!=t;function Yi(t){if("object"!=typeof t||null===t)return hu(t)?hu:e=>e===t;if(t instanceof Date){const e=t.valueOf();return t=>t instanceof Date&&t.valueOf()===e}return ArrayBuffer.isView(t)?e=>!!e&&q_(t,e):t instanceof Map?Qb(t):Array.isArray(t)?Jb(t):t instanceof de?Gb(t):Xb(t,!0)}function Jb(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=Yi(t[n]);return Fa(e)}function Qb(t){let e=-1;const n=[];for(const r of t.values())n[++e]=Yi(r);return Fa(n)}function Gb(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=Yi(t.get(n));return Fa(e)}function Xb(t,e=!1){const n=Object.keys(t);if(!e&&0===n.length)return()=>!1;const r=[];for(let e=-1,i=n.length;++e<i;)r[e]=Yi(t[n[e]]);return Fa(r,n)}function Fa(t,e){return n=>{if(!n||"object"!=typeof n)return!1;switch(n.constructor){case Array:return Zb(t,n);case Map:return mu(t,n,n.keys());case lc:case ac:case Object:case void 0:return mu(t,n,e||Object.keys(n))}return n instanceof de&&$b(t,n)}}function Zb(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e[r]))return!1;return!0}function $b(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e.get(r)))return!1;return!0}function mu(t,e,n){const r=n[Symbol.iterator](),i=e instanceof Map?e.keys():Object.keys(e)[Symbol.iterator](),o=e instanceof Map?e.values():Object.values(e)[Symbol.iterator]();let s=0;const a=t.length;let l=o.next(),c=r.next(),u=i.next();for(;s<a&&!c.done&&!u.done&&!l.done&&c.value===u.value&&t[s](l.value);++s,c=r.next(),u=i.next(),l=o.next());return!!(s===a&&c.done&&u.done&&l.done)||(r.return&&r.return(),i.return&&i.return(),o.return&&o.return(),!1)}function ip(t,e,n,r){return!!(n&1<<r)}function ty(t,e,n,r){return(n&1<<r)>>r}function pu(t,e,n){const r=n.byteLength+7&-8;if(t>0||n.byteLength<r){const i=new Uint8Array(r);return i.set(t%8==0?n.subarray(t>>3):Ml(new uc(n,t,e,null,ip)).subarray(0,r)),i}return n}function Ml(t){const e=[];let n=0,r=0,i=0;for(const o of t)o&&(i|=1<<r),8===++r&&(e[n++]=i,i=r=0);(0===n||r>0)&&(e[n++]=i);const o=new Uint8Array(e.length+7&-8);return o.set(e),o}class uc{constructor(t,e,n,r,i){this.bytes=t,this.length=n,this.context=r,this.get=i,this.bit=e%8,this.byteIndex=e>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(8===this.bit&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function Nl(t,e,n){if(n-e<=0)return 0;if(n-e<8){let r=0;for(const i of new uc(t,e,n-e,t,ty))r+=i;return r}const r=n>>3<<3,i=e+(e%8==0?0:8-e%8);return Nl(t,e,i)+Nl(t,r,n)+ey(t,i>>3,r-i>>3)}function ey(t,e,n){let r=0,i=Math.trunc(e);const o=new DataView(t.buffer,t.byteOffset,t.byteLength),s=void 0===n?t.byteLength:i+n;for(;s-i>=4;)r+=el(o.getUint32(i)),i+=4;for(;s-i>=2;)r+=el(o.getUint16(i)),i+=2;for(;s-i>=1;)r+=el(o.getUint8(i)),i+=1;return r}function el(t){let e=Math.trunc(t);return e-=e>>>1&1431655765,e=(858993459&e)+(e>>>2&858993459),16843009*(e+(e>>>4)&252645135)>>>24}const ny=-1;class le{get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get nullable(){if(0!==this._nullCount){const{type:t}=this;return ft.isSparseUnion(t)||ft.isDenseUnion(t)?this.children.some((t=>t.nullable)):this.nullBitmap&&this.nullBitmap.byteLength>0}return!0}get byteLength(){let t=0;const{valueOffsets:e,values:n,nullBitmap:r,typeIds:i}=this;return e&&(t+=e.byteLength),n&&(t+=n.byteLength),r&&(t+=r.byteLength),i&&(t+=i.byteLength),this.children.reduce(((t,e)=>t+e.byteLength),t)}get nullCount(){if(ft.isUnion(this.type))return this.children.reduce(((t,e)=>t+e.nullCount),0);let t,e=this._nullCount;return e<=-1&&(t=this.nullBitmap)&&(this._nullCount=e=0===t.length?0:this.length-Nl(t,this.offset,this.offset+this.length)),e}constructor(t,e,n,r,i,o=[],s){let a;this.type=t,this.children=o,this.dictionary=s,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(n||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1)),i instanceof le?(this.stride=i.stride,this.values=i.values,this.typeIds=i.typeIds,this.nullBitmap=i.nullBitmap,this.valueOffsets=i.valueOffsets):(this.stride=fr(t),i&&((a=i[0])&&(this.valueOffsets=a),(a=i[1])&&(this.values=a),(a=i[2])&&(this.nullBitmap=a),(a=i[3])&&(this.typeIds=a)))}getValid(t){const{type:e}=this;if(ft.isUnion(e)){const n=e,r=this.children[n.typeIdToChildIndex[this.typeIds[t]]],i=n.mode===dn.Dense?this.valueOffsets[t]:t;return r.getValid(i)}if(this.nullable&&this.nullCount>0){const e=this.offset+t;return!!(this.nullBitmap[e>>3]&1<<e%8)}return!0}setValid(t,e){let n;const{type:r}=this;if(ft.isUnion(r)){const i=r,o=this.children[i.typeIdToChildIndex[this.typeIds[t]]],s=i.mode===dn.Dense?this.valueOffsets[t]:t;n=o.getValid(s),o.setValid(s,e)}else{let{nullBitmap:r}=this;const{offset:i,length:o}=this,s=i+t,a=1<<s%8,l=s>>3;(!r||r.byteLength<=l)&&(r=new Uint8Array((i+o+63&-64)>>3).fill(255),this.nullCount>0?(r.set(pu(i,o,this.nullBitmap),0),Object.assign(this,{nullBitmap:r})):Object.assign(this,{nullBitmap:r,_nullCount:0}));const c=r[l];n=0!==(c&a),r[l]=e?c|a:c&~a}return n!==!!e&&(this._nullCount=this.nullCount+(e?-1:1)),e}clone(t=this.type,e=this.offset,n=this.length,r=this._nullCount,i=this,o=this.children){return new le(t,e,n,r,i,o,this.dictionary)}slice(t,e){const{stride:n,typeId:r,children:i}=this,o=+(0===this._nullCount)-1,s=16===r?n:1,a=this._sliceBuffers(t,e,n,r);return this.clone(this.type,this.offset+t,e,o,a,0===i.length||this.valueOffsets?i:this._sliceChildren(i,s*t,s*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===A.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:n}=this,r=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);r[e>>3]=(1<<e-(-8&e))-1,n>0&&r.set(pu(this.offset,e,this.nullBitmap),0);const i=this.buffers;return i[xr.VALIDITY]=r,this.clone(this.type,0,t,n+(t-e),i)}_sliceBuffers(t,e,n,r){let i;const{buffers:o}=this;return(i=o[xr.TYPE])&&(o[xr.TYPE]=i.subarray(t,t+e)),(i=o[xr.OFFSET])&&(o[xr.OFFSET]=i.subarray(t,t+e+1))||(i=o[xr.DATA])&&(o[xr.DATA]=6===r?i:i.subarray(n*t,n*(t+e))),o}_sliceChildren(t,e,n){return t.map((t=>t.slice(e,n)))}}le.prototype.children=Object.freeze([]);class Eo extends Ht{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{type:e,offset:n=0,length:r=0}=t;return new le(e,n,r,r)}visitBool(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length>>3,nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitInt(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length,nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitFloat(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length,nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitUtf8(t){const{type:e,offset:n=0}=t,r=Gt(t.data),i=Gt(t.nullBitmap),o=oo(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new le(e,n,s,a,[o,r,i])}visitLargeUtf8(t){const{type:e,offset:n=0}=t,r=Gt(t.data),i=Gt(t.nullBitmap),o=eu(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new le(e,n,s,a,[o,r,i])}visitBinary(t){const{type:e,offset:n=0}=t,r=Gt(t.data),i=Gt(t.nullBitmap),o=oo(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new le(e,n,s,a,[o,r,i])}visitLargeBinary(t){const{type:e,offset:n=0}=t,r=Gt(t.data),i=Gt(t.nullBitmap),o=eu(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new le(e,n,s,a,[o,r,i])}visitFixedSizeBinary(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length/fr(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitDate(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length/fr(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitTimestamp(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length/fr(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitTime(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length/fr(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitDecimal(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length/fr(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitList(t){const{type:e,offset:n=0,child:r}=t,i=Gt(t.nullBitmap),o=oo(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new le(e,n,s,a,[o,void 0,i],[r])}visitStruct(t){const{type:e,offset:n=0,children:r=[]}=t,i=Gt(t.nullBitmap),{length:o=r.reduce(((t,{length:e})=>Math.max(t,e)),0),nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,void 0,i],r)}visitUnion(t){const{type:e,offset:n=0,children:r=[]}=t,i=fe(e.ArrayType,t.typeIds),{length:o=i.length,nullCount:s=-1}=t;if(ft.isSparseUnion(e))return new le(e,n,o,s,[void 0,void 0,void 0,i],r);const a=oo(t.valueOffsets);return new le(e,n,o,s,[a,void 0,void 0,i],r)}visitDictionary(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.indices.ArrayType,t.data),{dictionary:o=new de([(new Eo).visit({type:e.dictionary})])}=t,{length:s=i.length,nullCount:a=(t.nullBitmap?-1:0)}=t;return new le(e,n,s,a,[void 0,i,r],[],o)}visitInterval(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length/fr(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitDuration(t){const{type:e,offset:n=0}=t,r=Gt(t.nullBitmap),i=fe(e.ArrayType,t.data),{length:o=i.length,nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,i,r])}visitFixedSizeList(t){const{type:e,offset:n=0,child:r=(new Eo).visit({type:e.valueType})}=t,i=Gt(t.nullBitmap),{length:o=r.length/fr(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new le(e,n,o,s,[void 0,void 0,i],[r])}visitMap(t){const{type:e,offset:n=0,child:r=(new Eo).visit({type:e.childType})}=t,i=Gt(t.nullBitmap),o=oo(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new le(e,n,s,a,[o,void 0,i],[r])}}const ry=new Eo;function zt(t){return ry.visit(t)}class gu{constructor(t=0,e){this.numChunks=t,this.getChunkIterator=e,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function iy(t){return t.some((t=>t.nullable))}function op(t){return t.reduce(((t,e)=>t+e.nullCount),0)}function sp(t){return t.reduce(((t,e,n)=>(t[n+1]=t[n]+e.length,t)),new Uint32Array(t.length+1))}function ap(t,e,n,r){const i=[];for(let o=-1,s=t.length;++o<s;){const s=t[o],a=e[o],{length:l}=s;if(a>=r)break;if(n>=a+l)continue;if(a>=n&&a+l<=r){i.push(s);continue}const c=Math.max(0,n-a),u=Math.min(r-a,l);i.push(s.slice(c,u-c))}return 0===i.length&&i.push(t[0].slice(0,0)),i}function fc(t,e,n,r){let i=0,o=0,s=e.length-1;do{if(i>=s-1)return n<e[s]?r(t,i,n-e[i]):null;o=i+Math.trunc(.5*(s-i)),n<e[o]?s=o:i=o}while(i<s)}function dc(t,e){return t.getValid(e)}function ba(t){function e(e,n,r){return t(e[n],r)}return function(t){return fc(this.data,this._offsets,t,e)}}function lp(t){let e;function n(n,r,i){return t(n[r],i,e)}return function(t,r){const i=this.data;e=r;const o=fc(i,this._offsets,t,n);return e=void 0,o}}function cp(t){let e;function n(n,r,i){let o=i,s=0,a=0;for(let i=r-1,l=n.length;++i<l;){const r=n[i];if(~(s=t(r,e,o)))return a+s;o=0,a+=r.length}return-1}return function(t,r){e=t;const i=this.data,o="number"!=typeof r?n(i,0,0):fc(i,this._offsets,r,n);return e=void 0,o}}class gt extends Ht{}function oy(t,e){return null===e&&t.length>0?0:-1}function sy(t,e){const{nullBitmap:n}=t;if(!n||t.nullCount<=0)return-1;let r=0;for(const i of new uc(n,t.offset+(e||0),t.length,n,ip)){if(!i)return r;++r}return-1}function Tt(t,e,n){if(void 0===e)return-1;if(null===e)switch(t.typeId){case A.Union:case A.Dictionary:break;default:return sy(t,n)}const r=hn.getVisitFn(t),i=Yi(e);for(let e=(n||0)-1,o=t.length;++e<o;)if(i(r(t,e)))return e;return-1}function up(t,e,n){const r=hn.getVisitFn(t),i=Yi(e);for(let e=(n||0)-1,o=t.length;++e<o;)if(i(r(t,e)))return e;return-1}gt.prototype.visitNull=oy,gt.prototype.visitBool=Tt,gt.prototype.visitInt=Tt,gt.prototype.visitInt8=Tt,gt.prototype.visitInt16=Tt,gt.prototype.visitInt32=Tt,gt.prototype.visitInt64=Tt,gt.prototype.visitUint8=Tt,gt.prototype.visitUint16=Tt,gt.prototype.visitUint32=Tt,gt.prototype.visitUint64=Tt,gt.prototype.visitFloat=Tt,gt.prototype.visitFloat16=Tt,gt.prototype.visitFloat32=Tt,gt.prototype.visitFloat64=Tt,gt.prototype.visitUtf8=Tt,gt.prototype.visitLargeUtf8=Tt,gt.prototype.visitBinary=Tt,gt.prototype.visitLargeBinary=Tt,gt.prototype.visitFixedSizeBinary=Tt,gt.prototype.visitDate=Tt,gt.prototype.visitDateDay=Tt,gt.prototype.visitDateMillisecond=Tt,gt.prototype.visitTimestamp=Tt,gt.prototype.visitTimestampSecond=Tt,gt.prototype.visitTimestampMillisecond=Tt,gt.prototype.visitTimestampMicrosecond=Tt,gt.prototype.visitTimestampNanosecond=Tt,gt.prototype.visitTime=Tt,gt.prototype.visitTimeSecond=Tt,gt.prototype.visitTimeMillisecond=Tt,gt.prototype.visitTimeMicrosecond=Tt,gt.prototype.visitTimeNanosecond=Tt,gt.prototype.visitDecimal=Tt,gt.prototype.visitList=Tt,gt.prototype.visitStruct=Tt,gt.prototype.visitUnion=Tt,gt.prototype.visitDenseUnion=up,gt.prototype.visitSparseUnion=up,gt.prototype.visitDictionary=Tt,gt.prototype.visitInterval=Tt,gt.prototype.visitIntervalDayTime=Tt,gt.prototype.visitIntervalYearMonth=Tt,gt.prototype.visitDuration=Tt,gt.prototype.visitDurationSecond=Tt,gt.prototype.visitDurationMillisecond=Tt,gt.prototype.visitDurationMicrosecond=Tt,gt.prototype.visitDurationNanosecond=Tt,gt.prototype.visitFixedSizeList=Tt,gt.prototype.visitMap=Tt;const ya=new gt;class _t extends Ht{}function Ot(t){const{type:e}=t;if(0===t.nullCount&&1===t.stride&&(ft.isInt(e)&&64!==e.bitWidth||ft.isTime(e)&&64!==e.bitWidth||ft.isFloat(e)&&e.precision!==Je.HALF))return new gu(t.data.length,(e=>{const n=t.data[e];return n.values.subarray(0,n.length)[Symbol.iterator]()}));let n=0;return new gu(t.data.length,(e=>{const r=t.data[e].length,i=t.slice(n,n+r);return n+=r,new ay(i)}))}class ay{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}_t.prototype.visitNull=Ot,_t.prototype.visitBool=Ot,_t.prototype.visitInt=Ot,_t.prototype.visitInt8=Ot,_t.prototype.visitInt16=Ot,_t.prototype.visitInt32=Ot,_t.prototype.visitInt64=Ot,_t.prototype.visitUint8=Ot,_t.prototype.visitUint16=Ot,_t.prototype.visitUint32=Ot,_t.prototype.visitUint64=Ot,_t.prototype.visitFloat=Ot,_t.prototype.visitFloat16=Ot,_t.prototype.visitFloat32=Ot,_t.prototype.visitFloat64=Ot,_t.prototype.visitUtf8=Ot,_t.prototype.visitLargeUtf8=Ot,_t.prototype.visitBinary=Ot,_t.prototype.visitLargeBinary=Ot,_t.prototype.visitFixedSizeBinary=Ot,_t.prototype.visitDate=Ot,_t.prototype.visitDateDay=Ot,_t.prototype.visitDateMillisecond=Ot,_t.prototype.visitTimestamp=Ot,_t.prototype.visitTimestampSecond=Ot,_t.prototype.visitTimestampMillisecond=Ot,_t.prototype.visitTimestampMicrosecond=Ot,_t.prototype.visitTimestampNanosecond=Ot,_t.prototype.visitTime=Ot,_t.prototype.visitTimeSecond=Ot,_t.prototype.visitTimeMillisecond=Ot,_t.prototype.visitTimeMicrosecond=Ot,_t.prototype.visitTimeNanosecond=Ot,_t.prototype.visitDecimal=Ot,_t.prototype.visitList=Ot,_t.prototype.visitStruct=Ot,_t.prototype.visitUnion=Ot,_t.prototype.visitDenseUnion=Ot,_t.prototype.visitSparseUnion=Ot,_t.prototype.visitDictionary=Ot,_t.prototype.visitInterval=Ot,_t.prototype.visitIntervalDayTime=Ot,_t.prototype.visitIntervalYearMonth=Ot,_t.prototype.visitDuration=Ot,_t.prototype.visitDurationSecond=Ot,_t.prototype.visitDurationMillisecond=Ot,_t.prototype.visitDurationMicrosecond=Ot,_t.prototype.visitDurationNanosecond=Ot,_t.prototype.visitFixedSizeList=Ot,_t.prototype.visitMap=Ot;const hc=new _t;var fp;const dp={},hp={};class de{constructor(t){var e,n,r;const i=t[0]instanceof de?t.flatMap((t=>t.data)):t;if(0===i.length||i.some((t=>!(t instanceof le))))throw new TypeError("Vector constructor expects an Array of Data instances.");const o=null===(e=i[0])||void 0===e?void 0:e.type;switch(i.length){case 0:this._offsets=[0];break;case 1:{const{get:t,set:e,indexOf:n}=dp[o.typeId],r=i[0];this.isValid=t=>dc(r,t),this.get=e=>t(r,e),this.set=(t,n)=>e(r,t,n),this.indexOf=t=>n(r,t),this._offsets=[0,r.length];break}default:Object.setPrototypeOf(this,hp[o.typeId]),this._offsets=sp(i)}this.data=i,this.type=o,this.stride=fr(o),this.numChildren=null!==(r=null===(n=o.children)||void 0===n?void 0:n.length)&&void 0!==r?r:0,this.length=this._offsets.at(-1)}get byteLength(){return this.data.reduce(((t,e)=>t+e.byteLength),0)}get nullable(){return iy(this.data)}get nullCount(){return op(this.data)}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${A[this.type.typeId]}Vector`}isValid(t){return!1}get(t){return null}at(t){return this.get(cc(t,this.length))}set(t,e){}indexOf(t,e){return-1}includes(t,e){return this.indexOf(t,e)>-1}[Symbol.iterator](){return hc.visit(this)}concat(...t){return new de(this.data.concat(t.flatMap((t=>t.data)).flat(Number.POSITIVE_INFINITY)))}slice(t,e){return new de(rp(this,t,e,(({data:t,_offsets:e},n,r)=>ap(t,e,n,r))))}toJSON(){return[...this]}toArray(){const{type:t,data:e,length:n,stride:r,ArrayType:i}=this;switch(t.typeId){case A.Int:case A.Float:case A.Decimal:case A.Time:case A.Timestamp:switch(e.length){case 0:return new i;case 1:return e[0].values.subarray(0,n*r);default:return e.reduce(((t,{values:e,length:n})=>(t.array.set(e.subarray(0,n*r),t.offset),t.offset+=n*r,t)),{array:new i(n*r),offset:0}).array}}return[...this]}toString(){return`[${[...this].join(",")}]`}getChild(t){var e;return this.getChildAt(null===(e=this.type.children)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.numChildren?new de(this.data.map((({children:e})=>e[t]))):null}get isMemoized(){return!!ft.isDictionary(this.type)&&this.data[0].dictionary.isMemoized}memoize(){if(ft.isDictionary(this.type)){const t=new va(this.data[0].dictionary),e=this.data.map((e=>{const n=e.clone();return n.dictionary=t,n}));return new de(e)}return new va(this)}unmemoize(){if(ft.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),e=this.data.map((e=>{const n=e.clone();return n.dictionary=t,n}));return new de(e)}return this}}fp=Symbol.toStringTag,de[fp]=(t=>{t.type=ft.prototype,t.data=[],t.length=0,t.stride=1,t.numChildren=0,t._offsets=new Uint32Array([0]),t[Symbol.isConcatSpreadable]=!0;const e=Object.keys(A).map((t=>A[t])).filter((t=>"number"==typeof t&&t!==A.NONE));for(const n of e){const e=hn.getVisitFnByTypeId(n),r=Bn.getVisitFnByTypeId(n),i=ya.getVisitFnByTypeId(n);dp[n]={get:e,set:r,indexOf:i},hp[n]=Object.create(t,{isValid:{value:ba(dc)},get:{value:ba(hn.getVisitFnByTypeId(n))},set:{value:lp(Bn.getVisitFnByTypeId(n))},indexOf:{value:cp(ya.getVisitFnByTypeId(n))}})}return"Vector"})(de.prototype);class va extends de{constructor(t){super(t.data);const e=this.get,n=this.set,r=this.slice,i=new Array(this.length);Object.defineProperty(this,"get",{value(t){const n=i[t];if(void 0!==n)return n;const r=e.call(this,t);return i[t]=r,r}}),Object.defineProperty(this,"set",{value(t,e){n.call(this,t,e),i[t]=e}}),Object.defineProperty(this,"slice",{value:(t,e)=>new va(r.call(this,t,e))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new de(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Cl{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,e,n,r){return t.prep(8,24),t.writeInt64(BigInt(r??0)),t.pad(4),t.writeInt32(n),t.writeInt64(BigInt(e??0)),t.offset()}}class pn{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new pn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,e){return t.setPosition(t.position()+4),(e||new pn).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Fe.V1}schema(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Jn).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Cl).__init(this.bb.__vector(this.bb_pos+n)+24*t,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,e){const n=this.bb.__offset(this.bb_pos,10);return n?(e||new Cl).__init(this.bb.__vector(this.bb_pos+n)+24*t,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,12);return n?(e||new Le).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Fe.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,e){t.finish(e)}static finishSizePrefixedFooterBuffer(t,e){t.finish(e,void 0,!0)}}class se{constructor(t=[],e,n,r=Fe.V5){this.fields=t||[],this.metadata=e||new Map,n||(n=Fl(this.fields)),this.dictionaries=n,this.metadataVersion=r}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map((t=>t.name))}toString(){return`Schema<{ ${this.fields.map(((t,e)=>`${e}: ${t}`)).join(", ")} }>`}select(t){const e=new Set(t),n=this.fields.filter((t=>e.has(t.name)));return new se(n,this.metadata)}selectAt(t){const e=t.map((t=>this.fields[t])).filter(Boolean);return new se(e,this.metadata)}assign(...t){const e=t[0]instanceof se?t[0]:Array.isArray(t[0])?new se(t[0]):new se(t),n=[...this.fields],r=Is(Is(new Map,this.metadata),e.metadata),i=e.fields.filter((t=>{const e=n.findIndex((e=>e.name===t.name));return!~e||(n[e]=t.clone({metadata:Is(Is(new Map,n[e].metadata),t.metadata)}))&&!1})),o=Fl(i,new Map);return new se([...n,...i],r,new Map([...this.dictionaries,...o]))}}se.prototype.fields=null,se.prototype.metadata=null,se.prototype.dictionaries=null;class Ie{static new(...t){let[e,n,r,i]=t;return t[0]&&"object"==typeof t[0]&&(({name:e}=t[0]),void 0===n&&(n=t[0].type),void 0===r&&(r=t[0].nullable),void 0===i&&(i=t[0].metadata)),new Ie(`${e}`,n,r,i)}constructor(t,e,n=!1,r){this.name=t,this.type=e,this.nullable=n,this.metadata=r||new Map}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...t){let[e,n,r,i]=t;return t[0]&&"object"==typeof t[0]?({name:e=this.name,type:n=this.type,nullable:r=this.nullable,metadata:i=this.metadata}=t[0]):[e=this.name,n=this.type,r=this.nullable,i=this.metadata]=t,Ie.new(e,n,r,i)}}function Is(t,e){return new Map([...t||new Map,...e||new Map])}function Fl(t,e=new Map){for(let n=-1,r=t.length;++n<r;){const r=t[n].type;if(ft.isDictionary(r))if(e.has(r.id)){if(e.get(r.id)!==r.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else e.set(r.id,r.dictionary);r.children&&r.children.length>0&&Fl(r.children,e)}return e}Ie.prototype.type=null,Ie.prototype.name=null,Ie.prototype.nullable=null,Ie.prototype.metadata=null;var ly=Lh,cy=Li;class mc{static decode(t){t=new cy(Gt(t));const e=pn.getRootAsFooter(t),n=se.decode(e.schema(),new Map,e.version());return new uy(n,e)}static encode(t){const e=new ly,n=se.encode(e,t.schema);pn.startRecordBatchesVector(e,t.numRecordBatches);for(const n of[...t.recordBatches()].slice().reverse())Vi.encode(e,n);const r=e.endVector();pn.startDictionariesVector(e,t.numDictionaries);for(const n of[...t.dictionaryBatches()].slice().reverse())Vi.encode(e,n);const i=e.endVector();return pn.startFooter(e),pn.addSchema(e,n),pn.addVersion(e,Fe.V5),pn.addRecordBatches(e,r),pn.addDictionaries(e,i),pn.finishFooterBuffer(e,pn.endFooter(e)),e.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}constructor(t,e=Fe.V5,n,r){this.schema=t,this.version=e,n&&(this._recordBatches=n),r&&(this._dictionaryBatches=r)}*recordBatches(){for(let t,e=-1,n=this.numRecordBatches;++e<n;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,n=this.numDictionaries;++e<n;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class uy extends mc{get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}constructor(t,e){super(t,e.version()),this._footer=e}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return Vi.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return Vi.decode(e)}return null}}class Vi{static decode(t){return new Vi(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:n}=e,r=BigInt(e.offset),i=BigInt(e.bodyLength);return Cl.createBlock(t,r,n,i)}constructor(t,e,n){this.metaDataLength=t,this.offset=Se(n),this.bodyLength=Se(e)}}const Ae=Object.freeze({done:!0,value:void 0});class _u{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class mp{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class fy extends mp{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}get closed(){return this._closedPromise}cancel(t){return Mt(this,void 0,void 0,(function*(){yield this.return(t)}))}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(Ae);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return wn.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return wn.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return Mt(this,void 0,void 0,(function*(){return yield this.abort(t),Ae}))}return(t){return Mt(this,void 0,void 0,(function*(){return yield this.close(),Ae}))}read(t){return Mt(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return Mt(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(...t){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise(((t,e)=>{this.resolvers.push({resolve:t,reject:e})})):Promise.resolve(Ae)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class dy extends fy{write(t){if((t=Gt(t)).byteLength>0)return super.write(t)}toString(t=!1){return t?kl(this.toUint8Array(!0)):this.toUint8Array(!1).then(kl)}toUint8Array(t=!1){return t?rr(this._values)[0]:Mt(this,void 0,void 0,(function*(){var t,e,n,r;const i=[];let o=0;try{for(var s,a=!0,l=Bi(this);!(t=(s=yield l.next()).done);a=!0){r=s.value,a=!1;const t=r;i.push(t),o+=t.byteLength}}catch(t){e={error:t}}finally{try{!a&&!t&&(n=l.return)&&(yield n.call(l))}finally{if(e)throw e.error}}return rr(i,o)[0]}))}}class wa{constructor(t){t&&(this.source=new hy(wn.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class zi{constructor(t){t instanceof zi?this.source=t.source:t instanceof dy?this.source=new Yr(wn.fromAsyncIterable(t)):Ch(t)?this.source=new Yr(wn.fromNodeStream(t)):rc(t)?this.source=new Yr(wn.fromDOMStream(t)):Mh(t)?this.source=new Yr(wn.fromDOMStream(t.body)):Ca(t)?this.source=new Yr(wn.fromIterable(t)):(qo(t)||nc(t))&&(this.source=new Yr(wn.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class hy{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t,e="read"){return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Ae)}return(t){return Object.create(this.source.return&&this.source.return(t)||Ae)}}class Yr{constructor(t){this.source=t,this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}cancel(t){return Mt(this,void 0,void 0,(function*(){yield this.return(t)}))}get closed(){return this._closedPromise}read(t){return Mt(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return Mt(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(t){return Mt(this,arguments,void 0,(function*(t,e="read"){return yield this.source.next({cmd:e,size:t})}))}throw(t){return Mt(this,void 0,void 0,(function*(){const e=this.source.throw&&(yield this.source.throw(t))||Ae;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}return(t){return Mt(this,void 0,void 0,(function*(){const e=this.source.return&&(yield this.source.return(t))||Ae;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}}class bu extends wa{constructor(t,e){super(),this.position=0,this.buffer=Gt(t),this.size=void 0===e?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:n}=this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:n,position:r}=this;return e&&r<n?("number"!=typeof t&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(n,r+Math.min(n-r,t)),e.subarray(r,this.position)):null}readAt(t,e){const n=this.buffer,r=Math.min(this.size,t+e);return n?n.subarray(t,r):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class Sa extends zi{constructor(t,e){super(),this.position=0,this._handle=t,"number"==typeof e?this.size=e:this._pending=Mt(this,void 0,void 0,(function*(){this.size=(yield t.stat()).size,delete this._pending}))}readInt32(t){return Mt(this,void 0,void 0,(function*(){const{buffer:e,byteOffset:n}=yield this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}))}seek(t){return Mt(this,void 0,void 0,(function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size}))}read(t){return Mt(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:e,size:n,position:r}=this;if(e&&r<n){"number"!=typeof t&&(t=Number.POSITIVE_INFINITY);let i=r,o=0,s=0;const a=Math.min(n,i+Math.min(n-i,t)),l=new Uint8Array(Math.max(0,(this.position=a)-i));for(;(i+=s)<a&&(o+=s)<l.byteLength;)({bytesRead:s}=yield e.read(l,o,l.byteLength-o,i));return l}return null}))}readAt(t,e){return Mt(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:n,size:r}=this;if(n&&t+e<r){const i=Math.min(r,t+e),o=new Uint8Array(i-t);return(yield n.read(o,0,e,t)).buffer}return new Uint8Array(e)}))}close(){return Mt(this,void 0,void 0,(function*(){const t=this._handle;this._handle=null,t&&(yield t.close())}))}throw(t){return Mt(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}return(t){return Mt(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}}const my=65536;function Ei(t){return t<0&&(t=4294967295+t+1),`0x${t.toString(16)}`}const Wi=8,pc=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class pp{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),n=new Uint32Array([t.buffer[1]>>>16,65535&t.buffer[1],t.buffer[0]>>>16,65535&t.buffer[0]]);let r=e[3]*n[3];this.buffer[0]=65535&r;let i=r>>>16;return r=e[2]*n[3],i+=r,r=e[3]*n[2]>>>0,i+=r,this.buffer[0]+=i<<16,this.buffer[1]=i>>>0<r?my:0,this.buffer[1]+=i>>>16,this.buffer[1]+=e[1]*n[3]+e[2]*n[2]+e[3]*n[1],this.buffer[1]+=e[0]*n[3]+e[1]*n[2]+e[2]*n[1]+e[3]*n[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${Ei(this.buffer[1])} ${Ei(this.buffer[0])}`}}class ce extends pp{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t,e=new Uint32Array(2)){return ce.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return ce.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const n=t.length,r=new ce(e);for(let e=0;e<n;){const i=8<n-e?8:n-e,o=new ce(new Uint32Array([Number.parseInt(t.slice(e,e+i),10),0])),s=new ce(new Uint32Array([pc[i],0]));r.times(s),r.plus(o),e+=i}return r}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)ce.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){return new ce(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new ce(new Uint32Array(t.buffer)).plus(e)}}class cn extends pp{negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=0|this.buffer[1],n=0|t.buffer[1];return e<n||e===n&&this.buffer[0]<t.buffer[0]}static from(t,e=new Uint32Array(2)){return cn.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(2)){return cn.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(2)){const n=t.startsWith("-"),r=t.length,i=new cn(e);for(let e=n?1:0;e<r;){const n=8<r-e?8:r-e,o=new cn(new Uint32Array([Number.parseInt(t.slice(e,e+n),10),0])),s=new cn(new Uint32Array([pc[n],0]));i.times(s),i.plus(o),e+=n}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)cn.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){return new cn(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new cn(new Uint32Array(t.buffer)).plus(e)}}class Qn{constructor(t){this.buffer=t}high(){return new cn(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new cn(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}times(t){const e=new ce(new Uint32Array([this.buffer[3],0])),n=new ce(new Uint32Array([this.buffer[2],0])),r=new ce(new Uint32Array([this.buffer[1],0])),i=new ce(new Uint32Array([this.buffer[0],0])),o=new ce(new Uint32Array([t.buffer[3],0])),s=new ce(new Uint32Array([t.buffer[2],0])),a=new ce(new Uint32Array([t.buffer[1],0])),l=new ce(new Uint32Array([t.buffer[0],0]));let c=ce.multiply(i,l);this.buffer[0]=c.low();const u=new ce(new Uint32Array([c.high(),0]));return c=ce.multiply(r,l),u.plus(c),c=ce.multiply(i,a),u.plus(c),this.buffer[1]=u.low(),this.buffer[3]=u.lessThan(c)?1:0,this.buffer[2]=u.high(),new ce(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(ce.multiply(n,l)).plus(ce.multiply(r,a)).plus(ce.multiply(i,s)),this.buffer[3]+=ce.multiply(e,l).plus(ce.multiply(n,a)).plus(ce.multiply(r,s)).plus(ce.multiply(i,o)).low(),this}plus(t){const e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return`${Ei(this.buffer[3])} ${Ei(this.buffer[2])} ${Ei(this.buffer[1])} ${Ei(this.buffer[0])}`}static multiply(t,e){return new Qn(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new Qn(new Uint32Array(t.buffer)).plus(e)}static from(t,e=new Uint32Array(4)){return Qn.fromString("string"==typeof t?t:t.toString(),e)}static fromNumber(t,e=new Uint32Array(4)){return Qn.fromString(t.toString(),e)}static fromString(t,e=new Uint32Array(4)){const n=t.startsWith("-"),r=t.length,i=new Qn(e);for(let e=n?1:0;e<r;){const n=8<r-e?8:r-e,o=new Qn(new Uint32Array([Number.parseInt(t.slice(e,e+n),10),0,0,0])),s=new Qn(new Uint32Array([pc[n],0,0,0]));i.times(s),i.plus(o),e+=n}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(4*t.length);for(let n=-1,r=t.length;++n<r;)Qn.from(t[n],new Uint32Array(e.buffer,e.byteOffset+16*n,4));return e}}class gp extends Ht{constructor(t,e,n,r,i=Fe.V5){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=n,this.dictionaries=r,this.metadataVersion=i}visit(t){return super.visit(t instanceof Ie?t.type:t)}visitNull(t,{length:e}=this.nextFieldNode()){return zt({type:t,length:e})}visitBool(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitInt(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitFloat(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitUtf8(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeUtf8(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitLargeBinary(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDate(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitTimestamp(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitTime(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDecimal(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitList(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),children:this.visitMany(t.children)})}visitUnion(t,{length:e,nullCount:n}=this.nextFieldNode()){return this.metadataVersion<Fe.V5&&this.readNullBitmap(t,n),t.mode===dn.Sparse?this.visitSparseUnion(t,{length:e,nullCount:n}):this.visitDenseUnion(t,{length:e,nullCount:n})}visitDenseUnion(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDuration(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitFixedSizeList(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),child:this.visit(t.children[0])})}visitMap(t,{length:e,nullCount:n}=this.nextFieldNode()){return zt({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e,n=this.nextBufferRange()){return e>0&&this.readData(t,n)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t,{length:e,offset:n}=this.nextBufferRange()){return this.bytes.subarray(n,n+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class py extends gp{constructor(t,e,n,r,i){super(new Uint8Array(0),e,n,r,i),this.sources=t}readNullBitmap(t,e,{offset:n}=this.nextBufferRange()){return e<=0?new Uint8Array(0):Ml(this.sources[n])}readOffsets(t,{offset:e}=this.nextBufferRange()){return fe(Uint8Array,fe(t.OffsetArrayType,this.sources[e]))}readTypeIds(t,{offset:e}=this.nextBufferRange()){return fe(Uint8Array,fe(t.ArrayType,this.sources[e]))}readData(t,{offset:e}=this.nextBufferRange()){const{sources:n}=this;return ft.isTimestamp(t)||(ft.isInt(t)||ft.isTime(t))&&64===t.bitWidth||ft.isDuration(t)||ft.isDate(t)&&t.unit===Tn.MILLISECOND?fe(Uint8Array,cn.convertArray(n[e])):ft.isDecimal(t)?fe(Uint8Array,Qn.convertArray(n[e])):ft.isBinary(t)||ft.isLargeBinary(t)||ft.isFixedSizeBinary(t)?gy(n[e]):ft.isBool(t)?Ml(n[e]):ft.isUtf8(t)||ft.isLargeUtf8(t)?ec(n[e].join("")):fe(Uint8Array,fe(t.ArrayType,n[e].map((t=>+t))))}}function gy(t){const e=t.join(""),n=new Uint8Array(e.length/2);for(let t=0;t<e.length;t+=2)n[t>>1]=Number.parseInt(e.slice(t,t+2),16);return n}class bt extends Ht{compareSchemas(t,e){return t===e||e instanceof t.constructor&&this.compareManyFields(t.fields,e.fields)}compareManyFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every(((t,n)=>this.compareFields(t,e[n])))}compareFields(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&this.visit(t.type,e.type)}}function on(t,e){return e instanceof t.constructor}function si(t,e){return t===e||on(t,e)}function kr(t,e){return t===e||on(t,e)&&t.bitWidth===e.bitWidth&&t.isSigned===e.isSigned}function ja(t,e){return t===e||on(t,e)&&t.precision===e.precision}function _y(t,e){return t===e||on(t,e)&&t.byteWidth===e.byteWidth}function gc(t,e){return t===e||on(t,e)&&t.unit===e.unit}function os(t,e){return t===e||on(t,e)&&t.unit===e.unit&&t.timezone===e.timezone}function ss(t,e){return t===e||on(t,e)&&t.unit===e.unit&&t.bitWidth===e.bitWidth}function by(t,e){return t===e||on(t,e)&&t.children.length===e.children.length&&Rr.compareManyFields(t.children,e.children)}function yy(t,e){return t===e||on(t,e)&&t.children.length===e.children.length&&Rr.compareManyFields(t.children,e.children)}function _c(t,e){return t===e||on(t,e)&&t.mode===e.mode&&t.typeIds.every(((t,n)=>t===e.typeIds[n]))&&Rr.compareManyFields(t.children,e.children)}function vy(t,e){return t===e||on(t,e)&&t.id===e.id&&t.isOrdered===e.isOrdered&&Rr.visit(t.indices,e.indices)&&Rr.visit(t.dictionary,e.dictionary)}function bc(t,e){return t===e||on(t,e)&&t.unit===e.unit}function as(t,e){return t===e||on(t,e)&&t.unit===e.unit}function wy(t,e){return t===e||on(t,e)&&t.listSize===e.listSize&&t.children.length===e.children.length&&Rr.compareManyFields(t.children,e.children)}function Sy(t,e){return t===e||on(t,e)&&t.keysSorted===e.keysSorted&&t.children.length===e.children.length&&Rr.compareManyFields(t.children,e.children)}bt.prototype.visitNull=si,bt.prototype.visitBool=si,bt.prototype.visitInt=kr,bt.prototype.visitInt8=kr,bt.prototype.visitInt16=kr,bt.prototype.visitInt32=kr,bt.prototype.visitInt64=kr,bt.prototype.visitUint8=kr,bt.prototype.visitUint16=kr,bt.prototype.visitUint32=kr,bt.prototype.visitUint64=kr,bt.prototype.visitFloat=ja,bt.prototype.visitFloat16=ja,bt.prototype.visitFloat32=ja,bt.prototype.visitFloat64=ja,bt.prototype.visitUtf8=si,bt.prototype.visitLargeUtf8=si,bt.prototype.visitBinary=si,bt.prototype.visitLargeBinary=si,bt.prototype.visitFixedSizeBinary=_y,bt.prototype.visitDate=gc,bt.prototype.visitDateDay=gc,bt.prototype.visitDateMillisecond=gc,bt.prototype.visitTimestamp=os,bt.prototype.visitTimestampSecond=os,bt.prototype.visitTimestampMillisecond=os,bt.prototype.visitTimestampMicrosecond=os,bt.prototype.visitTimestampNanosecond=os,bt.prototype.visitTime=ss,bt.prototype.visitTimeSecond=ss,bt.prototype.visitTimeMillisecond=ss,bt.prototype.visitTimeMicrosecond=ss,bt.prototype.visitTimeNanosecond=ss,bt.prototype.visitDecimal=si,bt.prototype.visitList=by,bt.prototype.visitStruct=yy,bt.prototype.visitUnion=_c,bt.prototype.visitDenseUnion=_c,bt.prototype.visitSparseUnion=_c,bt.prototype.visitDictionary=vy,bt.prototype.visitInterval=bc,bt.prototype.visitIntervalDayTime=bc,bt.prototype.visitIntervalYearMonth=bc,bt.prototype.visitDuration=as,bt.prototype.visitDurationSecond=as,bt.prototype.visitDurationMillisecond=as,bt.prototype.visitDurationMicrosecond=as,bt.prototype.visitDurationNanosecond=as,bt.prototype.visitFixedSizeList=wy,bt.prototype.visitMap=Sy;const Rr=new bt;function Iy(t,e){return Rr.compareSchemas(t,e)}function nl(t,e){return Oy(t,e.map((t=>t.data.concat())))}function Oy(t,e){const n=[...t.fields],r=[],i={numBatches:e.reduce(((t,e)=>Math.max(t,e.length)),0)};let o=0,s=0,a=-1;const l=e.length;let c,u=[];for(;i.numBatches-- >0;){for(s=Number.POSITIVE_INFINITY,a=-1;++a<l;)u[a]=c=e[a].shift(),s=Math.min(s,c?c.length:s);Number.isFinite(s)&&(u=ky(n,s,u,e,i),s>0&&(r[o++]=zt({type:new nn(n),length:s,nullCount:0,children:u.slice()})))}return[t=t.assign(n),r.map((e=>new Ln(t,e)))]}function ky(t,e,n,r,i){var o;const s=(e+63&-64)>>3;for(let a=-1,l=r.length;++a<l;){const l=n[a],c=null==l?void 0:l.length;if(c>=e)c===e?n[a]=l:(n[a]=l.slice(0,e),i.numBatches=Math.max(i.numBatches,r[a].unshift(l.slice(e,c-e))));else{const r=t[a];t[a]=r.clone({nullable:!0}),n[a]=null!==(o=null==l?void 0:l._changeLengthAndBackfillNullBitmap(e))&&void 0!==o?o:zt({type:r.type,length:e,nullCount:e,nullBitmap:new Uint8Array(s)})}}return n}var _p,bp;class On{constructor(...t){var e,n;if(0===t.length)return this.batches=[],this.schema=new se([]),this._offsets=[0],this;let r,i;t[0]instanceof se&&(r=t.shift()),t.at(-1)instanceof Uint32Array&&(i=t.pop());const o=t=>{if(t){if(t instanceof Ln)return[t];if(t instanceof On)return t.batches;if(t instanceof le){if(t.type instanceof nn)return[new Ln(new se(t.type.children),t)]}else{if(Array.isArray(t))return t.flatMap((t=>o(t)));if("function"==typeof t[Symbol.iterator])return[...t].flatMap((t=>o(t)));if("object"==typeof t){const e=Object.keys(t),n=e.map((e=>new de([t[e]]))),i=r??new se(e.map(((t,e)=>new Ie(String(t),n[e].type,n[e].nullable)))),[,o]=nl(i,n);return 0===o.length?[new Ln(t)]:o}}}return[]},s=t.flatMap((t=>o(t)));if(r=null!==(n=r??(null===(e=s[0])||void 0===e?void 0:e.schema))&&void 0!==n?n:new se([]),!(r instanceof se))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const t of s){if(!(t instanceof Ln))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!Iy(r,t.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=r,this.batches=s,this._offsets=i??sp(this.data)}get data(){return this.batches.map((({data:t})=>t))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce(((t,e)=>t+e.length),0)}get nullCount(){return-1===this._nullCount&&(this._nullCount=op(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}at(t){return this.get(cc(t,this.numRows))}set(t,e){}indexOf(t,e){return-1}[Symbol.iterator](){return this.batches.length>0?hc.visit(new de(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return`[\n  ${this.toArray().join(",\n  ")}\n]`}concat(...t){const e=this.schema,n=this.data.concat(t.flatMap((({data:t})=>t)));return new On(e,n.map((t=>new Ln(e,t))))}slice(t,e){const n=this.schema;[t,e]=rp({length:this.numRows},t,e);const r=ap(this.data,this._offsets,t,e);return new On(n,r.map((t=>new Ln(n,t))))}getChild(t){return this.getChildAt(this.schema.fields.findIndex((e=>e.name===t)))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const e=this.data.map((e=>e.children[t]));if(0===e.length){const{type:n}=this.schema.fields[t],r=zt({type:n,length:0,nullCount:0});e.push(r._changeLengthAndBackfillNullBitmap(this.numRows))}return new de(e)}return null}setChild(t,e){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((e=>e.name===t)),e)}setChildAt(t,e){let n=this.schema,r=[...this.batches];if(t>-1&&t<this.numCols){e||(e=new de([zt({type:new Lr,length:this.numRows})]));const i=n.fields.slice(),o=i[t].clone({type:e.type}),s=this.schema.fields.map(((t,e)=>this.getChildAt(e)));[i[t],s[t]]=[o,e],[n,r]=nl(n,s)}return new On(n,r)}select(t){const e=this.schema.fields.reduce(((t,e,n)=>t.set(e.name,n)),new Map);return this.selectAt(t.map((t=>e.get(t))).filter((t=>t>-1)))}selectAt(t){const e=this.schema.selectAt(t),n=this.batches.map((e=>e.selectAt(t)));return new On(e,n)}assign(t){const e=this.schema.fields,[n,r]=t.schema.fields.reduce(((t,n,r)=>{const[i,o]=t,s=e.findIndex((t=>t.name===n.name));return~s?o[s]=r:i.push(r),t}),[[],[]]),i=this.schema.assign(t.schema),o=[...e.map(((t,e)=>[e,r[e]])).map((([e,n])=>void 0===n?this.getChildAt(e):t.getChildAt(n))),...n.map((e=>t.getChildAt(e)))].filter(Boolean);return new On(...nl(i,o))}}_p=Symbol.toStringTag,On[_p]=(t=>(t.schema=null,t.batches=[],t._offsets=new Uint32Array([0]),t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,t.isValid=ba(dc),t.get=ba(hn.getVisitFn(A.Struct)),t.set=lp(Bn.getVisitFn(A.Struct)),t.indexOf=cp(ya.getVisitFn(A.Struct)),"Table"))(On.prototype);let Ln=class t{constructor(...t){switch(t.length){case 2:if([this.schema]=t,!(this.schema instanceof se))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=zt({nullCount:0,type:new nn(this.schema.fields),children:this.schema.fields.map((t=>zt({type:t.type,nullCount:0})))})]=t,!(this.data instanceof le))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=yu(this.schema,this.data.children);break;case 1:{const[e]=t,{fields:n,children:r,length:i}=Object.keys(e).reduce(((t,n,r)=>(t.children[r]=e[n],t.length=Math.max(t.length,e[n].length),t.fields[r]=Ie.new({name:n,type:e[n].type,nullable:!0}),t)),{length:0,fields:new Array,children:new Array}),o=new se(n),s=zt({type:new nn(n),length:i,children:r,nullCount:0});[this.schema,this.data]=yu(o,s.children,i);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=yp(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return hn.visit(this.data,t)}at(t){return this.get(cc(t,this.numRows))}set(t,e){return Bn.visit(this.data,t,e)}indexOf(t,e){return ya.visit(this.data,t,e)}[Symbol.iterator](){return hc.visit(new de([this.data]))}toArray(){return[...this]}concat(...t){return new On(this.schema,[this,...t])}slice(e,n){const[r]=new de([this.data]).slice(e,n).data;return new t(this.schema,r)}getChild(t){var e;return this.getChildAt(null===(e=this.schema.fields)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new de([this.data.children[t]]):null}setChild(t,e){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((e=>e.name===t)),e)}setChildAt(e,n){let r=this.schema,i=this.data;if(e>-1&&e<this.numCols){n||(n=new de([zt({type:new Lr,length:this.numRows})]));const t=r.fields.slice(),o=i.children.slice(),s=t[e].clone({type:n.type});[t[e],o[e]]=[s,n.data[0]],r=new se(t,new Map(this.schema.metadata)),i=zt({type:new nn(t),children:o})}return new t(r,i)}select(e){const n=this.schema.select(e),r=new nn(n.fields),i=[];for(const t of e){const e=this.schema.fields.findIndex((e=>e.name===t));~e&&(i[e]=this.data.children[e])}return new t(n,zt({type:r,length:this.numRows,children:i}))}selectAt(e){const n=this.schema.selectAt(e),r=e.map((t=>this.data.children[t])).filter(Boolean),i=zt({type:new nn(n.fields),length:this.numRows,children:r});return new t(n,i)}};function yu(t,e,n=e.reduce(((t,e)=>Math.max(t,e.length)),0)){var r;const i=[...t.fields],o=[...e],s=(n+63&-64)>>3;for(const[a,l]of t.fields.entries()){const t=e[a];(!t||t.length!==n)&&(i[a]=l.clone({nullable:!0}),o[a]=null!==(r=null==t?void 0:t._changeLengthAndBackfillNullBitmap(n))&&void 0!==r?r:zt({type:l.type,length:n,nullCount:n,nullBitmap:new Uint8Array(s)}))}return[t.assign(i),zt({type:new nn(i),length:n,children:o})]}function yp(t,e,n=new Map){var r,i;if((null!==(r=null==t?void 0:t.length)&&void 0!==r?r:0)>0&&(null==t?void 0:t.length)===(null==e?void 0:e.length))for(let r=-1,o=t.length;++r<o;){const{type:o}=t[r],s=e[r];for(const t of[s,...(null===(i=null==s?void 0:s.dictionary)||void 0===i?void 0:i.data)||[]])yp(o.children,null==t?void 0:t.children,n);if(ft.isDictionary(o)){const{id:t}=o;if(n.has(t)){if(n.get(t)!==s.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else null!=s&&s.dictionary&&n.set(t,s.dictionary)}}return n}bp=Symbol.toStringTag,Ln[bp]=(t=>(t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(Ln.prototype);class vp extends Ln{constructor(t){const e=t.fields.map((t=>zt({type:t.type})));super(t,zt({type:new nn(t.fields),nullCount:0,children:e}))}}let Tr=class t{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}static getSizePrefixedRootAsMessage(e,n){return e.setPosition(e.position()+4),(n||new t).__init(e.readInt32(e.position())+e.position(),e)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Fe.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):oe.NONE}header(t){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):BigInt("0")}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,12);return n?(e||new Le).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Fe.V1)}static addHeaderType(t,e){t.addFieldInt8(1,e,oe.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,BigInt("0"))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,e){t.finish(e)}static finishSizePrefixedMessageBuffer(t,e){t.finish(e,void 0,!0)}static createMessage(e,n,r,i,o,s){return t.startMessage(e),t.addVersion(e,n),t.addHeaderType(e,r),t.addHeader(e,i),t.addBodyLength(e,o),t.addCustomMetadata(e,s),t.endMessage(e)}};class Dy extends Ht{visit(t,e){return null==t||null==e?void 0:super.visit(t,e)}visitNull(t,e){return cu.startNull(e),cu.endNull(e)}visitInt(t,e){return gn.startInt(e),gn.addBitWidth(e,t.bitWidth),gn.addIsSigned(e,t.isSigned),gn.endInt(e)}visitFloat(t,e){return Xn.startFloatingPoint(e),Xn.addPrecision(e,t.precision),Xn.endFloatingPoint(e)}visitBinary(t,e){return iu.startBinary(e),iu.endBinary(e)}visitLargeBinary(t,e){return su.startLargeBinary(e),su.endLargeBinary(e)}visitBool(t,e){return ou.startBool(e),ou.endBool(e)}visitUtf8(t,e){return uu.startUtf8(e),uu.endUtf8(e)}visitLargeUtf8(t,e){return au.startLargeUtf8(e),au.endLargeUtf8(e)}visitDecimal(t,e){return yi.startDecimal(e),yi.addScale(e,t.scale),yi.addPrecision(e,t.precision),yi.addBitWidth(e,t.bitWidth),yi.endDecimal(e)}visitDate(t,e){return Rs.startDate(e),Rs.addUnit(e,t.unit),Rs.endDate(e)}visitTime(t,e){return Sn.startTime(e),Sn.addUnit(e,t.unit),Sn.addBitWidth(e,t.bitWidth),Sn.endTime(e)}visitTimestamp(t,e){const n=t.timezone&&e.createString(t.timezone)||void 0;return In.startTimestamp(e),In.addUnit(e,t.unit),void 0!==n&&In.addTimezone(e,n),In.endTimestamp(e)}visitInterval(t,e){return Zn.startInterval(e),Zn.addUnit(e,t.unit),Zn.endInterval(e)}visitDuration(t,e){return Us.startDuration(e),Us.addUnit(e,t.unit),Us.endDuration(e)}visitList(t,e){return lu.startList(e),lu.endList(e)}visitStruct(t,e){return ri.startStruct_(e),ri.endStruct_(e)}visitUnion(t,e){un.startTypeIdsVector(e,t.typeIds.length);const n=un.createTypeIdsVector(e,t.typeIds);return un.startUnion(e),un.addMode(e,t.mode),un.addTypeIds(e,n),un.endUnion(e)}visitDictionary(t,e){const n=this.visit(t.indices,e);return hr.startDictionaryEncoding(e),hr.addId(e,BigInt(t.id)),hr.addIsOrdered(e,t.isOrdered),void 0!==n&&hr.addIndexType(e,n),hr.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return Vs.startFixedSizeBinary(e),Vs.addByteWidth(e,t.byteWidth),Vs.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return zs.startFixedSizeList(e),zs.addListSize(e,t.listSize),zs.endFixedSizeList(e)}visitMap(t,e){return Ws.startMap(e),Ws.addKeysSorted(e,t.keysSorted),Ws.endMap(e)}}const rl=new Dy;function Ey(t,e=new Map){return new se(Ty(t,e),xs(t.metadata),e)}function wp(t){return new Vn(t.count,Sp(t.columns),Ip(t.columns))}function Ay(t){return new wr(wp(t.data),t.id,t.isDelta)}function Ty(t,e){return(t.fields||[]).filter(Boolean).map((t=>Ie.fromJSON(t,e)))}function vu(t,e){return(t.children||[]).filter(Boolean).map((t=>Ie.fromJSON(t,e)))}function Sp(t){return(t||[]).reduce(((t,e)=>[...t,new Ki(e.count,By(e.VALIDITY)),...Sp(e.children)]),[])}function Ip(t,e=[]){for(let n=-1,r=(t||[]).length;++n<r;){const r=t[n];r.VALIDITY&&e.push(new mr(e.length,r.VALIDITY.length)),r.TYPE_ID&&e.push(new mr(e.length,r.TYPE_ID.length)),r.OFFSET&&e.push(new mr(e.length,r.OFFSET.length)),r.DATA&&e.push(new mr(e.length,r.DATA.length)),e=Ip(r.children,e)}return e}function By(t){return(t||[]).reduce(((t,e)=>t+ +(0===e)),0)}function Py(t,e){let n,r,i,o,s,a;return e&&(o=t.dictionary)?e.has(n=o.id)?(r=(r=o.indexType)?wu(r):new Qo,a=new Ui(e.get(n),r,n,o.isOrdered),i=new Ie(t.name,a,t.nullable,xs(t.metadata))):(r=(r=o.indexType)?wu(r):new Qo,e.set(n,s=Su(t,vu(t,e))),a=new Ui(s,r,n,o.isOrdered),i=new Ie(t.name,a,t.nullable,xs(t.metadata))):(s=Su(t,vu(t,e)),i=new Ie(t.name,s,t.nullable,xs(t.metadata))),i||null}function xs(t=[]){return new Map(t.map((({key:t,value:e})=>[t,e])))}function wu(t){return new oi(t.isSigned,t.bitWidth)}function Su(t,e){const n=t.type.name;switch(n){case"NONE":case"null":return new Lr;case"binary":return new na;case"largebinary":return new ra;case"utf8":return new ia;case"largeutf8":return new oa;case"bool":return new sa;case"list":return new ha((e||[])[0]);case"struct":case"struct_":return new nn(e||[])}switch(n){case"int":{const e=t.type;return new oi(e.isSigned,e.bitWidth)}case"floatingpoint":{const e=t.type;return new ea(Je[e.precision])}case"decimal":{const e=t.type;return new aa(e.scale,e.precision,e.bitWidth)}case"date":{const e=t.type;return new la(Tn[e.unit])}case"time":{const e=t.type;return new ca(yt[e.unit],e.bitWidth)}case"timestamp":{const e=t.type;return new ua(yt[e.unit],e.timezone)}case"interval":{const e=t.type;return new fa(ir[e.unit])}case"duration":{const e=t.type;return new da(yt[e.unit])}case"union":{const n=t.type,[r,...i]=(n.mode+"").toLowerCase(),o=r.toUpperCase()+i.join("");return new ma(dn[o],n.typeIds||[],e||[])}case"fixedsizebinary":{const e=t.type;return new pa(e.byteWidth)}case"fixedsizelist":{const n=t.type;return new ga(n.listSize,(e||[])[0])}case"map":{const n=t.type;return new _a((e||[])[0],n.keysSorted)}}throw new Error(`Unrecognized type: "${n}"`)}var My=Lh,Ny=Li;class Rn{static fromJSON(t,e){const n=new Rn(0,Fe.V5,e);return n._createHeader=Cy(t,e),n}static decode(t){t=new Ny(Gt(t));const e=Tr.getRootAsMessage(t),n=e.bodyLength(),r=e.version(),i=e.headerType(),o=new Rn(n,r,i);return o._createHeader=Fy(e,i),o}static encode(t){const e=new My;let n=-1;return t.isSchema()?n=se.encode(e,t.header()):t.isRecordBatch()?n=Vn.encode(e,t.header()):t.isDictionaryBatch()&&(n=wr.encode(e,t.header())),Tr.startMessage(e),Tr.addVersion(e,Fe.V5),Tr.addHeader(e,n),Tr.addHeaderType(e,t.headerType),Tr.addBodyLength(e,BigInt(t.bodyLength)),Tr.finishMessageBuffer(e,Tr.endMessage(e)),e.asUint8Array()}static from(t,e=0){if(t instanceof se)return new Rn(0,Fe.V5,oe.Schema,t);if(t instanceof Vn)return new Rn(e,Fe.V5,oe.RecordBatch,t);if(t instanceof wr)return new Rn(e,Fe.V5,oe.DictionaryBatch,t);throw new Error(`Unrecognized Message header: ${t}`)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===oe.Schema}isRecordBatch(){return this.headerType===oe.RecordBatch}isDictionaryBatch(){return this.headerType===oe.DictionaryBatch}constructor(t,e,n,r){this._version=e,this._headerType=n,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength=Se(t)}}class Vn{get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}constructor(t,e,n){this._nodes=e,this._buffers=n,this._length=Se(t)}}class wr{get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}constructor(t,e,n=!1){this._data=t,this._isDelta=n,this._id=Se(e)}}class mr{constructor(t,e){this.offset=Se(t),this.length=Se(e)}}class Ki{constructor(t,e){this.length=Se(t),this.nullCount=Se(e)}}function Cy(t,e){return()=>{switch(e){case oe.Schema:return se.fromJSON(t);case oe.RecordBatch:return Vn.fromJSON(t);case oe.DictionaryBatch:return wr.fromJSON(t)}throw new Error(`Unrecognized Message type: { name: ${oe[e]}, type: ${e} }`)}}function Fy(t,e){return()=>{switch(e){case oe.Schema:return se.decode(t.header(new Jn),new Map,t.version());case oe.RecordBatch:return Vn.decode(t.header(new ur),t.version());case oe.DictionaryBatch:return wr.decode(t.header(new _i),t.version())}throw new Error(`Unrecognized Message type: { name: ${oe[e]}, type: ${e} }`)}}function jy(t,e=new Map,n=Fe.V5){const r=Hy(t,e);return new se(r,qs(t),e,n)}function Ly(t,e=Fe.V5){if(null!==t.compression())throw new Error("Record batch compression not implemented");return new Vn(t.length(),zy(t),Wy(t,e))}function Ry(t,e=Fe.V5){return new wr(Vn.decode(t.data(),e),t.id(),t.isDelta())}function Uy(t){return new mr(t.offset(),t.length())}function Vy(t){return new Ki(t.length(),t.nullCount())}function zy(t){const e=[];for(let n,r=-1,i=-1,o=t.nodesLength();++r<o;)(n=t.nodes(r))&&(e[++i]=Ki.decode(n));return e}function Wy(t,e){const n=[];for(let r,i=-1,o=-1,s=t.buffersLength();++i<s;)(r=t.buffers(i))&&(e<Fe.V4&&(r.bb_pos+=8*(i+1)),n[++o]=mr.decode(r));return n}function Hy(t,e){const n=[];for(let r,i=-1,o=-1,s=t.fieldsLength();++i<s;)(r=t.fields(i))&&(n[++o]=Ie.decode(r,e));return n}function Iu(t,e){const n=[];for(let r,i=-1,o=-1,s=t.childrenLength();++i<s;)(r=t.children(i))&&(n[++o]=Ie.decode(r,e));return n}function xy(t,e){let n,r,i,o,s,a;return e&&(a=t.dictionary())?e.has(n=Se(a.id()))?(o=(o=a.indexType())?Ou(o):new Qo,s=new Ui(e.get(n),o,n,a.isOrdered()),r=new Ie(t.name(),s,t.nullable(),qs(t))):(o=(o=a.indexType())?Ou(o):new Qo,e.set(n,i=ku(t,Iu(t,e))),s=new Ui(i,o,n,a.isOrdered()),r=new Ie(t.name(),s,t.nullable(),qs(t))):(i=ku(t,Iu(t,e)),r=new Ie(t.name(),i,t.nullable(),qs(t))),r||null}function qs(t){const e=new Map;if(t)for(let n,r,i=-1,o=Math.trunc(t.customMetadataLength());++i<o;)(n=t.customMetadata(i))&&null!=(r=n.key())&&e.set(r,n.value());return e}function Ou(t){return new oi(t.isSigned(),t.bitWidth())}function ku(t,e){const n=t.typeType();switch(n){case pe.NONE:case pe.Null:return new Lr;case pe.Binary:return new na;case pe.LargeBinary:return new ra;case pe.Utf8:return new ia;case pe.LargeUtf8:return new oa;case pe.Bool:return new sa;case pe.List:return new ha((e||[])[0]);case pe.Struct_:return new nn(e||[])}switch(n){case pe.Int:{const e=t.type(new gn);return new oi(e.isSigned(),e.bitWidth())}case pe.FloatingPoint:{const e=t.type(new Xn);return new ea(e.precision())}case pe.Decimal:{const e=t.type(new yi);return new aa(e.scale(),e.precision(),e.bitWidth())}case pe.Date:{const e=t.type(new Rs);return new la(e.unit())}case pe.Time:{const e=t.type(new Sn);return new ca(e.unit(),e.bitWidth())}case pe.Timestamp:{const e=t.type(new In);return new ua(e.unit(),e.timezone())}case pe.Interval:{const e=t.type(new Zn);return new fa(e.unit())}case pe.Duration:{const e=t.type(new Us);return new da(e.unit())}case pe.Union:{const n=t.type(new un);return new ma(n.mode(),n.typeIdsArray()||[],e||[])}case pe.FixedSizeBinary:{const e=t.type(new Vs);return new pa(e.byteWidth())}case pe.FixedSizeList:{const n=t.type(new zs);return new ga(n.listSize(),(e||[])[0])}case pe.Map:{const n=t.type(new Ws);return new _a((e||[])[0],n.keysSorted())}}throw new Error(`Unrecognized type: "${pe[n]}" (${n})`)}function qy(t,e){const n=e.fields.map((e=>Ie.encode(t,e)));Jn.startFieldsVector(t,n.length);const r=Jn.createFieldsVector(t,n),i=e.metadata&&e.metadata.size>0?Jn.createCustomMetadataVector(t,[...e.metadata].map((([e,n])=>{const r=t.createString(`${e}`),i=t.createString(`${n}`);return Le.startKeyValue(t),Le.addKey(t,r),Le.addValue(t,i),Le.endKeyValue(t)}))):-1;return Jn.startSchema(t),Jn.addFields(t,r),Jn.addEndianness(t,Xy?Ri.Little:Ri.Big),-1!==i&&Jn.addCustomMetadata(t,i),Jn.endSchema(t)}function Yy(t,e){let n=-1,r=-1,i=-1;const o=e.type;let s=e.typeId;ft.isDictionary(o)?(s=o.dictionary.typeId,i=rl.visit(o,t),r=rl.visit(o.dictionary,t)):r=rl.visit(o,t);const a=(o.children||[]).map((e=>Ie.encode(t,e))),l=vn.createChildrenVector(t,a),c=e.metadata&&e.metadata.size>0?vn.createCustomMetadataVector(t,[...e.metadata].map((([e,n])=>{const r=t.createString(`${e}`),i=t.createString(`${n}`);return Le.startKeyValue(t),Le.addKey(t,r),Le.addValue(t,i),Le.endKeyValue(t)}))):-1;return e.name&&(n=t.createString(e.name)),vn.startField(t),vn.addType(t,r),vn.addTypeType(t,s),vn.addChildren(t,l),vn.addNullable(t,!!e.nullable),-1!==n&&vn.addName(t,n),-1!==i&&vn.addDictionary(t,i),-1!==c&&vn.addCustomMetadata(t,c),vn.endField(t)}function Ky(t,e){const n=e.nodes||[],r=e.buffers||[];ur.startNodesVector(t,n.length);for(const e of n.slice().reverse())Ki.encode(t,e);const i=t.endVector();ur.startBuffersVector(t,r.length);for(const e of r.slice().reverse())mr.encode(t,e);const o=t.endVector();return ur.startRecordBatch(t),ur.addLength(t,BigInt(e.length)),ur.addNodes(t,i),ur.addBuffers(t,o),ur.endRecordBatch(t)}function Jy(t,e){const n=Vn.encode(t,e.data);return _i.startDictionaryBatch(t),_i.addId(t,BigInt(e.id)),_i.addIsDelta(t,e.isDelta),_i.addData(t,n),_i.endDictionaryBatch(t)}function Qy(t,e){return Vh.createFieldNode(t,BigInt(e.length),BigInt(e.nullCount))}function Gy(t,e){return Uh.createBuffer(t,BigInt(e.offset),BigInt(e.length))}Ie.encode=Yy,Ie.decode=xy,Ie.fromJSON=Py,se.encode=qy,se.decode=jy,se.fromJSON=Ey,Vn.encode=Ky,Vn.decode=Ly,Vn.fromJSON=wp,wr.encode=Jy,wr.decode=Ry,wr.fromJSON=Ay,Ki.encode=Qy,Ki.decode=Vy,mr.encode=Gy,mr.decode=Uy;const Xy=(()=>{const t=new ArrayBuffer(2);return new DataView(t).setInt16(0,256,!0),256===new Int16Array(t)[0]})(),yc=t=>`Expected ${oe[t]} Message in stream, but was null or length 0.`,vc=t=>`Header pointer of flatbuffer-encoded ${oe[t]} Message is null or length 0.`,Op=(t,e)=>`Expected to read ${t} metadata bytes, but only read ${e}.`,kp=(t,e)=>`Expected to read ${t} bytes for message body, but only read ${e}.`;class Dp{constructor(t){this.source=t instanceof wa?t:new wa(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||-1===t.value&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Ae:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(yc(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=Gt(this.source.read(t));if(e.byteLength<t)throw new Error(kp(t,e.byteLength));return e.byteOffset%8==0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(t=!1){const e=oe.Schema,n=this.readMessage(e),r=null==n?void 0:n.header();if(t&&!r)throw new Error(vc(e));return r}readMetadataLength(){const t=this.source.read(La),e=t&&new Li(t),n=(null==e?void 0:e.readInt32(0))||0;return{done:0===n,value:n}}readMetadata(t){const e=this.source.read(t);if(!e)return Ae;if(e.byteLength<t)throw new Error(Op(t,e.byteLength));return{done:!1,value:Rn.decode(e)}}}class Zy{constructor(t,e){this.source=t instanceof zi?t:Ph(t)?new Sa(t,e):new zi(t)}[Symbol.asyncIterator](){return this}next(){return Mt(this,void 0,void 0,(function*(){let t;return(t=yield this.readMetadataLength()).done||-1===t.value&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?Ae:t}))}throw(t){return Mt(this,void 0,void 0,(function*(){return yield this.source.throw(t)}))}return(t){return Mt(this,void 0,void 0,(function*(){return yield this.source.return(t)}))}readMessage(t){return Mt(this,void 0,void 0,(function*(){let e;if((e=yield this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(yc(t));return e.value}))}readMessageBody(t){return Mt(this,void 0,void 0,(function*(){if(t<=0)return new Uint8Array(0);const e=Gt(yield this.source.read(t));if(e.byteLength<t)throw new Error(kp(t,e.byteLength));return e.byteOffset%8==0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}))}readSchema(){return Mt(this,arguments,void 0,(function*(t=!1){const e=oe.Schema,n=yield this.readMessage(e),r=null==n?void 0:n.header();if(t&&!r)throw new Error(vc(e));return r}))}readMetadataLength(){return Mt(this,void 0,void 0,(function*(){const t=yield this.source.read(La),e=t&&new Li(t),n=(null==e?void 0:e.readInt32(0))||0;return{done:0===n,value:n}}))}readMetadata(t){return Mt(this,void 0,void 0,(function*(){const e=yield this.source.read(t);if(!e)return Ae;if(e.byteLength<t)throw new Error(Op(t,e.byteLength));return{done:!1,value:Rn.decode(e)}}))}}class $y extends Dp{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof _u?t:new _u(t)}next(){const{_json:t}=this;if(!this._schema)return this._schema=!0,{done:!1,value:Rn.fromJSON(t.schema,oe.Schema)};if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];return this._body=e.data.columns,{done:!1,value:Rn.fromJSON(e,oe.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];return this._body=e.columns,{done:!1,value:Rn.fromJSON(e,oe.RecordBatch)}}return this._body=[],Ae}readMessageBody(t){return function t(e){return(e||[]).reduce(((e,n)=>[...e,...n.VALIDITY&&[n.VALIDITY]||[],...n.TYPE_ID&&[n.TYPE_ID]||[],...n.OFFSET&&[n.OFFSET]||[],...n.DATA&&[n.DATA]||[],...t(n.children)]),[])}(this._body)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(yc(t));return e.value}readSchema(){const t=oe.Schema,e=this.readMessage(t),n=null==e?void 0:e.header();if(!e||!n)throw new Error(vc(t));return n}}const La=4,jl="ARROW1",Ia=new Uint8Array(6);for(let t=0;t<6;t+=1)Ia[t]=jl.codePointAt(t);function wc(t,e=0){for(let n=-1,r=Ia.length;++n<r;)if(Ia[n]!==t[e+n])return!1;return!0}const ls=Ia.length,Ep=ls+La,tv=2*ls+La;class gr extends mp{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return qo(e)?e.then((()=>this)):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return wn.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return wn.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof gr?t:Dl(t)?iv(t):Ph(t)?av(t):qo(t)?Mt(this,void 0,void 0,(function*(){return yield gr.from(yield t)})):Mh(t)||rc(t)||Ch(t)||nc(t)?sv(new zi(t)):ov(new wa(t))}static readAll(t){return t instanceof gr?t.isSync()?Du(t):Eu(t):Dl(t)||ArrayBuffer.isView(t)||Ca(t)||Bh(t)?Du(t):Eu(t)}}class Oa extends gr{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return $n(this,arguments,(function*(){yield xt(yield*js(Bi(this[Symbol.iterator]())))}))}}class ka extends gr{constructor(t){super(t),this._impl=t}readAll(){return Mt(this,void 0,void 0,(function*(){var t,e,n,r;const i=new Array;try{for(var o,s=!0,a=Bi(this);!(t=(o=yield a.next()).done);s=!0){r=o.value,s=!1;const t=r;i.push(t)}}catch(t){e={error:t}}finally{try{!s&&!t&&(n=a.return)&&(yield n.call(a))}finally{if(e)throw e.error}}return i}))}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class Ap extends Oa{constructor(t){super(t),this._impl=t}}class ev extends ka{constructor(t){super(t),this._impl=t}}class Tp{get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}constructor(t=new Map){this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){const n=this._loadVectors(t,e,this.schema.fields),r=zt({type:new nn(this.schema.fields),length:t.length,children:n});return new Ln(this.schema,r)}_loadDictionaryBatch(t,e){const{id:n,isDelta:r}=t,{dictionaries:i,schema:o}=this,s=i.get(n),a=o.dictionaries.get(n),l=this._loadVectors(t.data,e,[a]);return(s&&r?s.concat(new de(l)):new de(l)).memoize()}_loadVectors(t,e,n){return new gp(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(n)}}class Da extends Tp{constructor(t,e){super(e),this._reader=Dl(t)?new $y(this._handle=t):new Dp(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=Pp(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Ae}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Ae}next(){if(this.closed)return Ae;let t;const{_reader:e}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new vp(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class Ea extends Tp{constructor(t,e){super(e),this._reader=new Zy(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return Mt(this,void 0,void 0,(function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}))}open(t){return Mt(this,void 0,void 0,(function*(){return this.closed||(this.autoDestroy=Pp(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this}))}throw(t){return Mt(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):Ae}))}return(t){return Mt(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):Ae}))}next(){return Mt(this,void 0,void 0,(function*(){if(this.closed)return Ae;let t;const{_reader:e}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=yield e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=yield e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new vp(this.schema)}):yield this.return()}))}_readNextMessageAndValidate(t){return Mt(this,void 0,void 0,(function*(){return yield this._reader.readMessage(t)}))}}class Bp extends Da{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,e){super(t instanceof bu?t:new bu(t),e)}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var e;if(this.closed)return null;this._footer||this.open();const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(n&&this._handle.seek(n.offset)){const t=this._reader.readMessage(oe.RecordBatch);if(null!=t&&t.isRecordBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,n)}}return null}_readDictionaryBatch(t){var e;const n=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(n&&this._handle.seek(n.offset)){const t=this._reader.readMessage(oe.DictionaryBatch);if(null!=t&&t.isDictionaryBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}_readFooter(){const{_handle:t}=this,e=t.size-Ep,n=t.readInt32(e),r=t.readAt(e-n,n);return mc.decode(r)}_readNextMessageAndValidate(t){var e;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(this._recordBatchIndex);if(n&&this._handle.seek(n.offset))return this._reader.readMessage(t)}return null}}class nv extends Ea{get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}constructor(t,...e){const n="number"!=typeof e[0]?e.shift():void 0,r=e[0]instanceof Map?e.shift():void 0;super(t instanceof Sa?t:new Sa(t,n),r)}isFile(){return!0}isAsync(){return!0}open(t){const e=Object.create(null,{open:{get:()=>super.open}});return Mt(this,void 0,void 0,(function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield e.open.call(this,t)}))}readRecordBatch(t){return Mt(this,void 0,void 0,(function*(){var e;if(this.closed)return null;this._footer||(yield this.open());const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(n&&(yield this._handle.seek(n.offset))){const t=yield this._reader.readMessage(oe.RecordBatch);if(null!=t&&t.isRecordBatch()){const e=t.header(),n=yield this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,n)}}return null}))}_readDictionaryBatch(t){return Mt(this,void 0,void 0,(function*(){var e;const n=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(n&&(yield this._handle.seek(n.offset))){const t=yield this._reader.readMessage(oe.DictionaryBatch);if(null!=t&&t.isDictionaryBatch()){const e=t.header(),n=yield this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}))}_readFooter(){return Mt(this,void 0,void 0,(function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const e=t.size-Ep,n=yield t.readInt32(e),r=yield t.readAt(e-n,n);return mc.decode(r)}))}_readNextMessageAndValidate(t){return Mt(this,void 0,void 0,(function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&(yield this._handle.seek(e.offset)))return yield this._reader.readMessage(t)}return null}))}}class rv extends Da{constructor(t,e){super(t,e)}_loadVectors(t,e,n){return new py(e,t.nodes,t.buffers,this.dictionaries,this.schema.metadataVersion).visitMany(n)}}function Pp(t,e){return e&&"boolean"==typeof e.autoDestroy?e.autoDestroy:t.autoDestroy}function*Du(t){const e=gr.from(t);try{if(!e.open({autoDestroy:!1}).closed)do{yield e}while(!e.reset().open().closed)}finally{e.cancel()}}function Eu(t){return $n(this,arguments,(function*(){const e=yield xt(gr.from(t));try{if(!(yield xt(e.open({autoDestroy:!1}))).closed)do{yield yield xt(e)}while(!(yield xt(e.reset().open())).closed)}finally{yield xt(e.cancel())}}))}function iv(t){return new Oa(new rv(t))}function ov(t){const e=t.peek(ls+7&-8);return e&&e.byteLength>=4?wc(e)?new Ap(new Bp(t.read())):new Oa(new Da(t)):new Oa(new Da(function*(){}()))}function sv(t){return Mt(this,void 0,void 0,(function*(){const e=yield t.peek(ls+7&-8);return e&&e.byteLength>=4?wc(e)?new Ap(new Bp(yield t.read())):new ka(new Ea(t)):new ka(new Ea(function(){return $n(this,arguments,(function*(){}))}()))}))}function av(t){return Mt(this,void 0,void 0,(function*(){const{size:e}=yield t.stat(),n=new Sa(t,e);return e>=tv&&wc(yield n.readAt(0,ls+7&-8))?new ev(new nv(n)):new ka(new Ea(n))}))}function Mp(t){const e=gr.from(t);return qo(e)?e.then((t=>Mp(t))):e.isAsync()?e.readAll().then((t=>new On(t))):new On(e.readAll())}const Au=Symbol("Unset"),Tu=Symbol("IsSetTracked"),Bu=Symbol("GetModKeys"),Pu=Symbol("GetOwnKey"),lv=Symbol("GetOwnPath"),Mu=Symbol("GetParent"),Ll=(t={},e={},n=void 0,r=void 0)=>{if(n&&!n[Tu])throw new Error("SetTracked parent must be SetTracked");const i=Object.assign((()=>{}),e??{}),o=Object.keys(i),s=new Proxy(i,{get(i,a){switch(a){case Au:return!(null!=n&&n[Bu].includes(r));case Bu:return o;case Pu:return r;case Mu:return n;case lv:{const t=[r];let e=n;for(;void 0!==e;)t.unshift(e[Pu]),e=e[Mu];return t.join(".")}case Tu:return!0;case"toJSON":return()=>({...i});case"toString":case"toPrimitive":case Symbol.toPrimitive:return s[Au]?r&&r in t?()=>t[r]:()=>"":e.toString.bind(e);default:return a in i||(i[a]=Ll(t,void 0,s,a)),i[a]}},set:(e,n,r)=>(o.push(n),"object"==typeof r&&(r=Ll(t,r,s,n)),e[n]=r,!0)});return s};var Np={exports:{}};!function(t){!function(e){function n(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}function r(t,e,r,i,o,s){return n(function(t,e){return t<<e|t>>>32-e}(n(n(e,t),n(i,s)),o),r)}function i(t,e,n,i,o,s,a){return r(e&n|~e&i,t,e,o,s,a)}function o(t,e,n,i,o,s,a){return r(e&i|n&~i,t,e,o,s,a)}function s(t,e,n,i,o,s,a){return r(e^n^i,t,e,o,s,a)}function a(t,e,n,i,o,s,a){return r(n^(e|~i),t,e,o,s,a)}function l(t,e){t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;var r,l,c,u,d,h=1732584193,f=-271733879,p=-1732584194,m=271733878;for(r=0;r<t.length;r+=16)l=h,c=f,u=p,d=m,h=i(h,f,p,m,t[r],7,-680876936),m=i(m,h,f,p,t[r+1],12,-389564586),p=i(p,m,h,f,t[r+2],17,606105819),f=i(f,p,m,h,t[r+3],22,-1044525330),h=i(h,f,p,m,t[r+4],7,-176418897),m=i(m,h,f,p,t[r+5],12,1200080426),p=i(p,m,h,f,t[r+6],17,-1473231341),f=i(f,p,m,h,t[r+7],22,-45705983),h=i(h,f,p,m,t[r+8],7,1770035416),m=i(m,h,f,p,t[r+9],12,-1958414417),p=i(p,m,h,f,t[r+10],17,-42063),f=i(f,p,m,h,t[r+11],22,-1990404162),h=i(h,f,p,m,t[r+12],7,1804603682),m=i(m,h,f,p,t[r+13],12,-40341101),p=i(p,m,h,f,t[r+14],17,-1502002290),h=o(h,f=i(f,p,m,h,t[r+15],22,1236535329),p,m,t[r+1],5,-165796510),m=o(m,h,f,p,t[r+6],9,-1069501632),p=o(p,m,h,f,t[r+11],14,643717713),f=o(f,p,m,h,t[r],20,-373897302),h=o(h,f,p,m,t[r+5],5,-701558691),m=o(m,h,f,p,t[r+10],9,38016083),p=o(p,m,h,f,t[r+15],14,-660478335),f=o(f,p,m,h,t[r+4],20,-405537848),h=o(h,f,p,m,t[r+9],5,568446438),m=o(m,h,f,p,t[r+14],9,-1019803690),p=o(p,m,h,f,t[r+3],14,-187363961),f=o(f,p,m,h,t[r+8],20,1163531501),h=o(h,f,p,m,t[r+13],5,-1444681467),m=o(m,h,f,p,t[r+2],9,-51403784),p=o(p,m,h,f,t[r+7],14,1735328473),h=s(h,f=o(f,p,m,h,t[r+12],20,-1926607734),p,m,t[r+5],4,-378558),m=s(m,h,f,p,t[r+8],11,-2022574463),p=s(p,m,h,f,t[r+11],16,1839030562),f=s(f,p,m,h,t[r+14],23,-35309556),h=s(h,f,p,m,t[r+1],4,-1530992060),m=s(m,h,f,p,t[r+4],11,1272893353),p=s(p,m,h,f,t[r+7],16,-155497632),f=s(f,p,m,h,t[r+10],23,-1094730640),h=s(h,f,p,m,t[r+13],4,681279174),m=s(m,h,f,p,t[r],11,-358537222),p=s(p,m,h,f,t[r+3],16,-722521979),f=s(f,p,m,h,t[r+6],23,76029189),h=s(h,f,p,m,t[r+9],4,-640364487),m=s(m,h,f,p,t[r+12],11,-421815835),p=s(p,m,h,f,t[r+15],16,530742520),h=a(h,f=s(f,p,m,h,t[r+2],23,-995338651),p,m,t[r],6,-198630844),m=a(m,h,f,p,t[r+7],10,1126891415),p=a(p,m,h,f,t[r+14],15,-1416354905),f=a(f,p,m,h,t[r+5],21,-57434055),h=a(h,f,p,m,t[r+12],6,1700485571),m=a(m,h,f,p,t[r+3],10,-1894986606),p=a(p,m,h,f,t[r+10],15,-1051523),f=a(f,p,m,h,t[r+1],21,-2054922799),h=a(h,f,p,m,t[r+8],6,1873313359),m=a(m,h,f,p,t[r+15],10,-30611744),p=a(p,m,h,f,t[r+6],15,-1560198380),f=a(f,p,m,h,t[r+13],21,1309151649),h=a(h,f,p,m,t[r+4],6,-145523070),m=a(m,h,f,p,t[r+11],10,-1120210379),p=a(p,m,h,f,t[r+2],15,718787259),f=a(f,p,m,h,t[r+9],21,-343485551),h=n(h,l),f=n(f,c),p=n(p,u),m=n(m,d);return[h,f,p,m]}function c(t){var e,n="",r=32*t.length;for(e=0;e<r;e+=8)n+=String.fromCharCode(t[e>>5]>>>e%32&255);return n}function u(t){var e,n=[];for(n[(t.length>>2)-1]=void 0,e=0;e<n.length;e+=1)n[e]=0;var r=8*t.length;for(e=0;e<r;e+=8)n[e>>5]|=(255&t.charCodeAt(e/8))<<e%32;return n}function d(t){var e,n,r="0123456789abcdef",i="";for(n=0;n<t.length;n+=1)e=t.charCodeAt(n),i+=r.charAt(e>>>4&15)+r.charAt(15&e);return i}function h(t){return unescape(encodeURIComponent(t))}function f(t){return function(t){return c(l(u(t),8*t.length))}(h(t))}function p(t,e){return function(t,e){var n,r,i=u(t),o=[],s=[];for(o[15]=s[15]=void 0,i.length>16&&(i=l(i,8*t.length)),n=0;n<16;n+=1)o[n]=909522486^i[n],s[n]=1549556828^i[n];return r=l(o.concat(u(e)),512+8*e.length),c(l(s.concat(r),640))}(h(t),h(e))}function m(t,e,n){return e?n?p(e,t):function(t,e){return d(p(t,e))}(e,t):n?f(t):function(t){return d(f(t))}(t)}t.exports?t.exports=m:e.md5=m}(Ug)}(Np);var cv=Np.exports;const Nu=Vg(cv),uv=!0,Cp=!0,fv="always",dv=async()=>{let t={};{const e=await fetch(Wt("/data/manifest.json"));e.ok&&({renderedFiles:t}=await e.json())}await ko(Hg),0===Object.keys(t??{}).length?console.warn('No sources found, execute "npm run sources" to generate'.trim()):(await ko(vh,t,{addBasePath:Wt}),await ko(xg,Object.keys(t)))},Cu=ko(dv);async function hv(t,e,n){const r=await n(Wt(`/api/${t}/${e}/all-queries.json`));if(!r.ok)return{};const i=await r.json(),o=await Promise.all(Object.entries(i).map((async([t,e])=>{const r=await n(Wt(`/api/prerendered_queries/${e}.arrow`));if(!r.ok)return null;const i=await Mp(r);return[t,zg(i)]})));return Object.fromEntries(o.filter(Boolean))}const mv=["/settings","/explore"],Fu=new Map,pv=async({fetch:t,route:e,params:n,url:r})=>{var i,o,s;const[{customFormattingSettings:a},l,c]=await Promise.all([t(Wt("/api/customFormattingSettings.json/GET.json")).then((t=>t.json())),t(Wt("/api/pagesManifest.json")).then((t=>t.json())),t(Wt(`/api/${e.id}/evidencemeta.json`)).then((t=>t.json())).catch((()=>({queries:[]})))]),u=Nu(e.id),d=Nu(Object.entries(n).sort().map((([t,e])=>`${t}${e}`)).join("")),h=e.id&&mv.every((t=>!e.id.startsWith(t)));let f={};const{inputs:p=Ll({label:"",value:"(SELECT NULL WHERE 0 /* An Input has not been set */)"})}=Fu.get(r.pathname)??{};function m(t,{query_name:e,callback:n=t=>t}={}){return(async()=>{await Cu;const e=await Wg(t);return n(e)})()}Fu.has(r.pathname),h&&(f=await hv(u,d,t));let b=l;for(const t of(e.id??"").split("/").slice(1)){if(b=b.children[t],!b)break;if(null!=(i=b.frontMatter)&&i.title)b.title=b.frontMatter.title;else if(null!=(o=b.frontMatter)&&o.breadcrumb){let{breadcrumb:t}=b.frontMatter;for(const[e,r]of Object.entries(n))t=t.replaceAll(`\${params.${e}}`,r);b.title=null==(s=(await m(t))[0])?void 0:s.breadcrumb}}return{__db:{query:m,load:async()=>Cu,async updateParquetURLs(t){const{renderedFiles:e}=JSON.parse(t);await ko(vh,e,{addBasePath:Wt})}},inputs:p,data:f,customFormattingSettings:a,isUserPage:h,evidencemeta:c,pagesManifest:l}},RO=Object.freeze(Object.defineProperty({__proto__:null,load:pv,prerender:Cp,ssr:uv,trailingSlash:fv},Symbol.toStringTag,{value:"Module"}));async function gv(t){const{prop:e,defaultEl:n}=t;if(await Promise.all([ni(1),ho]),void 0===e)return void(null==n||n.focus());const r=$g(e)?e(n):e;if("string"==typeof r){const t=document.querySelector(r);if(!Nt(t))return;t.focus()}else Nt(r)&&r.focus()}const _v={ltr:[...wl,ln.ARROW_RIGHT]},bv={ltr:[ln.ARROW_LEFT]},ju=["menu","trigger"],yv={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,typeahead:!0,closeOnItemClick:!0,onOutsideClick:void 0};function vv(t){const{name:e,selector:n}=e_(t.selector),{preventScroll:r,arrowSize:i,positioning:o,closeOnEscape:s,closeOnOutsideClick:a,portal:l,forceVisible:c,typeahead:u,loop:d,closeFocus:h,disableFocusFirstItem:f,closeOnItemClick:p,onOutsideClick:m}=t.rootOptions,b=t.rootOpen,y=t.rootActiveTrigger,g=t.nextFocusable,v=t.prevFocusable,_=tn.writable(!1),w=tn(Ke(0)),S=tn(Ke(null)),I=tn(Ke("right")),O=tn(Ke(null)),$=tn(mo([I,S],(([t,e])=>n=>t===(null==e?void 0:e.side)&&wv(n,null==e?void 0:e.area)))),{typed:x,handleTypeaheadSearch:E}=D_(),k=Do({...Kc(ju),...t.ids}),A=Jc({open:b,forceVisible:c,activeTrigger:y}),D=an(e(),{stores:[A,l,k.menu,k.trigger],returned:([t,e,n,r])=>({role:"menu",hidden:!t||void 0,style:ro({display:t?void 0:"none"}),id:n,"aria-labelledby":r,"data-state":t?"open":"closed","data-portal":t_(e),tabindex:-1}),action:t=>{let e=io;const r=Yn([A,y,o,a,l,s],(([r,i,o,s,a,l])=>{e(),r&&i&&ho().then((()=>{e(),ao(t,n),e=Xc(t,{anchorElement:i,open:b,options:{floating:o,modal:{closeOnInteractOutside:s,shouldCloseOnInteractOutside:t=>{var e;return null==(e=m.get())||e(t),!(t.defaultPrevented||Nt(i)&&i.contains(t.target))},onClose:()=>{b.set(!1),i.focus()},open:r},portal:Zc(t,a),escapeKeydown:l?void 0:null}}).destroy}))})),i=ar($t(t,"keydown",(t=>{const e=t.target,n=t.currentTarget;if(!Nt(e)||!Nt(n)||e.closest('[role="menu"]')!==n)return;if(Qc.includes(t.key)&&Ru(t,d.get()??!1),t.key===ln.TAB)return t.preventDefault(),b.set(!1),void Lu(t,g,v);const r=1===t.key.length;!(t.ctrlKey||t.altKey||t.metaKey)&&r&&!0===u.get()&&E(t.key,Xr(n))})));return{destroy(){r(),i(),e()}}}}),T=an(e("trigger"),{stores:[b,k.menu,k.trigger],returned:([t,e,n])=>({"aria-controls":e,"aria-expanded":t,"data-state":t?"open":"closed",id:n,tabindex:0}),action:t=>(Os(t),y.update((e=>e||t)),{destroy:ar($t(t,"click",(t=>{const e=b.get(),n=t.currentTarget;Nt(n)&&(U(n),e||t.preventDefault())})),$t(t,"keydown",(t=>{const e=t.currentTarget;if(!Nt(e)||!wl.includes(t.key)&&t.key!==ln.ARROW_DOWN)return;t.preventDefault(),U(e);const n=e.getAttribute("aria-controls");if(!n)return;const r=document.getElementById(n);if(!r)return;const i=Xr(r);i.length&&$e(i[0])})))})}),C=an(e("arrow"),{stores:i,returned:t=>({"data-arrow":!0,style:ro({position:"absolute",width:`var(--arrow-size, ${t}px)`,height:`var(--arrow-size, ${t}px)`})})}),L=an(e("overlay"),{stores:[A],returned:([t])=>({hidden:!t||void 0,tabindex:-1,style:ro({display:t?void 0:"none"}),"aria-hidden":"true","data-state":Ov(t)}),action:t=>{let e=io;if(s.get()){const n=n_(t,{handler:()=>{b.set(!1);const t=y.get();t&&t.focus()}});n&&n.destroy&&(e=n.destroy)}const n=Yn([l],(([e])=>{if(null===e)return io;const n=Zc(t,e);return null===n?io:r_(t,n).destroy}));return{destroy(){e(),n()}}}}),M=an(e("item"),{returned:()=>({role:"menuitem",tabindex:-1,"data-orientation":"vertical"}),action:t=>(ao(t,n),Os(t),{destroy:ar($t(t,"pointerdown",(t=>{const e=t.currentTarget;Nt(e)&&lr(e)&&t.preventDefault()})),$t(t,"click",(t=>{const e=t.currentTarget;if(Nt(e)){if(lr(e))return void t.preventDefault();if(t.defaultPrevented)return void $e(e);p.get()&&ni(1).then((()=>{b.set(!1)}))}})),$t(t,"keydown",(t=>{H(t)})),$t(t,"pointermove",(t=>{q(t)})),$t(t,"pointerleave",(t=>{Y(t)})),$t(t,"focusin",(t=>{R(t)})),$t(t,"focusout",(t=>{V(t)})))})}),P=an(e("group"),{returned:()=>t=>({role:"group","aria-labelledby":t})}),B=an(e("group-label"),{returned:()=>t=>({id:t})}),N={defaultChecked:!1,disabled:!1},{elements:{root:j}}=Av({orientation:"horizontal"}),F={...yv,disabled:!1,positioning:{placement:"right-start",gutter:8}};function U(t){b.update((e=>{const n=!e;return n&&(g.set(E_(t)),v.set(A_(t)),y.set(t)),n}))}function R(t){const e=t.currentTarget;if(!Nt(e))return;const n=O.get();n&&ci(n),T_(e),O.set(e)}function V(t){const e=t.currentTarget;Nt(e)&&ci(e)}function W(t){G(t)&&t.preventDefault()}function z(t){if(G(t))return;const e=t.target;if(!Nt(e))return;const n=Q(e);n&&$e(n)}function q(t,e=null){if(!so(t)||(W(t),t.defaultPrevented))return;if(e)return void $e(e);const n=t.currentTarget;Nt(n)&&$e(n)}function Y(t){so(t)&&z(t)}function H(t){if(x.get().length>0&&t.key===ln.SPACE)t.preventDefault();else if(wl.includes(t.key)){t.preventDefault();const e=t.currentTarget;if(!Nt(e))return;e.click()}}function J(t){return"indeterminate"===t}function K(t){return J(t)?"indeterminate":t?"checked":"unchecked"}function G(t){return $.get()(t)}function Q(t){const e=t.closest('[role="menu"]');return Nt(e)?e:null}return Gc((()=>{const t=document.getElementById(k.trigger.get());Nt(t)&&b.get()&&y.set(t);const e=[],n=()=>_.set(!1);return e.push(qr(document,"keydown",(()=>{_.set(!0),e.push(ar(qr(document,"pointerdown",n,{capture:!0,once:!0}),qr(document,"pointermove",n,{capture:!0,once:!0})))}),{capture:!0})),e.push(qr(document,"keydown",(t=>{t.key===ln.ESCAPE&&s.get()&&b.set(!1)}))),()=>{e.forEach((t=>t()))}})),Yn([b,O],(([t,e])=>{!t&&e&&ci(e)})),Yn([b],(([t])=>{if(gi&&!t){const e=y.get();if(!e)return;const n=h.get();!t&&e&&gv({prop:n,defaultEl:e})}})),Yn([b,r],(([t,e])=>{if(!gi)return;const n=[];return t&&e&&n.push(B_()),ni(1).then((()=>{const e=document.getElementById(k.menu.get());if(e&&t&&_.get()){if(f.get())return void $e(e);const t=Xr(e);if(!t.length)return;$e(t[0])}})),()=>{n.forEach((t=>t()))}})),Yn(b,(t=>{if(!gi)return;const e=()=>_.set(!1);return ar(qr(document,"pointerdown",e,{capture:!0,once:!0}),qr(document,"pointermove",e,{capture:!0,once:!0}),qr(document,"keydown",(e=>{_.set(!0),e.key===ln.ESCAPE&&t&&s.get()&&b.set(!1)}),{capture:!0}))})),{elements:{trigger:T,menu:D,overlay:L,item:M,group:P,groupLabel:B,arrow:C,separator:j},builders:{createCheckboxItem:t=>{const r={...N,...t},i=r.checked??Ke(r.defaultChecked??null),o=Ls(i,r.onCheckedChange),s=Ke(r.disabled),a=an(e("checkbox-item"),{stores:[o,s],returned:([t,e])=>({role:"menuitemcheckbox",tabindex:-1,"data-orientation":"vertical","aria-checked":J(t)?"mixed":t?"true":"false","data-disabled":Ga(e),"data-state":K(t)}),action:t=>(ao(t,n),Os(t),{destroy:ar($t(t,"pointerdown",(t=>{const e=t.currentTarget;Nt(e)&&lr(e)&&t.preventDefault()})),$t(t,"click",(t=>{const e=t.currentTarget;if(Nt(e)){if(lr(e))return void t.preventDefault();if(t.defaultPrevented)return void $e(e);o.update((t=>!!J(t)||!t)),p.get()&&ho().then((()=>{b.set(!1)}))}})),$t(t,"keydown",(t=>{H(t)})),$t(t,"pointermove",(t=>{const e=t.currentTarget;if(Nt(e)){if(lr(e))return void z(t);q(t,e)}})),$t(t,"pointerleave",(t=>{Y(t)})),$t(t,"focusin",(t=>{R(t)})),$t(t,"focusout",(t=>{V(t)})))})}),l=mo(o,(t=>!0===t)),c=mo(o,(t=>"indeterminate"===t));return{elements:{checkboxItem:a},states:{checked:o},helpers:{isChecked:l,isIndeterminate:c},options:{disabled:s}}},createSubmenu:t=>{const r={...F,...t},i=r.open??Ke(!1),o=Ls(i,null==r?void 0:r.onOpenChange),s=Do(Dh(r,"ids")),{positioning:a,arrowSize:l,disabled:h}=s,f=tn(Ke(null)),p=tn(Ke(null)),m=tn(Ke(0)),y=Do({...Kc(ju),...r.ids});Gc((()=>{const t=document.getElementById(y.trigger.get());t&&f.set(t)}));const $=Jc({open:o,forceVisible:c,activeTrigger:f}),k=an(e("submenu"),{stores:[$,y.menu,y.trigger],returned:([t,e,n])=>({role:"menu",hidden:!t||void 0,style:ro({display:t?void 0:"none"}),id:e,"aria-labelledby":n,"data-state":t?"open":"closed","data-id":e,tabindex:-1}),action:t=>{let e=io;const n=Yn([$,a],(([n,r])=>{if(e(),!n)return;const i=f.get();i&&ho().then((()=>{e();const n=Q(i);e=Xc(t,{anchorElement:i,open:o,options:{floating:r,portal:Nt(n)?n:void 0,modal:null,focusTrap:null,escapeKeydown:null}}).destroy}))})),r=ar($t(t,"keydown",(t=>{if(t.key===ln.ESCAPE)return;const e=t.target,n=t.currentTarget;if(!Nt(e)||!Nt(n)||e.closest('[role="menu"]')!==n)return;if(Qc.includes(t.key))return t.stopImmediatePropagation(),void Ru(t,d.get()??!1);const r=bv.ltr.includes(t.key),i=t.ctrlKey||t.altKey||t.metaKey,s=1===t.key.length;if(r){const e=f.get();return t.preventDefault(),void o.update((()=>(e&&$e(e),!1)))}if(t.key===ln.TAB)return t.preventDefault(),b.set(!1),void Lu(t,g,v);!i&&s&&!0===u.get()&&E(t.key,Xr(n))})),$t(t,"pointermove",(t=>{!function(t){if(!so(t))return;const e=t.target,n=t.currentTarget;if(!Nt(n)||!Nt(e))return;const r=w.get(),i=r!==t.clientX;if(n.contains(e)&&i){const e=t.clientX>r?"right":"left";I.set(e),w.set(t.clientX)}}(t)})),$t(t,"focusout",(t=>{const e=f.get();if(_.get()){const n=t.target,r=document.getElementById(y.menu.get());if(!Nt(r)||!Nt(n))return;!r.contains(n)&&n!==e&&o.set(!1)}else{const n=t.currentTarget,r=t.relatedTarget;if(!Nt(r)||!Nt(n))return;!n.contains(r)&&r!==e&&o.set(!1)}})));return{destroy(){n(),e(),r()}}}}),A=an(e("subtrigger"),{stores:[o,h,y.menu,y.trigger],returned:([t,e,n,r])=>({role:"menuitem",id:r,tabindex:-1,"aria-controls":n,"aria-expanded":t,"data-state":t?"open":"closed","data-disabled":Ga(e),"aria-haspopop":"menu"}),action:t=>{ao(t,n),Os(t),f.update((e=>e||t));const e=ar($t(t,"click",(t=>{if(t.defaultPrevented)return;const e=t.currentTarget;!Nt(e)||lr(e)||($e(e),o.get()||o.update((t=>t||(f.set(e),!t))))})),$t(t,"keydown",(t=>{const e=x.get(),n=t.currentTarget;if(!(!Nt(n)||lr(n)||e.length>0&&t.key===ln.SPACE)&&_v.ltr.includes(t.key)){if(!o.get())return n.click(),void t.preventDefault();const e=n.getAttribute("aria-controls");if(!e)return;const r=document.getElementById(e);if(!Nt(r))return;const i=Xr(r)[0];$e(i)}})),$t(t,"pointermove",(t=>{if(!so(t)||(W(t),t.defaultPrevented))return;const e=t.currentTarget;if(!Nt(e))return;Iv(y.menu.get())||$e(e);const n=p.get();!o.get()&&!n&&!lr(e)&&p.set(window.setTimeout((()=>{o.update((()=>(f.set(e),!0))),il(p)}),100))})),$t(t,"pointerleave",(t=>{if(!so(t))return;il(p);const e=document.getElementById(y.menu.get()),n=null==e?void 0:e.getBoundingClientRect();if(n){const r=null==e?void 0:e.dataset.side,i="right"===r,o=i?-5:5,s=n[i?"left":"right"],a=n[i?"right":"left"];S.set({area:[{x:t.clientX+o,y:t.clientY},{x:s,y:n.top},{x:a,y:n.top},{x:a,y:n.bottom},{x:s,y:n.bottom}],side:r}),window.clearTimeout(m.get()),m.set(window.setTimeout((()=>{S.set(null)}),300))}else{if(function(t){G(t)&&t.preventDefault()}(t),t.defaultPrevented)return;S.set(null)}})),$t(t,"focusout",(t=>{const e=t.currentTarget;if(!Nt(e))return;ci(e);const n=t.relatedTarget;if(!Nt(n))return;const r=e.getAttribute("aria-controls");if(!r)return;const i=document.getElementById(r);i&&!i.contains(n)&&o.set(!1)})),$t(t,"focusin",(t=>{R(t)})));return{destroy(){il(p),window.clearTimeout(m.get()),S.set(null),e()}}}}),D=an(e("subarrow"),{stores:l,returned:t=>({"data-arrow":!0,style:ro({position:"absolute",width:`var(--arrow-size, ${t}px)`,height:`var(--arrow-size, ${t}px)`})})});return Yn([b],(([t])=>{t||(f.set(null),o.set(!1))})),Yn([S],(([t])=>{!gi||t||window.clearTimeout(m.get())})),Yn([o],(([t])=>{if(gi&&(t&&_.get()&&ni(1).then((()=>{const t=document.getElementById(y.menu.get());if(!t)return;const e=Xr(t);e.length&&$e(e[0])})),!t)){const t=O.get(),e=document.getElementById(y.trigger.get());if(t&&ni(1).then((()=>{const e=document.getElementById(y.menu.get());e&&e.contains(t)&&ci(t)})),!e||document.activeElement===e)return;ci(e)}})),{ids:y,elements:{subTrigger:A,subMenu:k,subArrow:D},states:{subOpen:o},options:s}},createMenuRadioGroup:(t={})=>{const r=t.value??Ke(t.defaultValue??null),i=Ls(r,t.onValueChange),o=an(e("radio-group"),{returned:()=>({role:"group"})}),s={disabled:!1},a=an(e("radio-item"),{stores:[i],returned:([t])=>e=>{const{value:n,disabled:r}={...s,...e},i=t===n;return{disabled:r,role:"menuitemradio","data-state":i?"checked":"unchecked","aria-checked":i,"data-disabled":Ga(r),"data-value":n,"data-orientation":"vertical",tabindex:-1}},action:t=>(ao(t,n),{destroy:ar($t(t,"pointerdown",(e=>{const n=e.currentTarget;if(!Nt(n))return;const r=t.dataset.value;(t.dataset.disabled||void 0===r)&&e.preventDefault()})),$t(t,"click",(e=>{const n=e.currentTarget;if(!Nt(n))return;const r=t.dataset.value;if(t.dataset.disabled||void 0===r)e.preventDefault();else if(e.defaultPrevented){if(!Nt(n))return;$e(n)}else i.set(r),p.get()&&ho().then((()=>{b.set(!1)}))})),$t(t,"keydown",(t=>{H(t)})),$t(t,"pointermove",(e=>{const n=e.currentTarget;if(!Nt(n))return;const r=t.dataset.value;t.dataset.disabled||void 0===r?z(e):q(e,n)})),$t(t,"pointerleave",(t=>{Y(t)})),$t(t,"focusin",(t=>{R(t)})),$t(t,"focusout",(t=>{V(t)})))})}),l=mo(i,(t=>e=>t===e));return{elements:{radioGroup:o,radioItem:a},states:{value:i},helpers:{isChecked:l}}}},states:{open:b},helpers:{handleTypeaheadSearch:E},ids:k,options:t.rootOptions}}function Lu(t,e,n){if(t.shiftKey){const e=n.get();e&&(t.preventDefault(),ni(1).then((()=>e.focus())),n.set(null))}else{const n=e.get();n&&(t.preventDefault(),ni(1).then((()=>n.focus())),e.set(null))}}function Xr(t){return Array.from(t.querySelectorAll(`[data-melt-menu-id="${t.id}"]`)).filter((t=>Nt(t)))}function Os(t){!t||!lr(t)||(t.setAttribute("data-disabled",""),t.setAttribute("aria-disabled","true"))}function il(t){if(!gi)return;const e=t.get();e&&(window.clearTimeout(e),t.set(null))}function so(t){return"mouse"===t.pointerType}function ao(t,e){if(!t)return;const n=t.closest(`${e()}, ${e("submenu")}`);Nt(n)&&t.setAttribute("data-melt-menu-id",n.id)}function Ru(t,e){t.preventDefault();const n=document.activeElement,r=t.currentTarget;if(!Nt(n)||!Nt(r))return;const i=Xr(r);if(!i.length)return;const o=i.filter((t=>!(t.hasAttribute("data-disabled")||"true"===t.getAttribute("disabled")))),s=o.indexOf(n);let a;switch(t.key){case ln.ARROW_DOWN:a=e?s<o.length-1?s+1:0:s<o.length-1?s+1:s;break;case ln.ARROW_UP:a=e?s>0?s-1:o.length-1:s<0?o.length-1:s>0?s-1:0;break;case ln.HOME:a=0;break;case ln.END:a=o.length-1;break;default:return}$e(o[a])}function wv(t,e){return!!e&&Sv({x:t.clientX,y:t.clientY},e)}function Sv(t,e){const{x:n,y:r}=t;let i=!1;for(let t=0,o=e.length-1;t<e.length;o=t++){const s=e[t].x,a=e[t].y,l=e[o].x,c=e[o].y;a>r!=c>r&&n<(l-s)*(r-a)/(c-a)+s&&(i=!i)}return i}function Iv(t){const e=document.activeElement;if(!Nt(e))return!1;const n=e.closest(`[data-id="${t}"]`);return Nt(n)}function Ov(t){return t?"open":"closed"}const kv={arrowSize:8,positioning:{placement:"bottom"},preventScroll:!0,closeOnEscape:!0,closeOnOutsideClick:!0,portal:void 0,loop:!1,dir:"ltr",defaultOpen:!1,forceVisible:!1,typeahead:!0,closeFocus:void 0,disableFocusFirstItem:!1,closeOnItemClick:!0,onOutsideClick:void 0};function Dv(t){const e={...kv,...t},n=Do(Dh(e,"ids")),r=e.open??Ke(e.defaultOpen),i=Ls(r,null==e?void 0:e.onOpenChange),o=tn(Ke(null)),s=tn(Ke(null)),a=tn(Ke(null)),{elements:l,builders:c,ids:u,states:d,options:h}=vv({rootOptions:n,rootOpen:i,rootActiveTrigger:tn(o),nextFocusable:tn(s),prevFocusable:tn(a),selector:"dropdown-menu",ids:e.ids});return{ids:u,elements:l,states:d,builders:c,options:h}}const Ev={orientation:"horizontal",decorative:!1},Av=t=>{const e={...Ev,...t},n=Do(e),{orientation:r,decorative:i}=n;return{elements:{root:an("separator",{stores:[r,i],returned:([t,e])=>({role:e?"none":"separator","aria-orientation":"vertical"===t?t:void 0,"aria-hidden":e,"data-orientation":t})})},options:n}};let Tv="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",Bv=(t=21)=>{let e="",n=0|t;for(;n--;)e+=Tv[64*Math.random()|0];return e};function Pv(){return Bv(10)}function Mv(t,e){const n=[];return e.builders.forEach((e=>{const r=e.action(t);r&&n.push(r)})),{destroy:()=>{n.forEach((t=>{t.destroy&&t.destroy()}))}}}function Uu(t){const e={};return t.forEach((t=>{Object.keys(t).forEach((n=>{"action"!==n&&(e[n]=t[n])}))})),e}function Nv(t){let e,n,r=t[1]?"a":"button",i=(t[1]?"a":"button")&&ol(t);return{c(){i&&i.c(),e=ut()},l(t){i&&i.l(t),e=ut()},m(t,r){i&&i.m(t,r),L(t,e,r),n=!0},p(t,n){t[1],r?Zt(r,t[1]?"a":"button")?(i.d(1),i=ol(t),r=t[1]?"a":"button",i.c(),i.m(e.parentNode,e)):i.p(t,n):(i=ol(t),r=t[1]?"a":"button",i.c(),i.m(e.parentNode,e))},i(t){n||(I(i,t),n=!0)},o(t){E(i,t),n=!1},d(t){t&&w(e),i&&i.d(t)}}}function Cv(t){let e,n,r=t[1]?"a":"button",i=(t[1]?"a":"button")&&sl(t);return{c(){i&&i.c(),e=ut()},l(t){i&&i.l(t),e=ut()},m(t,r){i&&i.m(t,r),L(t,e,r),n=!0},p(t,n){t[1],r?Zt(r,t[1]?"a":"button")?(i.d(1),i=sl(t),r=t[1]?"a":"button",i.c(),i.m(e.parentNode,e)):i.p(t,n):(i=sl(t),r=t[1]?"a":"button",i.c(),i.m(e.parentNode,e))},i(t){n||(I(i,t),n=!0)},o(t){E(i,t),n=!1},d(t){t&&w(e),i&&i.d(t)}}}function ol(t){let e,n,r,i,o;const s=t[7].default,a=ge(s,t,t[6],null);let l=[{type:n=t[1]?void 0:t[2]},{href:t[1]},{tabindex:"0"},t[5],t[4]],c={};for(let t=0;t<l.length;t+=1)c=te(c,l[t]);return{c(){e=Y(t[1]?"a":"button"),a&&a.c(),this.h()},l(n){e=q(n,((t[1]?"a":"button")||"null").toUpperCase(),{type:!0,href:!0,tabindex:!0});var r=J(e);a&&a.l(r),r.forEach(w),this.h()},h(){ji(t[1]?"a":"button")(e,c)},m(n,s){L(n,e,s),a&&a.m(e,null),t[29](e),r=!0,i||(o=[Pt(e,"click",t[18]),Pt(e,"change",t[19]),Pt(e,"keydown",t[20]),Pt(e,"keyup",t[21]),Pt(e,"mouseenter",t[22]),Pt(e,"mouseleave",t[23]),Pt(e,"mousedown",t[24]),Pt(e,"pointerdown",t[25]),Pt(e,"mouseup",t[26]),Pt(e,"pointerup",t[27])],i=!0)},p(t,i){a&&a.p&&(!r||64&i)&&_e(a,s,t,t[6],r?ye(s,t[6],i,null):be(t[6]),null),ji(t[1]?"a":"button")(e,c=rn(l,[(!r||6&i&&n!==(n=t[1]?void 0:t[2]))&&{type:n},(!r||2&i)&&{href:t[1]},{tabindex:"0"},32&i&&t[5],t[4]]))},i(t){r||(I(a,t),r=!0)},o(t){E(a,t),r=!1},d(n){n&&w(e),a&&a.d(n),t[29](null),i=!1,zn(o)}}}function sl(t){let e,n,r,i,o,s;const a=t[7].default,l=ge(a,t,t[6],null);let c=[{type:n=t[1]?void 0:t[2]},{href:t[1]},{tabindex:"0"},Uu(t[3]),t[5],t[4]],u={};for(let t=0;t<c.length;t+=1)u=te(u,c[t]);return{c(){e=Y(t[1]?"a":"button"),l&&l.c(),this.h()},l(n){e=q(n,((t[1]?"a":"button")||"null").toUpperCase(),{type:!0,href:!0,tabindex:!0});var r=J(e);l&&l.l(r),r.forEach(w),this.h()},h(){ji(t[1]?"a":"button")(e,u)},m(n,a){L(n,e,a),l&&l.m(e,null),t[28](e),i=!0,o||(s=[Pt(e,"click",t[8]),Pt(e,"change",t[9]),Pt(e,"keydown",t[10]),Pt(e,"keyup",t[11]),Pt(e,"mouseenter",t[12]),Pt(e,"mouseleave",t[13]),Pt(e,"mousedown",t[14]),Pt(e,"pointerdown",t[15]),Pt(e,"mouseup",t[16]),Pt(e,"pointerup",t[17]),Ir(r=Mv.call(null,e,{builders:t[3]}))],o=!0)},p(t,o){l&&l.p&&(!i||64&o)&&_e(l,a,t,t[6],i?ye(a,t[6],o,null):be(t[6]),null),ji(t[1]?"a":"button")(e,u=rn(c,[(!i||6&o&&n!==(n=t[1]?void 0:t[2]))&&{type:n},(!i||2&o)&&{href:t[1]},{tabindex:"0"},8&o&&Uu(t[3]),32&o&&t[5],t[4]])),r&&Kg(r.update)&&8&o&&r.update.call(null,{builders:t[3]})},i(t){i||(I(l,t),i=!0)},o(t){E(l,t),i=!1},d(n){n&&w(e),l&&l.d(n),t[28](null),o=!1,zn(s)}}}function Fv(t){let e,n,r,i;const o=[Cv,Nv],s=[];function a(t,e){return t[3]&&t[3].length?0:1}return e=a(t),n=s[e]=o[e](t),{c(){n.c(),r=ut()},l(t){n.l(t),r=ut()},m(t,n){s[e].m(t,n),L(t,r,n),i=!0},p(t,[i]){let l=e;e=a(t),e===l?s[e].p(t,i):(jt(),E(s[l],1,1,(()=>{s[l]=null})),Lt(),n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),I(n,1),n.m(r.parentNode,r))},i(t){i||(I(n),i=!0)},o(t){E(n),i=!1},d(t){t&&w(r),s[e].d(t)}}}function jv(t,e,n){const r=["href","type","builders","el"];let i=Ue(e,r),{$$slots:o={},$$scope:s}=e,{href:a}=e,{type:l}=e,{builders:c=[]}=e,{el:u}=e;return t.$$set=t=>{e=te(te({},e),Sr(t)),n(5,i=Ue(e,r)),"href"in t&&n(1,a=t.href),"type"in t&&n(2,l=t.type),"builders"in t&&n(3,c=t.builders),"el"in t&&n(0,u=t.el),"$$scope"in t&&n(6,s=t.$$scope)},[u,a,l,c,{"data-button-root":""},i,s,o,function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(t){kn[t?"unshift":"push"]((()=>{u=t,n(0,u)}))},function(t){kn[t?"unshift":"push"]((()=>{u=t,n(0,u)}))}]}let Lv=class extends re{constructor(t){super(),ie(this,t,jv,Fv,Zt,{href:1,type:2,builders:3,el:0})}};function Sc(){return{NAME:"menu",SUB_NAME:"menu-submenu",RADIO_GROUP_NAME:"menu-radiogroup",CHECKBOX_ITEM_NAME:"menu-checkboxitem",RADIO_ITEM_NAME:"menu-radioitem",GROUP_NAME:"menu-group",PARTS:["arrow","checkbox-indicator","checkbox-item","content","group","item","label","radio-group","radio-item","radio-indicator","separator","sub-content","sub-trigger","trigger"]}}function cs(){const{NAME:t}=Sc();return Jg(t)}function Rv(t){const{NAME:e,PARTS:n}=Sc(),r=i_("menu",n),i={...Dv({...o_(t),forceVisible:!0}),getAttrs:r};return Ih(e,i),{...i,updateOption:s_(i.options)}}function Uv(){const{GROUP_NAME:t}=Sc(),{elements:{group:e},getAttrs:n}=cs(),r=Pv();return Ih(t,r),{group:e,id:r,getAttrs:n}}function Vv(t){const e={side:"bottom",align:"center",...t},{options:{positioning:n}}=cs();a_(n)(e)}const zv=t=>({builder:8&t}),Vu=t=>({builder:t[3]}),Wv=t=>({builder:8&t}),zu=t=>({builder:t[3]});function Hv(t){let e,n,r=t[1]?"a":"div",i=(t[1]?"a":"div")&&al(t);return{c(){i&&i.c(),e=ut()},l(t){i&&i.l(t),e=ut()},m(t,r){i&&i.m(t,r),L(t,e,r),n=!0},p(t,n){t[1],r?Zt(r,t[1]?"a":"div")?(i.d(1),i=al(t),r=t[1]?"a":"div",i.c(),i.m(e.parentNode,e)):i.p(t,n):(i=al(t),r=t[1]?"a":"div",i.c(),i.m(e.parentNode,e))},i(t){n||(I(i,t),n=!0)},o(t){E(i,t),n=!1},d(t){t&&w(e),i&&i.d(t)}}}function xv(t){let e;const n=t[11].default,r=ge(n,t,t[10],zu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||1032&i)&&_e(r,n,t,t[10],e?ye(n,t[10],i,Wv):be(t[10]),zu)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function al(t){let e,n,r,i;const o=t[11].default,s=ge(o,t,t[10],Vu);let a=[{href:t[1]},t[3],t[6]],l={};for(let t=0;t<a.length;t+=1)l=te(l,a[t]);return{c(){e=Y(t[1]?"a":"div"),s&&s.c(),this.h()},l(n){e=q(n,((t[1]?"a":"div")||"null").toUpperCase(),{href:!0});var r=J(e);s&&s.l(r),r.forEach(w),this.h()},h(){ji(t[1]?"a":"div")(e,l)},m(o,a){L(o,e,a),s&&s.m(e,null),t[13](e),n=!0,r||(i=[Ir(t[3].action(e)),Pt(e,"m-click",t[5]),Pt(e,"m-focusin",t[5]),Pt(e,"m-focusout",t[5]),Pt(e,"m-keydown",t[5]),Pt(e,"m-pointerdown",t[5]),Pt(e,"m-pointerleave",t[5]),Pt(e,"m-pointermove",t[5]),Pt(e,"pointerenter",t[12])],r=!0)},p(t,r){s&&s.p&&(!n||1032&r)&&_e(s,o,t,t[10],n?ye(o,t[10],r,zv):be(t[10]),Vu),ji(t[1]?"a":"div")(e,l=rn(a,[(!n||2&r)&&{href:t[1]},8&r&&t[3],64&r&&t[6]]))},i(t){n||(I(s,t),n=!0)},o(t){E(s,t),n=!1},d(n){n&&w(e),s&&s.d(n),t[13](null),r=!1,zn(i)}}}function qv(t){let e,n,r,i;const o=[xv,Hv],s=[];function a(t,e){return t[2]?0:1}return e=a(t),n=s[e]=o[e](t),{c(){n.c(),r=ut()},l(t){n.l(t),r=ut()},m(t,n){s[e].m(t,n),L(t,r,n),i=!0},p(t,[i]){let l=e;e=a(t),e===l?s[e].p(t,i):(jt(),E(s[l],1,1,(()=>{s[l]=null})),Lt(),n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),I(n,1),n.m(r.parentNode,r))},i(t){i||(I(n),i=!0)},o(t){E(n),i=!1},d(t){t&&w(r),s[e].d(t)}}}function Yv(t,e,n){let r,i;const o=["href","asChild","disabled","el"];let s,a=Ue(e,o),{$$slots:l={},$$scope:c}=e,{href:u}=e,{asChild:d=!1}=e,{disabled:h=!1}=e,{el:f}=e;const{elements:{item:p},getAttrs:m}=cs();Te(t,p,(t=>n(9,s=t)));const b=tc();return t.$$set=t=>{e=te(te({},e),Sr(t)),n(6,a=Ue(e,o)),"href"in t&&n(1,u=t.href),"asChild"in t&&n(2,d=t.asChild),"disabled"in t&&n(7,h=t.disabled),"el"in t&&n(0,f=t.el),"$$scope"in t&&n(10,c=t.$$scope)},t.$$.update=()=>{512&t.$$.dirty&&n(3,r=s),128&t.$$.dirty&&n(8,i={...m("item"),...l_(h)}),264&t.$$.dirty&&Object.assign(r,i)},[f,u,d,r,p,b,a,h,i,s,c,l,function(e){ne.call(this,t,e)},function(t){kn[t?"unshift":"push"]((()=>{f=t,n(0,f)}))}]}class Kv extends re{constructor(t){super(),ie(this,t,Yv,qv,Zt,{href:1,asChild:2,disabled:7,el:0})}}const Jv=t=>({builder:4&t}),Wu=t=>({builder:t[2]}),Qv=t=>({builder:4&t}),Hu=t=>({builder:t[2]});function Gv(t){let e,n,r,i;const o=t[7].default,s=ge(o,t,t[6],Wu);let a=[t[2],t[4]],l={};for(let t=0;t<a.length;t+=1)l=te(l,a[t]);return{c(){e=Y("div"),s&&s.c(),this.h()},l(t){e=q(t,"DIV",{});var n=J(e);s&&s.l(n),n.forEach(w),this.h()},h(){Ge(e,l)},m(o,a){L(o,e,a),s&&s.m(e,null),t[8](e),n=!0,r||(i=Ir(t[2].action(e)),r=!0)},p(t,r){s&&s.p&&(!n||68&r)&&_e(s,o,t,t[6],n?ye(o,t[6],r,Jv):be(t[6]),Wu),Ge(e,l=rn(a,[4&r&&t[2],16&r&&t[4]]))},i(t){n||(I(s,t),n=!0)},o(t){E(s,t),n=!1},d(n){n&&w(e),s&&s.d(n),t[8](null),r=!1,i()}}}function Xv(t){let e;const n=t[7].default,r=ge(n,t,t[6],Hu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||68&i)&&_e(r,n,t,t[6],e?ye(n,t[6],i,Qv):be(t[6]),Hu)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function Zv(t){let e,n,r,i;const o=[Xv,Gv],s=[];function a(t,e){return t[1]?0:1}return e=a(t),n=s[e]=o[e](t),{c(){n.c(),r=ut()},l(t){n.l(t),r=ut()},m(t,n){s[e].m(t,n),L(t,r,n),i=!0},p(t,[i]){let l=e;e=a(t),e===l?s[e].p(t,i):(jt(),E(s[l],1,1,(()=>{s[l]=null})),Lt(),n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),I(n,1),n.m(r.parentNode,r))},i(t){i||(I(n),i=!0)},o(t){E(n),i=!1},d(t){t&&w(r),s[e].d(t)}}}function $v(t,e,n){let r;const i=["asChild","el"];let o,s=Ue(e,i),{$$slots:a={},$$scope:l}=e,{asChild:c=!1}=e,{el:u}=e;const{group:d,id:h,getAttrs:f}=Uv();Te(t,d,(t=>n(5,o=t)));const p=f("group");return t.$$set=t=>{e=te(te({},e),Sr(t)),n(4,s=Ue(e,i)),"asChild"in t&&n(1,c=t.asChild),"el"in t&&n(0,u=t.el),"$$scope"in t&&n(6,l=t.$$scope)},t.$$.update=()=>{32&t.$$.dirty&&n(2,r=o(h)),4&t.$$.dirty&&Object.assign(r,p)},[u,c,r,d,s,o,l,a,function(t){kn[t?"unshift":"push"]((()=>{u=t,n(0,u)}))}]}class t0 extends re{constructor(t){super(),ie(this,t,$v,Zv,Zt,{asChild:1,el:0})}}const e0=t=>({ids:1&t}),xu=t=>({ids:t[0]});function n0(t){let e;const n=t[16].default,r=ge(n,t,t[15],xu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,[i]){r&&r.p&&(!e||32769&i)&&_e(r,n,t,t[15],e?ye(n,t[15],i,e0):be(t[15]),xu)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function r0(t,e,n){let r,{$$slots:i={},$$scope:o}=e,{closeOnOutsideClick:s}=e,{closeOnEscape:a}=e,{portal:l}=e,{open:c}=e,{onOpenChange:u}=e,{preventScroll:d}=e,{loop:h}=e,{dir:f}=e,{typeahead:p}=e,{closeFocus:m}=e,{disableFocusFirstItem:b}=e,{closeOnItemClick:y}=e,{onOutsideClick:g}=e;const{states:{open:v},updateOption:_,ids:w}=Rv({closeOnOutsideClick:s,closeOnEscape:a,portal:l,forceVisible:!0,defaultOpen:c,preventScroll:d,loop:h,dir:f,typeahead:p,closeFocus:m,disableFocusFirstItem:b,closeOnItemClick:y,onOutsideClick:g,onOpenChange:({next:t})=>(c!==t&&(null==u||u(t),n(2,c=t)),t)}),S=mo([w.menu,w.trigger],(([t,e])=>({menu:t,trigger:e})));return Te(t,S,(t=>n(0,r=t))),t.$$set=t=>{"closeOnOutsideClick"in t&&n(3,s=t.closeOnOutsideClick),"closeOnEscape"in t&&n(4,a=t.closeOnEscape),"portal"in t&&n(5,l=t.portal),"open"in t&&n(2,c=t.open),"onOpenChange"in t&&n(6,u=t.onOpenChange),"preventScroll"in t&&n(7,d=t.preventScroll),"loop"in t&&n(8,h=t.loop),"dir"in t&&n(9,f=t.dir),"typeahead"in t&&n(10,p=t.typeahead),"closeFocus"in t&&n(11,m=t.closeFocus),"disableFocusFirstItem"in t&&n(12,b=t.disableFocusFirstItem),"closeOnItemClick"in t&&n(13,y=t.closeOnItemClick),"onOutsideClick"in t&&n(14,g=t.onOutsideClick),"$$scope"in t&&n(15,o=t.$$scope)},t.$$.update=()=>{4&t.$$.dirty&&void 0!==c&&v.set(c),8&t.$$.dirty&&_("closeOnOutsideClick",s),16&t.$$.dirty&&_("closeOnEscape",a),32&t.$$.dirty&&_("portal",l),128&t.$$.dirty&&_("preventScroll",d),256&t.$$.dirty&&_("loop",h),512&t.$$.dirty&&_("dir",f),2048&t.$$.dirty&&_("closeFocus",m),4096&t.$$.dirty&&_("disableFocusFirstItem",b),1024&t.$$.dirty&&_("typeahead",p),8192&t.$$.dirty&&_("closeOnItemClick",y),16384&t.$$.dirty&&_("onOutsideClick",g)},[r,S,c,s,a,l,u,d,h,f,p,m,b,y,g,o,i]}class i0 extends re{constructor(t){super(),ie(this,t,r0,n0,Zt,{closeOnOutsideClick:3,closeOnEscape:4,portal:5,open:2,onOpenChange:6,preventScroll:7,loop:8,dir:9,typeahead:10,closeFocus:11,disableFocusFirstItem:12,closeOnItemClick:13,onOutsideClick:14})}}const o0=t=>({builder:256&t[0]}),qu=t=>({builder:t[8]}),s0=t=>({builder:256&t[0]}),Yu=t=>({builder:t[8]}),a0=t=>({builder:256&t[0]}),Ku=t=>({builder:t[8]}),l0=t=>({builder:256&t[0]}),Ju=t=>({builder:t[8]}),c0=t=>({builder:256&t[0]}),Qu=t=>({builder:t[8]}),u0=t=>({builder:256&t[0]}),Gu=t=>({builder:t[8]});function f0(t){let e,n,r,i;const o=t[28].default,s=ge(o,t,t[27],qu);let a=[t[8],t[13]],l={};for(let t=0;t<a.length;t+=1)l=te(l,a[t]);return{c(){e=Y("div"),s&&s.c(),this.h()},l(t){e=q(t,"DIV",{});var n=J(e);s&&s.l(n),n.forEach(w),this.h()},h(){Ge(e,l)},m(o,a){L(o,e,a),s&&s.m(e,null),t[33](e),n=!0,r||(i=[Ir(t[8].action(e)),Pt(e,"m-keydown",t[12])],r=!0)},p(t,r){s&&s.p&&(!n||134217984&r[0])&&_e(s,o,t,t[27],n?ye(o,t[27],r,o0):be(t[27]),qu),Ge(e,l=rn(a,[256&r[0]&&t[8],8192&r[0]&&t[13]]))},i(t){n||(I(s,t),n=!0)},o(t){E(s,t),n=!1},d(n){n&&w(e),s&&s.d(n),t[33](null),r=!1,zn(i)}}}function d0(t){let e,n,r,i,o;const s=t[28].default,a=ge(s,t,t[27],Yu);let l=[t[8],t[13]],c={};for(let t=0;t<l.length;t+=1)c=te(c,l[t]);return{c(){e=Y("div"),a&&a.c(),this.h()},l(t){e=q(t,"DIV",{});var n=J(e);a&&a.l(n),n.forEach(w),this.h()},h(){Ge(e,c)},m(n,s){L(n,e,s),a&&a.m(e,null),t[32](e),r=!0,i||(o=[Ir(t[8].action(e)),Pt(e,"m-keydown",t[12])],i=!0)},p(n,i){t=n,a&&a.p&&(!r||134217984&i[0])&&_e(a,s,t,t[27],r?ye(s,t[27],i,s0):be(t[27]),Yu),Ge(e,c=rn(l,[256&i[0]&&t[8],8192&i[0]&&t[13]]))},i(t){r||(I(a,t),n&&n.end(1),r=!0)},o(i){E(a,i),i&&(n=is(e,t[5],t[6])),r=!1},d(r){r&&w(e),a&&a.d(r),t[32](null),r&&n&&n.end(),i=!1,zn(o)}}}function h0(t){let e,n,r,i,o;const s=t[28].default,a=ge(s,t,t[27],Ku);let l=[t[8],t[13]],c={};for(let t=0;t<l.length;t+=1)c=te(c,l[t]);return{c(){e=Y("div"),a&&a.c(),this.h()},l(t){e=q(t,"DIV",{});var n=J(e);a&&a.l(n),n.forEach(w),this.h()},h(){Ge(e,c)},m(n,s){L(n,e,s),a&&a.m(e,null),t[31](e),r=!0,i||(o=[Ir(t[8].action(e)),Pt(e,"m-keydown",t[12])],i=!0)},p(n,i){t=n,a&&a.p&&(!r||134217984&i[0])&&_e(a,s,t,t[27],r?ye(s,t[27],i,a0):be(t[27]),Ku),Ge(e,c=rn(l,[256&i[0]&&t[8],8192&i[0]&&t[13]]))},i(i){r||(I(a,i),i&&(n||En((()=>{n=qi(e,t[3],t[4]),n.start()}))),r=!0)},o(t){E(a,t),r=!1},d(n){n&&w(e),a&&a.d(n),t[31](null),i=!1,zn(o)}}}function m0(t){let e,n,r,i,o,s;const a=t[28].default,l=ge(a,t,t[27],Ju);let c=[t[8],t[13]],u={};for(let t=0;t<c.length;t+=1)u=te(u,c[t]);return{c(){e=Y("div"),l&&l.c(),this.h()},l(t){e=q(t,"DIV",{});var n=J(e);l&&l.l(n),n.forEach(w),this.h()},h(){Ge(e,u)},m(n,r){L(n,e,r),l&&l.m(e,null),t[30](e),i=!0,o||(s=[Ir(t[8].action(e)),Pt(e,"m-keydown",t[12])],o=!0)},p(n,r){t=n,l&&l.p&&(!i||134217984&r[0])&&_e(l,a,t,t[27],i?ye(a,t[27],r,l0):be(t[27]),Ju),Ge(e,u=rn(c,[256&r[0]&&t[8],8192&r[0]&&t[13]]))},i(o){i||(I(l,o),o&&En((()=>{i&&(r&&r.end(1),n=qi(e,t[3],t[4]),n.start())})),i=!0)},o(o){E(l,o),n&&n.invalidate(),o&&(r=is(e,t[5],t[6])),i=!1},d(n){n&&w(e),l&&l.d(n),t[30](null),n&&r&&r.end(),o=!1,zn(s)}}}function p0(t){let e,n,r,i,o;const s=t[28].default,a=ge(s,t,t[27],Qu);let l=[t[8],t[13]],c={};for(let t=0;t<l.length;t+=1)c=te(c,l[t]);return{c(){e=Y("div"),a&&a.c(),this.h()},l(t){e=q(t,"DIV",{});var n=J(e);a&&a.l(n),n.forEach(w),this.h()},h(){Ge(e,c)},m(n,s){L(n,e,s),a&&a.m(e,null),t[29](e),r=!0,i||(o=[Ir(t[8].action(e)),Pt(e,"m-keydown",t[12])],i=!0)},p(n,i){t=n,a&&a.p&&(!r||134217984&i[0])&&_e(a,s,t,t[27],r?ye(s,t[27],i,c0):be(t[27]),Qu),Ge(e,c=rn(l,[256&i[0]&&t[8],8192&i[0]&&t[13]]))},i(i){r||(I(a,i),i&&En((()=>{r&&(n||(n=Dn(e,t[1],t[2],!0)),n.run(1))})),r=!0)},o(i){E(a,i),i&&(n||(n=Dn(e,t[1],t[2],!1)),n.run(0)),r=!1},d(r){r&&w(e),a&&a.d(r),t[29](null),r&&n&&n.end(),i=!1,zn(o)}}}function g0(t){let e;const n=t[28].default,r=ge(n,t,t[27],Gu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||134217984&i[0])&&_e(r,n,t,t[27],e?ye(n,t[27],i,u0):be(t[27]),Gu)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function _0(t){let e,n,r,i;const o=[g0,p0,m0,h0,d0,f0],s=[];function a(t,e){return t[7]&&t[9]?0:t[1]&&t[9]?1:t[3]&&t[5]&&t[9]?2:t[3]&&t[9]?3:t[5]&&t[9]?4:t[9]?5:-1}return~(e=a(t))&&(n=s[e]=o[e](t)),{c(){n&&n.c(),r=ut()},l(t){n&&n.l(t),r=ut()},m(t,n){~e&&s[e].m(t,n),L(t,r,n),i=!0},p(t,i){let l=e;e=a(t),e===l?~e&&s[e].p(t,i):(n&&(jt(),E(s[l],1,1,(()=>{s[l]=null})),Lt()),~e?(n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),I(n,1),n.m(r.parentNode,r)):n=null)},i(t){i||(I(n),i=!0)},o(t){E(n),i=!1},d(t){t&&w(r),~e&&s[e].d(t)}}}function b0(t,e,n){let r;const i=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let o,s,a=Ue(e,i),{$$slots:l={},$$scope:c}=e,{transition:u}=e,{transitionConfig:d}=e,{inTransition:h}=e,{inTransitionConfig:f}=e,{outTransition:p}=e,{outTransitionConfig:m}=e,{asChild:b=!1}=e,{id:y}=e,{side:g="bottom"}=e,{align:v="center"}=e,{sideOffset:_=0}=e,{alignOffset:w=0}=e,{collisionPadding:S=8}=e,{avoidCollisions:I=!0}=e,{collisionBoundary:O}=e,{sameWidth:$=!1}=e,{fitViewport:x=!1}=e,{strategy:E="absolute"}=e,{overlap:k=!1}=e,{el:A}=e;const{elements:{menu:D},states:{open:T},ids:C,getAttrs:L}=cs();Te(t,D,(t=>n(26,s=t))),Te(t,T,(t=>n(9,o=t)));const M=tc(),P=L("content");return t.$$set=t=>{e=te(te({},e),Sr(t)),n(13,a=Ue(e,i)),"transition"in t&&n(1,u=t.transition),"transitionConfig"in t&&n(2,d=t.transitionConfig),"inTransition"in t&&n(3,h=t.inTransition),"inTransitionConfig"in t&&n(4,f=t.inTransitionConfig),"outTransition"in t&&n(5,p=t.outTransition),"outTransitionConfig"in t&&n(6,m=t.outTransitionConfig),"asChild"in t&&n(7,b=t.asChild),"id"in t&&n(14,y=t.id),"side"in t&&n(15,g=t.side),"align"in t&&n(16,v=t.align),"sideOffset"in t&&n(17,_=t.sideOffset),"alignOffset"in t&&n(18,w=t.alignOffset),"collisionPadding"in t&&n(19,S=t.collisionPadding),"avoidCollisions"in t&&n(20,I=t.avoidCollisions),"collisionBoundary"in t&&n(21,O=t.collisionBoundary),"sameWidth"in t&&n(22,$=t.sameWidth),"fitViewport"in t&&n(23,x=t.fitViewport),"strategy"in t&&n(24,E=t.strategy),"overlap"in t&&n(25,k=t.overlap),"el"in t&&n(0,A=t.el),"$$scope"in t&&n(27,c=t.$$scope)},t.$$.update=()=>{16384&t.$$.dirty[0]&&y&&C.menu.set(y),67108864&t.$$.dirty[0]&&n(8,r=s),256&t.$$.dirty[0]&&Object.assign(r,P),67076608&t.$$.dirty[0]&&o&&Vv({side:g,align:v,sideOffset:_,alignOffset:w,collisionPadding:S,avoidCollisions:I,collisionBoundary:O,sameWidth:$,fitViewport:x,strategy:E,overlap:k})},[A,u,d,h,f,p,m,b,r,o,D,T,M,a,y,g,v,_,w,S,I,O,$,x,E,k,s,c,l,function(t){kn[t?"unshift":"push"]((()=>{A=t,n(0,A)}))},function(t){kn[t?"unshift":"push"]((()=>{A=t,n(0,A)}))},function(t){kn[t?"unshift":"push"]((()=>{A=t,n(0,A)}))},function(t){kn[t?"unshift":"push"]((()=>{A=t,n(0,A)}))},function(t){kn[t?"unshift":"push"]((()=>{A=t,n(0,A)}))}]}class y0 extends re{constructor(t){super(),ie(this,t,b0,_0,Zt,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:14,side:15,align:16,sideOffset:17,alignOffset:18,collisionPadding:19,avoidCollisions:20,collisionBoundary:21,sameWidth:22,fitViewport:23,strategy:24,overlap:25,el:0},null,[-1,-1])}}const v0=t=>({builder:4&t}),Xu=t=>({builder:t[2]}),w0=t=>({builder:4&t}),Zu=t=>({builder:t[2]});function S0(t){let e,n,r,i;const o=t[9].default,s=ge(o,t,t[8],Xu);let a=[t[2],{type:"button"},t[5]],l={};for(let t=0;t<a.length;t+=1)l=te(l,a[t]);return{c(){e=Y("button"),s&&s.c(),this.h()},l(t){e=q(t,"BUTTON",{type:!0});var n=J(e);s&&s.l(n),n.forEach(w),this.h()},h(){Ge(e,l)},m(o,a){L(o,e,a),s&&s.m(e,null),e.autofocus&&e.focus(),t[10](e),n=!0,r||(i=[Ir(t[2].action(e)),Pt(e,"m-keydown",t[4]),Pt(e,"m-pointerdown",t[4])],r=!0)},p(t,r){s&&s.p&&(!n||260&r)&&_e(s,o,t,t[8],n?ye(o,t[8],r,v0):be(t[8]),Xu),Ge(e,l=rn(a,[4&r&&t[2],{type:"button"},32&r&&t[5]]))},i(t){n||(I(s,t),n=!0)},o(t){E(s,t),n=!1},d(n){n&&w(e),s&&s.d(n),t[10](null),r=!1,zn(i)}}}function I0(t){let e;const n=t[9].default,r=ge(n,t,t[8],Zu);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||260&i)&&_e(r,n,t,t[8],e?ye(n,t[8],i,w0):be(t[8]),Zu)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function O0(t){let e,n,r,i;const o=[I0,S0],s=[];function a(t,e){return t[1]?0:1}return e=a(t),n=s[e]=o[e](t),{c(){n.c(),r=ut()},l(t){n.l(t),r=ut()},m(t,n){s[e].m(t,n),L(t,r,n),i=!0},p(t,[i]){let l=e;e=a(t),e===l?s[e].p(t,i):(jt(),E(s[l],1,1,(()=>{s[l]=null})),Lt(),n=s[e],n?n.p(t,i):(n=s[e]=o[e](t),n.c()),I(n,1),n.m(r.parentNode,r))},i(t){i||(I(n),i=!0)},o(t){E(n),i=!1},d(t){t&&w(r),s[e].d(t)}}}function k0(t,e,n){let r;const i=["asChild","id","el"];let o,s=Ue(e,i),{$$slots:a={},$$scope:l}=e,{asChild:c=!1}=e,{id:u}=e,{el:d}=e;const{elements:{trigger:h},ids:f,getAttrs:p}=cs();Te(t,h,(t=>n(7,o=t)));const m=tc(),b=p("trigger");return t.$$set=t=>{e=te(te({},e),Sr(t)),n(5,s=Ue(e,i)),"asChild"in t&&n(1,c=t.asChild),"id"in t&&n(6,u=t.id),"el"in t&&n(0,d=t.el),"$$scope"in t&&n(8,l=t.$$scope)},t.$$.update=()=>{64&t.$$.dirty&&u&&f.trigger.set(u),128&t.$$.dirty&&n(2,r=o),4&t.$$.dirty&&Object.assign(r,b)},[d,c,r,h,m,s,u,o,l,a,function(t){kn[t?"unshift":"push"]((()=>{d=t,n(0,d)}))}]}class D0 extends re{constructor(t){super(),ie(this,t,k0,O0,Zt,{asChild:1,id:6,el:0})}}const ll=t=>t instanceof Date,Ic=t=>0===Object.keys(t).length,_r=t=>null!=t&&"object"==typeof t,Oc=(t,...e)=>Object.prototype.hasOwnProperty.call(t,...e),cl=t=>_r(t)&&Ic(t),kc=()=>Object.create(null),Fp=(t,e)=>t!==e&&_r(t)&&_r(e)?Object.keys(e).reduce(((n,r)=>{if(Oc(t,r)){const i=Fp(t[r],e[r]);return _r(i)&&Ic(i)||(n[r]=i),n}return n[r]=e[r],n}),kc()):{},jp=(t,e)=>t!==e&&_r(t)&&_r(e)?Object.keys(t).reduce(((n,r)=>{if(Oc(e,r)){const i=jp(t[r],e[r]);return _r(i)&&Ic(i)||(n[r]=i),n}return n[r]=void 0,n}),kc()):{},Lp=(t,e)=>t===e?{}:_r(t)&&_r(e)?ll(t)||ll(e)?t.valueOf()==e.valueOf()?{}:e:Object.keys(e).reduce(((n,r)=>{if(Oc(t,r)){const i=Lp(t[r],e[r]);return cl(i)&&!ll(i)&&(cl(t[r])||!cl(e[r]))||(n[r]=i),n}return n}),kc()):e,E0=(t,e)=>({added:Fp(t,e),deleted:jp(t,e),updated:Lp(t,e)});var ns,rs,Fi,Pa;class A0{constructor(){no(this,ns,[]),no(this,rs,{}),no(this,Fi,new Set),Ja(this,"subscribe",(t=>(Ar(this,Fi).add(t),t(this.generations),()=>Ar(this,Fi).delete(t)))),no(this,Pa,0),Ja(this,"publish",(()=>{if(Hc(this,Pa)._++>1e5)throw new Error("History published too many times.");Ar(this,Fi).forEach((t=>t(this.generations)))}))}get generations(){return[...Ar(this,ns)]}push(t){const e=t=>{let e=Object.entries(t);e.sort(((t,e)=>t[0].localeCompare(e[0])));const n=Object.fromEntries(e);return JSON.parse(JSON.stringify(n))},n=e(Ar(this,rs)),r=e(t),i=E0(n,r);Ar(this,ns).push({...i,before:n,after:r,asof:new Date}),Qa(this,rs,r),this.publish()}}function T0(t){let e;const n=t[5].default,r=ge(n,t,t[8],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||256&i)&&_e(r,n,t,t[8],e?ye(n,t[8],i,null):be(t[8]),null)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function B0(t){let e,n;const r=[{builders:t[3]},{class:jr($c({variant:t[1],size:t[2],className:t[0]}),"hover:bg-base-200 shadow-base-200")},{type:"button"},t[4]];let i={$$slots:{default:[T0]},$$scope:{ctx:t}};for(let t=0;t<r.length;t+=1)i=te(i,r[t]);return e=new Lv({props:i}),e.$on("click",t[6]),e.$on("keydown",t[7]),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,[n]){const i=31&n?rn(r,[8&n&&{builders:t[3]},7&n&&{class:jr($c({variant:t[1],size:t[2],className:t[0]}),"hover:bg-base-200 shadow-base-200")},r[2],16&n&&Ma(t[4])]):{};256&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function P0(t,e,n){const r=["class","variant","size","builders"];let i=Ue(e,r),{$$slots:o={},$$scope:s}=e,{class:a}=e,{variant:l="default"}=e,{size:c="default"}=e,{builders:u=[]}=e;return t.$$set=t=>{e=te(te({},e),Sr(t)),n(4,i=Ue(e,r)),"class"in t&&n(0,a=t.class),"variant"in t&&n(1,l=t.variant),"size"in t&&n(2,c=t.size),"builders"in t&&n(3,u=t.builders),"$$scope"in t&&n(8,s=t.$$scope)},[a,l,c,u,i,o,function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},s]}ns=new WeakMap,rs=new WeakMap,Fi=new WeakMap,Pa=new WeakMap;class M0 extends re{constructor(t){super(),ie(this,t,P0,B0,Zt,{class:0,variant:1,size:2,builders:3})}}function $u(t){let e,n;return{c(){e=Y("span"),n=wt(t[1]),this.h()},l(r){e=q(r,"SPAN",{class:!0});var i=J(e);n=vt(i,t[1]),i.forEach(w),this.h()},h(){C(e,"class","cursor-pointer font-bold pr-8 flex items-center")},m(t,r){L(t,e,r),W(e,n)},p(t,e){2&e&&Re(n,t[1])},d(t){t&&w(e)}}}function N0(t){let e,n,r,i,o,s,a,l,c,u,d=t[1]&&$u(t);return{c(){e=Y("div"),d&&d.c(),n=rt(),r=Y("span"),i=wt(t[2]),this.h()},l(o){e=q(o,"DIV",{role:!0,class:!0});var s=J(e);d&&d.l(s),n=nt(s),r=q(s,"SPAN",{class:!0});var a=J(r);i=vt(a,t[2]),a.forEach(w),s.forEach(w),this.h()},h(){C(r,"class","cursor-pointer"),C(e,"role","none"),C(e,"class",o="print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+tf[t[0]])},m(o,s){L(o,e,s),d&&d.m(e,null),W(e,n),W(e,r),W(r,i),l=!0,c||(u=[Pt(e,"click",t[3]),Pt(e,"keypress",t[3])],c=!0)},p(t,[r]){t[1]?d?d.p(t,r):(d=$u(t),d.c(),d.m(e,n)):d&&(d.d(1),d=null),(!l||4&r)&&Re(i,t[2]),(!l||1&r&&o!==(o="print:hidden rounded py-1 px-3 my-4 mx-0 shadow-md text-xs font-mono flex justify-between transition-all duration-300 border "+tf[t[0]]))&&C(e,"class",o)},i(t){l||(t&&En((()=>{l&&(a&&a.end(1),s=qi(e,Sl,{}),s.start())})),l=!0)},o(t){s&&s.invalidate(),t&&(a=is(e,ii,{x:1e3,duration:1e3,delay:0,opacity:.8})),l=!1},d(t){t&&w(e),d&&d.d(),t&&a&&a.end(),c=!1,zn(u)}}}const Rl={error:"negative",success:"positive"},C0=t=>Object.keys(Rl).includes(t),F0=t=>C0(t)?(console.warn(`[Toast] The status "${t}" is deprecated. Please use "${Rl[t]}" instead.`),Rl[t]):t,tf={negative:"border-negative/50 bg-negative/10 text-negative",positive:"border-positive/50 bg-positive/10 text-positive",info:"border-info/50 bg-info/10 text-info",warning:"border-warning/50 bg-warning/10 text-warning"};function j0(t,e,n){let{id:r}=e,{status:i="info"}=e,{title:o}=e,{message:s}=e,{dismissable:a=!0}=e;const l=Qg();return t.$$set=t=>{"id"in t&&n(4,r=t.id),"status"in t&&n(0,i=t.status),"title"in t&&n(1,o=t.title),"message"in t&&n(2,s=t.message),"dismissable"in t&&n(5,a=t.dismissable)},t.$$.update=()=>{1&t.$$.dirty&&n(0,i=F0(i))},[i,o,s,()=>{a&&l("dismiss",{id:r})},r,a]}class L0 extends re{constructor(t){super(),ie(this,t,j0,N0,Zt,{id:4,status:0,title:1,message:2,dismissable:5})}}function ef(t,e,n){const r=t.slice();return r[2]=e[n],r}function nf(t,e){let n,r,i;const o=[e[2]];let s={};for(let t=0;t<o.length;t+=1)s=te(s,o[t]);return r=new L0({props:s}),r.$on("dismiss",e[1]),{key:t,first:null,c(){n=ut(),lt(r.$$.fragment),this.h()},l(t){n=ut(),at(r.$$.fragment,t),this.h()},h(){this.first=n},m(t,e){L(t,n,e),st(r,t,e),i=!0},p(t,n){e=t;const i=1&n?rn(o,[Ma(e[2])]):{};r.$set(i)},i(t){i||(I(r.$$.fragment,t),i=!0)},o(t){E(r.$$.fragment,t),i=!1},d(t){t&&w(n),ot(r,t)}}}function R0(t){let e,n,r=[],i=new Map,o=he(t[0]);const s=t=>t[2].id;for(let e=0;e<o.length;e+=1){let n=ef(t,o,e),a=s(n);i.set(a,r[e]=nf(a,n))}return{c(){e=Y("div");for(let t=0;t<r.length;t+=1)r[t].c();this.h()},l(t){e=q(t,"DIV",{class:!0});var n=J(e);for(let t=0;t<r.length;t+=1)r[t].l(n);n.forEach(w),this.h()},h(){C(e,"class","z-[1] fixed right-0 bottom-0 mx-10 my-6 w-80")},m(t,i){L(t,e,i);for(let t=0;t<r.length;t+=1)r[t]&&r[t].m(e,null);n=!0},p(t,[n]){1&n&&(o=he(t[0]),jt(),r=Eh(r,n,s,1,t,o,i,e,c_,nf,null,ef),Lt())},i(t){if(!n){for(let t=0;t<o.length;t+=1)I(r[t]);n=!0}},o(t){for(let t=0;t<r.length;t+=1)E(r[t]);n=!1},d(t){t&&w(e);for(let t=0;t<r.length;t+=1)r[t].d()}}}function U0(t,e,n){let r;return Te(t,xc,(t=>n(0,r=t))),[r,({detail:t})=>xc.dismiss(t.id)]}class V0 extends re{constructor(t){super(),ie(this,t,U0,R0,Zt,{})}}const rf="/_app/immutable/assets/wordmark-white.C8ZS96Ri.png",of="/_app/immutable/assets/wordmark-black.rfl-FBgf.png";function z0(t){let e,n,r,i,o;return{c(){e=Y("img"),r=rt(),i=Y("img"),this.h()},l(t){e=q(t,"IMG",{src:!0,alt:!0,class:!0,href:!0}),r=nt(t),i=q(t,"IMG",{src:!0,alt:!0,class:!0,href:!0}),this.h()},h(){vs(e.src,n=t[0]??t[1]??of)||C(e,"src",n),C(e,"alt","Home"),C(e,"class","h-5 aspect-auto block dark:hidden"),C(e,"href",Wt("/")),vs(i.src,o=t[0]??t[2]??rf)||C(i,"src",o),C(i,"alt","Home"),C(i,"class","h-5 aspect-auto hidden dark:block"),C(i,"href",Wt("/"))},m(t,n){L(t,e,n),L(t,r,n),L(t,i,n)},p(t,r){3&r&&!vs(e.src,n=t[0]??t[1]??of)&&C(e,"src",n),5&r&&!vs(i.src,o=t[0]??t[2]??rf)&&C(i,"src",o)},d(t){t&&(w(e),w(r),w(i))}}}function W0(t){let e;return{c(){e=wt(t[3])},l(n){e=vt(n,t[3])},m(t,n){L(t,e,n)},p(t,n){8&n&&Re(e,t[3])},d(t){t&&w(e)}}}function H0(t){let e;function n(t,e){return t[3]?W0:z0}let r=n(t),i=r(t);return{c(){i.c(),e=ut()},l(t){i.l(t),e=ut()},m(t,n){i.m(t,n),L(t,e,n)},p(t,[o]){r===(r=n(t))&&i?i.p(t,o):(i.d(1),i=r(t),i&&(i.c(),i.m(e.parentNode,e)))},i:Xt,o:Xt,d(t){t&&w(e),i.d(t)}}}function x0(t,e,n){let{logo:r}=e,{lightLogo:i}=e,{darkLogo:o}=e,{title:s}=e;return t.$$set=t=>{"logo"in t&&n(0,r=t.logo),"lightLogo"in t&&n(1,i=t.lightLogo),"darkLogo"in t&&n(2,o=t.darkLogo),"title"in t&&n(3,s=t.title)},[r,i,o,s]}class Dc extends re{constructor(t){super(),ie(this,t,x0,H0,Zt,{logo:0,lightLogo:1,darkLogo:2,title:3})}}function Ul(){return Ul=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Ul.apply(null,arguments)}function mt(t){return(mt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var us,Ut,Rp,ti,sf,Up,Vl,Vp,Ec,zl,Wl,zp,Go={},Wp=[],q0=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Ra=Array.isArray;function pr(t,e){for(var n in e)t[n]=e[n];return t}function Ac(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function tr(t,e,n){var r,i,o,s={};for(o in e)"key"==o?r=e[o]:"ref"==o?i=e[o]:s[o]=e[o];if(arguments.length>2&&(s.children=arguments.length>3?us.call(arguments,2):n),"function"==typeof t&&null!=t.defaultProps)for(o in t.defaultProps)void 0===s[o]&&(s[o]=t.defaultProps[o]);return Ao(t,s,r,i,null)}function Ao(t,e,n,r,i){var o={type:t,props:e,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:i??++Rp,__i:-1,__u:0};return null==i&&null!=Ut.vnode&&Ut.vnode(o),o}function br(t){return t.children}function er(t,e){this.props=t,this.context=e}function Hi(t,e){if(null==e)return t.__?Hi(t.__,t.__i+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?Hi(t):null}function Hp(t){var e,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return Hp(t)}}function Hl(t){(!t.__d&&(t.__d=!0)&&ti.push(t)&&!Aa.__r++||sf!==Ut.debounceRendering)&&((sf=Ut.debounceRendering)||Up)(Aa)}function Aa(){var t,e,n,r,i,o,s,a;for(ti.sort(Vl);t=ti.shift();)t.__d&&(e=ti.length,r=void 0,o=(i=(n=t).__v).__e,s=[],a=[],n.__P&&((r=pr({},i)).__v=i.__v+1,Ut.vnode&&Ut.vnode(r),Tc(n.__P,r,i,n.__n,n.__P.namespaceURI,32&i.__u?[o]:null,s,o??Hi(i),!!(32&i.__u),a),r.__v=i.__v,r.__.__k[r.__i]=r,Yp(s,r,a),r.__e!=o&&Hp(r)),ti.length>e&&ti.sort(Vl));Aa.__r=0}function xp(t,e,n,r,i,o,s,a,l,c,u){var d,h,f,p,m,b,y=r&&r.__k||Wp,g=e.length;for(l=function(t,e,n,r,i){var o,s,a,l,c,u=n.length,d=u,h=0;for(t.__k=new Array(i),o=0;o<i;o++)null!=(s=e[o])&&"boolean"!=typeof s&&"function"!=typeof s?(l=o+h,(s=t.__k[o]="string"==typeof s||"number"==typeof s||"bigint"==typeof s||s.constructor==String?Ao(null,s,null,null,null):Ra(s)?Ao(br,{children:s},null,null,null):void 0===s.constructor&&s.__b>0?Ao(s.type,s.props,s.key,s.ref?s.ref:null,s.__v):s).__=t,s.__b=t.__b+1,a=null,-1!==(c=s.__i=Y0(s,n,l,d))&&(d--,(a=n[c])&&(a.__u|=2)),null==a||null===a.__v?(-1==c&&h--,"function"!=typeof s.type&&(s.__u|=4)):c!=l&&(c==l-1?h--:c==l+1?h++:(c>l?h--:h++,s.__u|=4))):t.__k[o]=null;if(d)for(o=0;o<u;o++)null!=(a=n[o])&&!(2&a.__u)&&(a.__e==r&&(r=Hi(a)),Kp(a,a));return r}(n,e,y,l,g),d=0;d<g;d++)null!=(f=n.__k[d])&&(h=-1===f.__i?Go:y[f.__i]||Go,f.__i=d,b=Tc(t,f,h,i,o,s,a,l,c,u),p=f.__e,f.ref&&h.ref!=f.ref&&(h.ref&&Bc(h.ref,null,f),u.push(f.ref,f.__c||p,f)),null==m&&null!=p&&(m=p),4&f.__u||h.__k===f.__k?l=qp(f,l,t):"function"==typeof f.type&&void 0!==b?l=b:p&&(l=p.nextSibling),f.__u&=-7);return n.__e=m,l}function qp(t,e,n){var r,i;if("function"==typeof t.type){for(r=t.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=t,e=qp(r[i],e,n));return e}t.__e!=e&&(e&&t.type&&!n.contains(e)&&(e=Hi(t)),n.insertBefore(t.__e,e||null),e=t.__e);do{e=e&&e.nextSibling}while(null!=e&&8==e.nodeType);return e}function yr(t,e){return e=e||[],null==t||"boolean"==typeof t||(Ra(t)?t.some((function(t){yr(t,e)})):e.push(t)),e}function Y0(t,e,n,r){var i,o,s=t.key,a=t.type,l=e[n];if(null===l||l&&s==l.key&&a===l.type&&!(2&l.__u))return n;if(r>(null==l||2&l.__u?0:1))for(i=n-1,o=n+1;i>=0||o<e.length;){if(i>=0){if((l=e[i])&&!(2&l.__u)&&s==l.key&&a===l.type)return i;i--}if(o<e.length){if((l=e[o])&&!(2&l.__u)&&s==l.key&&a===l.type)return o;o++}}return-1}function af(t,e,n){"-"==e[0]?t.setProperty(e,n??""):t[e]=null==n?"":"number"!=typeof n||q0.test(e)?n:n+"px"}function ks(t,e,n,r,i){var o;t:if("style"==e)if("string"==typeof n)t.style.cssText=n;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||af(t.style,e,"");if(n)for(e in n)r&&n[e]===r[e]||af(t.style,e,n[e])}else if("o"==e[0]&&"n"==e[1])o=e!=(e=e.replace(Vp,"$1")),e=e.toLowerCase()in t||"onFocusOut"==e||"onFocusIn"==e?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+o]=n,n?r?n.u=r.u:(n.u=Ec,t.addEventListener(e,o?Wl:zl,o)):t.removeEventListener(e,o?Wl:zl,o);else{if("http://www.w3.org/2000/svg"==i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=e&&"height"!=e&&"href"!=e&&"list"!=e&&"form"!=e&&"tabIndex"!=e&&"download"!=e&&"rowSpan"!=e&&"colSpan"!=e&&"role"!=e&&"popover"!=e&&e in t)try{t[e]=n??"";break t}catch{}"function"==typeof n||(null==n||!1===n&&"-"!=e[4]?t.removeAttribute(e):t.setAttribute(e,"popover"==e&&1==n?"":n))}}function lf(t){return function(e){if(this.l){var n=this.l[e.type+t];if(null==e.t)e.t=Ec++;else if(e.t<n.u)return;return n(Ut.event?Ut.event(e):e)}}}function Tc(t,e,n,r,i,o,s,a,l,c){var u,d,h,f,p,m,b,y,g,v,_,w,S,I,O,$,x,E=e.type;if(void 0!==e.constructor)return null;128&n.__u&&(l=!!(32&n.__u),o=[a=e.__e=n.__e]),(u=Ut.__b)&&u(e);t:if("function"==typeof E)try{if(y=e.props,g="prototype"in E&&E.prototype.render,v=(u=E.contextType)&&r[u.__c],_=u?v?v.props.value:u.__:r,n.__c?b=(d=e.__c=n.__c).__=d.__E:(g?e.__c=d=new E(y,_):(e.__c=d=new er(y,_),d.constructor=E,d.render=J0),v&&v.sub(d),d.props=y,d.state||(d.state={}),d.context=_,d.__n=r,h=d.__d=!0,d.__h=[],d._sb=[]),g&&null==d.__s&&(d.__s=d.state),g&&null!=E.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=pr({},d.__s)),pr(d.__s,E.getDerivedStateFromProps(y,d.__s))),f=d.props,p=d.state,d.__v=e,h)g&&null==E.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),g&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(g&&null==E.getDerivedStateFromProps&&y!==f&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(y,_),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(y,d.__s,_)||e.__v==n.__v)){for(e.__v!=n.__v&&(d.props=y,d.state=d.__s,d.__d=!1),e.__e=n.__e,e.__k=n.__k,e.__k.some((function(t){t&&(t.__=e)})),w=0;w<d._sb.length;w++)d.__h.push(d._sb[w]);d._sb=[],d.__h.length&&s.push(d);break t}null!=d.componentWillUpdate&&d.componentWillUpdate(y,d.__s,_),g&&null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(f,p,m)}))}if(d.context=_,d.props=y,d.__P=t,d.__e=!1,S=Ut.__r,I=0,g){for(d.state=d.__s,d.__d=!1,S&&S(e),u=d.render(d.props,d.state,d.context),O=0;O<d._sb.length;O++)d.__h.push(d._sb[O]);d._sb=[]}else do{d.__d=!1,S&&S(e),u=d.render(d.props,d.state,d.context),d.state=d.__s}while(d.__d&&++I<25);d.state=d.__s,null!=d.getChildContext&&(r=pr(pr({},r),d.getChildContext())),g&&!h&&null!=d.getSnapshotBeforeUpdate&&(m=d.getSnapshotBeforeUpdate(f,p)),a=xp(t,Ra($=null!=u&&u.type===br&&null==u.key?u.props.children:u)?$:[$],e,n,r,i,o,s,a,l,c),d.base=e.__e,e.__u&=-161,d.__h.length&&s.push(d),b&&(d.__E=d.__=null)}catch(t){if(e.__v=null,l||null!=o)if(t.then){for(e.__u|=l?160:128;a&&8==a.nodeType&&a.nextSibling;)a=a.nextSibling;o[o.indexOf(a)]=null,e.__e=a}else for(x=o.length;x--;)Ac(o[x]);else e.__e=n.__e,e.__k=n.__k;Ut.__e(t,e,n)}else null==o&&e.__v==n.__v?(e.__k=n.__k,e.__e=n.__e):a=e.__e=K0(n.__e,e,n,r,i,o,s,l,c);return(u=Ut.diffed)&&u(e),128&e.__u?void 0:a}function Yp(t,e,n){for(var r=0;r<n.length;r++)Bc(n[r],n[++r],n[++r]);Ut.__c&&Ut.__c(e,t),t.some((function(e){try{t=e.__h,e.__h=[],t.some((function(t){t.call(e)}))}catch(t){Ut.__e(t,e.__v)}}))}function K0(t,e,n,r,i,o,s,a,l){var c,u,d,h,f,p,m,b=n.props,y=e.props,g=e.type;if("svg"==g?i="http://www.w3.org/2000/svg":"math"==g?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=o)for(c=0;c<o.length;c++)if((f=o[c])&&"setAttribute"in f==!!g&&(g?f.localName==g:3==f.nodeType)){t=f,o[c]=null;break}if(null==t){if(null==g)return document.createTextNode(y);t=document.createElementNS(i,g,y.is&&y),a&&(Ut.__m&&Ut.__m(e,o),a=!1),o=null}if(null===g)b===y||a&&t.data===y||(t.data=y);else{if(o=o&&us.call(t.childNodes),b=n.props||Go,!a&&null!=o)for(b={},c=0;c<t.attributes.length;c++)b[(f=t.attributes[c]).name]=f.value;for(c in b)if(f=b[c],"children"!=c)if("dangerouslySetInnerHTML"==c)d=f;else if(!(c in y)){if("value"==c&&"defaultValue"in y||"checked"==c&&"defaultChecked"in y)continue;ks(t,c,null,f,i)}for(c in y)f=y[c],"children"==c?h=f:"dangerouslySetInnerHTML"==c?u=f:"value"==c?p=f:"checked"==c?m=f:a&&"function"!=typeof f||b[c]===f||ks(t,c,f,b[c],i);if(u)a||d&&(u.__html===d.__html||u.__html===t.innerHTML)||(t.innerHTML=u.__html),e.__k=[];else if(d&&(t.innerHTML=""),xp(t,Ra(h)?h:[h],e,n,r,"foreignObject"==g?"http://www.w3.org/1999/xhtml":i,o,s,o?o[0]:n.__k&&Hi(n,0),a,l),null!=o)for(c=o.length;c--;)Ac(o[c]);a||(c="value","progress"==g&&null==p?t.removeAttribute("value"):void 0!==p&&(p!==t[c]||"progress"==g&&!p||"option"==g&&p!==b[c])&&ks(t,c,p,b[c],i),c="checked",void 0!==m&&m!==t[c]&&ks(t,c,m,b[c],i))}return t}function Bc(t,e,n){try{if("function"==typeof t){var r="function"==typeof t.__u;r&&t.__u(),r&&null==e||(t.__u=t(e))}else t.current=e}catch(t){Ut.__e(t,n)}}function Kp(t,e,n){var r,i;if(Ut.unmount&&Ut.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||Bc(r,null,e)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(t){Ut.__e(t,e)}r.base=r.__P=null}if(r=t.__k)for(i=0;i<r.length;i++)r[i]&&Kp(r[i],e,n||"function"!=typeof t.type);n||Ac(t.__e),t.__c=t.__=t.__e=void 0}function J0(t,e,n){return this.constructor(t,n)}function Xo(t,e,n){var r,i,o,s;e==document&&(e=document.documentElement),Ut.__&&Ut.__(t,e),i=(r="function"==typeof n)?null:n&&n.__k||e.__k,o=[],s=[],Tc(e,t=(!r&&n||e).__k=tr(br,null,[t]),i||Go,Go,e.namespaceURI,!r&&n?[n]:i?null:e.firstChild?us.call(e.childNodes):null,o,!r&&n?n:i?i.__e:e.firstChild,r,s),Yp(o,t,s)}function Jp(t,e){Xo(t,e,Jp)}function Q0(t,e,n){var r,i,o,s,a=pr({},t.props);for(o in t.type&&t.type.defaultProps&&(s=t.type.defaultProps),e)"key"==o?r=e[o]:"ref"==o?i=e[o]:a[o]=void 0===e[o]&&void 0!==s?s[o]:e[o];return arguments.length>2&&(a.children=arguments.length>3?us.call(arguments,2):n),Ao(t.type,a,r||t.key,i||t.ref,null)}us=Wp.slice,Ut={__e:function(t,e,n,r){for(var i,o,s;e=e.__;)if((i=e.__c)&&!i.__)try{if((o=i.constructor)&&null!=o.getDerivedStateFromError&&(i.setState(o.getDerivedStateFromError(t)),s=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(t,r||{}),s=i.__d),s)return i.__E=i}catch(e){t=e}throw t}},Rp=0,er.prototype.setState=function(t,e){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=pr({},this.state),"function"==typeof t&&(t=t(pr({},n),this.props)),t&&pr(n,t),null!=t&&this.__v&&(e&&this._sb.push(e),Hl(this))},er.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),Hl(this))},er.prototype.render=br,ti=[],Up="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Vl=function(t,e){return t.__v.__b-e.__v.__b},Aa.__r=0,Vp=/(PointerCapture)$|Capture$/i,Ec=0,zl=lf(!1),Wl=lf(!0),zp=0;var Ur,we,ul,cf,xi=0,Qp=[],Ee=Ut,uf=Ee.__b,ff=Ee.__r,df=Ee.diffed,hf=Ee.__c,mf=Ee.unmount,pf=Ee.__;function Ji(t,e){Ee.__h&&Ee.__h(we,t,xi||e),xi=0;var n=we.__H||(we.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({}),n.__[t]}function fs(t){return xi=1,Pc(ng,t)}function Pc(t,e,n){var r=Ji(Ur++,2);if(r.t=t,!r.__c&&(r.__=[n?n(e):ng(void 0,e),function(t){var e=r.__N?r.__N[0]:r.__[0],n=r.t(e,t);e!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=we,!we.u)){var i=function(t,e,n){if(!r.__c.__H)return!0;var i=r.__c.__H.__.filter((function(t){return!!t.__c}));if(i.every((function(t){return!t.__N})))return!o||o.call(this,t,e,n);var s=r.__c.props!==t;return i.forEach((function(t){if(t.__N){var e=t.__[0];t.__=t.__N,t.__N=void 0,e!==t.__[0]&&(s=!0)}})),o&&o.call(this,t,e,n)||s};we.u=!0;var o=we.shouldComponentUpdate,s=we.componentWillUpdate;we.componentWillUpdate=function(t,e,n){if(this.__e){var r=o;o=void 0,i(t,e,n),o=r}s&&s.call(this,t,e,n)},we.shouldComponentUpdate=i}return r.__N||r.__}function ds(t,e){var n=Ji(Ur++,3);!Ee.__s&&Mc(n.__H,e)&&(n.__=t,n.i=e,we.__H.__h.push(n))}function hs(t,e){var n=Ji(Ur++,4);!Ee.__s&&Mc(n.__H,e)&&(n.__=t,n.i=e,we.__h.push(n))}function Gp(t){return xi=5,Ua((function(){return{current:t}}),[])}function Xp(t,e,n){xi=6,hs((function(){return"function"==typeof t?(t(e()),function(){return t(null)}):t?(t.current=e(),function(){return t.current=null}):void 0}),null==n?n:n.concat(t))}function Ua(t,e){var n=Ji(Ur++,7);return Mc(n.__H,e)&&(n.__=t(),n.__H=e,n.__h=t),n.__}function Zp(t,e){return xi=8,Ua((function(){return t}),e)}function $p(t){var e=we.context[t.__c],n=Ji(Ur++,9);return n.c=t,e?(null==n.__&&(n.__=!0,e.sub(we)),e.props.value):t.__}function tg(t,e){Ee.useDebugValue&&Ee.useDebugValue(e?e(t):t)}function eg(){var t=Ji(Ur++,11);if(!t.__){for(var e=we.__v;null!==e&&!e.__m&&null!==e.__;)e=e.__;var n=e.__m||(e.__m=[0,0]);t.__="P"+n[0]+"-"+n[1]++}return t.__}function G0(){for(var t;t=Qp.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(Ys),t.__H.__h.forEach(xl),t.__H.__h=[]}catch(e){t.__H.__h=[],Ee.__e(e,t.__v)}}Ee.__b=function(t){we=null,uf&&uf(t)},Ee.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),pf&&pf(t,e)},Ee.__r=function(t){ff&&ff(t),Ur=0;var e=(we=t.__c).__H;e&&(ul===we?(e.__h=[],we.__h=[],e.__.forEach((function(t){t.__N&&(t.__=t.__N),t.i=t.__N=void 0}))):(e.__h.forEach(Ys),e.__h.forEach(xl),e.__h=[],Ur=0)),ul=we},Ee.diffed=function(t){df&&df(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(1!==Qp.push(e)&&cf===Ee.requestAnimationFrame||((cf=Ee.requestAnimationFrame)||X0)(G0)),e.__H.__.forEach((function(t){t.i&&(t.__H=t.i),t.i=void 0}))),ul=we=null},Ee.__c=function(t,e){e.some((function(t){try{t.__h.forEach(Ys),t.__h=t.__h.filter((function(t){return!t.__||xl(t)}))}catch(n){e.some((function(t){t.__h&&(t.__h=[])})),e=[],Ee.__e(n,t.__v)}})),hf&&hf(t,e)},Ee.unmount=function(t){mf&&mf(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach((function(t){try{Ys(t)}catch(t){e=t}})),n.__H=void 0,e&&Ee.__e(e,n.__v))};var gf="function"==typeof requestAnimationFrame;function X0(t){var e,n=function(){clearTimeout(r),gf&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,100);gf&&(e=requestAnimationFrame(n))}function Ys(t){var e=we,n=t.__c;"function"==typeof n&&(t.__c=void 0,n()),we=e}function xl(t){var e=we;t.__c=t.__(),we=e}function Mc(t,e){return!t||t.length!==e.length||e.some((function(e,n){return e!==t[n]}))}function ng(t,e){return"function"==typeof e?e(t):e}function rg(t,e){for(var n in e)t[n]=e[n];return t}function ql(t,e){for(var n in t)if("__source"!==n&&!(n in e))return!0;for(var r in e)if("__source"!==r&&t[r]!==e[r])return!0;return!1}function ig(t,e){var n=e(),r=fs({t:{__:n,u:e}}),i=r[0].t,o=r[1];return hs((function(){i.__=n,i.u=e,fl(i)&&o({t:i})}),[t,n,e]),ds((function(){return fl(i)&&o({t:i}),t((function(){fl(i)&&o({t:i})}))}),[t]),n}function fl(t){var e,n,r=t.u,i=t.__;try{var o=r();return!((e=i)===(n=o)&&(0!==e||1/e==1/n)||e!=e&&n!=n)}catch{return!0}}function og(t){t()}function sg(t){return t}function ag(){return[!1,og]}var lg=hs;function Yl(t,e){this.props=t,this.context=e}(Yl.prototype=new er).isPureReactComponent=!0,Yl.prototype.shouldComponentUpdate=function(t,e){return ql(this.props,t)||ql(this.state,e)};var _f=Ut.__b;Ut.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),_f&&_f(t)};var Z0=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911,bf=function(t,e){return null==t?null:yr(yr(t).map(e))},$0={map:bf,forEach:bf,count:function(t){return t?yr(t).length:0},only:function(t){var e=yr(t);if(1!==e.length)throw"Children.only";return e[0]},toArray:yr},t1=Ut.__e;Ut.__e=function(t,e,n,r){if(t.then)for(var i,o=e;o=o.__;)if((i=o.__c)&&i.__c)return null==e.__e&&(e.__e=n.__e,e.__k=n.__k),i.__c(t,e);t1(t,e,n,r)};var yf=Ut.unmount;function cg(t,e,n){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach((function(t){"function"==typeof t.__c&&t.__c()})),t.__c.__H=null),null!=(t=rg({},t)).__c&&(t.__c.__P===n&&(t.__c.__P=e),t.__c=null),t.__k=t.__k&&t.__k.map((function(t){return cg(t,e,n)}))),t}function ug(t,e,n){return t&&n&&(t.__v=null,t.__k=t.__k&&t.__k.map((function(t){return ug(t,e,n)})),t.__c&&t.__c.__P===e&&(t.__e&&n.appendChild(t.__e),t.__c.__e=!0,t.__c.__P=n)),t}function Ks(){this.__u=0,this.o=null,this.__b=null}function fg(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function Io(){this.i=null,this.l=null}Ut.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&32&t.__u&&(t.type=null),yf&&yf(t)},(Ks.prototype=new er).__c=function(t,e){var n=e.__c,r=this;null==r.o&&(r.o=[]),r.o.push(n);var i=fg(r.__v),o=!1,s=function(){o||(o=!0,n.__R=null,i?i(a):a())};n.__R=s;var a=function(){if(! --r.__u){if(r.state.__a){var t=r.state.__a;r.__v.__k[0]=ug(t,t.__c.__P,t.__c.__O)}var e;for(r.setState({__a:r.__b=null});e=r.o.pop();)e.forceUpdate()}};r.__u++||32&e.__u||r.setState({__a:r.__b=r.__v.__k[0]}),t.then(s,s)},Ks.prototype.componentWillUnmount=function(){this.o=[]},Ks.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=cg(this.__b,n,r.__O=r.__P)}this.__b=null}var i=e.__a&&tr(br,null,t.fallback);return i&&(i.__u&=-33),[tr(br,null,e.__a?null:t.children),i]};var vf=function(t,e,n){if(++n[1]===n[0]&&t.l.delete(e),t.props.revealOrder&&("t"!==t.props.revealOrder[0]||!t.l.size))for(n=t.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;t.i=n=n[2]}};function e1(t){return this.getChildContext=function(){return t.context},t.children}function n1(t){var e=this,n=t.h;e.componentWillUnmount=function(){Xo(null,e.v),e.v=null,e.h=null},e.h&&e.h!==n&&e.componentWillUnmount(),e.v||(e.h=n,e.v={nodeType:1,parentNode:n,childNodes:[],contains:function(){return!0},appendChild:function(t){this.childNodes.push(t),e.h.appendChild(t)},insertBefore:function(t,n){this.childNodes.push(t),e.h.insertBefore(t,n)},removeChild:function(t){this.childNodes.splice(this.childNodes.indexOf(t)>>>1,1),e.h.removeChild(t)}}),Xo(tr(e1,{context:e.context},t.__v),e.v)}function dg(t,e){var n=tr(n1,{__v:t,h:e});return n.containerInfo=e,n}(Io.prototype=new er).__a=function(t){var e=this,n=fg(e.__v),r=e.l.get(t);return r[0]++,function(i){var o=function(){e.props.revealOrder?(r.push(i),vf(e,t,r)):i()};n?n(o):o()}},Io.prototype.render=function(t){this.i=null,this.l=new Map;var e=yr(t.children);t.revealOrder&&"b"===t.revealOrder[0]&&e.reverse();for(var n=e.length;n--;)this.l.set(e[n],this.i=[1,0,this.i]);return t.children},Io.prototype.componentDidUpdate=Io.prototype.componentDidMount=function(){var t=this;this.l.forEach((function(e,n){vf(t,n,e)}))};var hg=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,r1=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,i1=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,o1=/[A-Z0-9]/g,s1=typeof document<"u",a1=function(t){return(typeof Symbol<"u"&&"symbol"==mt(Symbol())?/fil|che|rad/:/fil|che|ra/).test(t)};function mg(t,e,n){return null==e.__k&&(e.textContent=""),Xo(t,e),"function"==typeof n&&n(),t?t.__c:null}er.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(t){Object.defineProperty(er.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})}));var wf=Ut.event;function l1(){}function c1(){return this.cancelBubble}function u1(){return this.defaultPrevented}Ut.event=function(t){return wf&&(t=wf(t)),t.persist=l1,t.isPropagationStopped=c1,t.isDefaultPrevented=u1,t.nativeEvent=t};var Nc,f1={enumerable:!1,configurable:!0,get:function(){return this.class}},Sf=Ut.vnode;Ut.vnode=function(t){"string"==typeof t.type&&function(t){var e=t.props,n=t.type,r={},i=-1===n.indexOf("-");for(var o in e){var s=e[o];if(!("value"===o&&"defaultValue"in e&&null==s||s1&&"children"===o&&"noscript"===n||"class"===o||"className"===o)){var a=o.toLowerCase();"defaultValue"===o&&"value"in e&&null==e.value?o="value":"download"===o&&!0===s?s="":"translate"===a&&"no"===s?s=!1:"o"===a[0]&&"n"===a[1]?"ondoubleclick"===a?o="ondblclick":"onchange"!==a||"input"!==n&&"textarea"!==n||a1(e.type)?"onfocus"===a?o="onfocusin":"onblur"===a?o="onfocusout":i1.test(o)&&(o=a):a=o="oninput":i&&r1.test(o)?o=o.replace(o1,"-$&").toLowerCase():null===s&&(s=void 0),"oninput"===a&&r[o=a]&&(o="oninputCapture"),r[o]=s}}"select"==n&&r.multiple&&Array.isArray(r.value)&&(r.value=yr(e.children).forEach((function(t){t.props.selected=-1!=r.value.indexOf(t.props.value)}))),"select"==n&&null!=r.defaultValue&&(r.value=yr(e.children).forEach((function(t){t.props.selected=r.multiple?-1!=r.defaultValue.indexOf(t.props.value):r.defaultValue==t.props.value}))),e.class&&!e.className?(r.class=e.class,Object.defineProperty(r,"className",f1)):(e.className&&!e.class||e.class&&e.className)&&(r.class=r.className=e.className),t.props=r}(t),t.$$typeof=hg,Sf&&Sf(t)};var If=Ut.__r;Ut.__r=function(t){If&&If(t),Nc=t.__c};var Of=Ut.diffed;Ut.diffed=function(t){Of&&Of(t);var e=t.props,n=t.__e;null!=n&&"textarea"===t.type&&"value"in e&&e.value!==n.value&&(n.value=null==e.value?"":e.value),Nc=null};var d1={ReactCurrentDispatcher:{current:{readContext:function(t){return Nc.__n[t.__c].props.value},useCallback:Zp,useContext:$p,useDebugValue:tg,useDeferredValue:sg,useEffect:ds,useId:eg,useImperativeHandle:Xp,useInsertionEffect:lg,useLayoutEffect:hs,useMemo:Ua,useReducer:Pc,useRef:Gp,useState:fs,useSyncExternalStore:ig,useTransition:ag}}};function Ds(t){return!!t&&t.$$typeof===hg}var k={useState:fs,useId:eg,useReducer:Pc,useEffect:ds,useLayoutEffect:hs,useInsertionEffect:lg,useTransition:ag,useDeferredValue:sg,useSyncExternalStore:ig,startTransition:og,useRef:Gp,useImperativeHandle:Xp,useMemo:Ua,useCallback:Zp,useContext:$p,useDebugValue:tg,version:"18.3.1",Children:$0,render:mg,hydrate:function(t,e,n){return Jp(t,e),"function"==typeof n&&n(),t?t.__c:null},unmountComponentAtNode:function(t){return!!t.__k&&(Xo(null,t),!0)},createPortal:dg,createElement:tr,createContext:function(t,e){var n={__c:e="__cC"+zp++,__:t,Consumer:function(t,e){return t.children(e)},Provider:function(t){var n,r;return this.getChildContext||(n=new Set,(r={})[e]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(t){this.props.value!==t.value&&n.forEach((function(t){t.__e=!0,Hl(t)}))},this.sub=function(t){n.add(t);var e=t.componentWillUnmount;t.componentWillUnmount=function(){n&&n.delete(t),e&&e.call(t)}}),t.children}};return n.Provider.__=n.Consumer.contextType=n},createFactory:function(t){return tr.bind(null,t)},cloneElement:function(t){return Ds(t)?Q0.apply(null,arguments):t},createRef:function(){return{current:null}},Fragment:br,isValidElement:Ds,isElement:Ds,isFragment:function(t){return Ds(t)&&t.type===br},isMemo:function(t){return!!t&&!!t.displayName&&("string"==typeof t.displayName||t.displayName instanceof String)&&t.displayName.startsWith("Memo(")},findDOMNode:function(t){return t&&(t.base||1===t.nodeType&&t)||null},Component:er,PureComponent:Yl,memo:function(t,e){function n(t){var n=this.props.ref,r=n==t.ref;return!r&&n&&(n.call?n(null):n.current=null),e?!e(this.props,t)||!r:ql(this.props,t)}function r(e){return this.shouldComponentUpdate=n,tr(t,e)}return r.displayName="Memo("+(t.displayName||t.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r},forwardRef:function(t){function e(e){var n=rg({},e);return delete n.ref,t(n,e.ref||null)}return e.$$typeof=Z0,e.render=e,e.prototype.isReactComponent=e.__f=!0,e.displayName="ForwardRef("+(t.displayName||t.name)+")",e},flushSync:function(t,e){return t(e)},unstable_batchedUpdates:function(t,e){return t(e)},StrictMode:br,Suspense:Ks,SuspenseList:Io,lazy:function(t){var e,n,r;function i(i){if(e||(e=t()).then((function(t){n=t.default||t}),(function(t){r=t})),r)throw r;if(!n)throw e;return tr(n,i)}return i.displayName="Lazy",i.__f=!0,i},__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:d1};function Kl(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function kf(t,e,n,r,i,o,s){try{var a=t[o](s),l=a.value}catch(t){return void n(t)}a.done?e(l):Promise.resolve(l).then(r,i)}function dl(t){return function(){var e=this,n=arguments;return new Promise((function(r,i){var o=t.apply(e,n);function s(t){kf(o,r,i,s,a,"next",t)}function a(t){kf(o,r,i,s,a,"throw",t)}s(void 0)}))}}function Qi(t,e,n){return e=Zo(e),function(t,e){if(e&&("object"==mt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Cc()?Reflect.construct(e,n||[],Zo(t).constructor):e.apply(t,n))}function Gi(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Xi(t,e,n){return Object.defineProperty(t,"prototype",{writable:!1}),t}function Zi(t,e,n){return r=function(t){if("object"!=mt(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!=mt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"==mt(r)?r:r+"")in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Qe(){return Qe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Qe.apply(null,arguments)}function Zo(t){return(Zo=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function $i(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$o(t,e)}function Cc(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch{}return(Cc=function(){return!!t})()}function Df(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Vt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Df(Object(n),!0).forEach((function(e){Zi(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Df(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function nr(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n={};for(var r in t)if({}.hasOwnProperty.call(t,r)){if(e.includes(r))continue;n[r]=t[r]}return n}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.includes(n)||{}.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function Zr(){Zr=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch{c=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var o=e&&e.prototype instanceof y?e:y,s=Object.create(o.prototype),a=new D(r||[]);return i(s,"_invoke",{value:x(t,n,a)}),s}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var h="suspendedStart",f="suspendedYield",p="executing",m="completed",b={};function y(){}function g(){}function v(){}var _={};c(_,s,(function(){return this}));var w=Object.getPrototypeOf,S=w&&w(w(T([])));S&&S!==n&&r.call(S,s)&&(_=S);var I=v.prototype=y.prototype=Object.create(_);function O(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function $(t,e){function n(i,o,s,a){var l=d(t[i],t,o);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"==mt(u)&&r.call(u,"__await")?e.resolve(u.__await).then((function(t){n("next",t,s,a)}),(function(t){n("throw",t,s,a)})):e.resolve(u).then((function(t){c.value=t,s(c)}),(function(t){return n("throw",t,s,a)}))}a(l.arg)}var o;i(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,i){n(t,r,e,i)}))}return o=o?o.then(i,i):i()}})}function x(e,n,r){var i=h;return function(o,s){if(i===p)throw Error("Generator is already running");if(i===m){if("throw"===o)throw s;return{value:t,done:!0}}for(r.method=o,r.arg=s;;){var a=r.delegate;if(a){var l=E(a,r);if(l){if(l===b)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===h)throw i=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=p;var c=d(e,n,r);if("normal"===c.type){if(i=r.done?m:f,c.arg===b)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=m,r.method="throw",r.arg=c.arg)}}}function E(e,n){var r=n.method,i=e.iterator[r];if(i===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var o=d(i,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,b;var s=o.arg;return s?s.done?(n[e.resultName]=s.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,b):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function k(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function A(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(k,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[s];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,o=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return o.next=o}}throw new TypeError(mt(e)+" is not iterable")}return g.prototype=v,i(I,"constructor",{value:v,configurable:!0}),i(v,"constructor",{value:g,configurable:!0}),g.displayName=c(v,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,c(t,l,"GeneratorFunction")),t.prototype=Object.create(I),t},e.awrap=function(t){return{__await:t}},O($.prototype),c($.prototype,a,(function(){return this})),e.AsyncIterator=$,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var s=new $(u(t,n,r,i),o);return e.isGeneratorFunction(n)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},O(I),c(I,l,"Generator"),c(I,s,(function(){return this})),c(I,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=T,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return a.type="throw",a.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return i("end");if(s.tryLoc<=this.prev){var l=r.call(s,"catchLoc"),c=r.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return i(s.catchLoc,!0);if(this.prev<s.finallyLoc)return i(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return i(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return i(s.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=t,s.arg=e,o?(this.method="next",this.next=o.finallyLoc,b):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),b},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),A(n),b}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;A(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:T(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),b}},e}function $o(t,e){return($o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,o,s,a=[],l=!0,c=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(a.push(r.value),a.length!==e);l=!0);}catch(t){c=!0,i=t}finally{try{if(!l&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(c)throw i}}return a}}(t,e)||pg(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function To(t){return function(t){if(Array.isArray(t))return Kl(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||pg(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pg(t,e){if(t){if("string"==typeof t)return Kl(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Kl(t,e):void 0}}function Jl(t){var e="function"==typeof Map?new Map:void 0;return Jl=function(t){if(null===t||!function(t){try{return-1!==Function.toString.call(t).indexOf("[native code]")}catch{return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return function(t,e,n){if(Cc())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,e);var i=new(t.bind.apply(t,r));return n&&$o(i,n.prototype),i}(t,arguments,Zo(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),$o(n,t)},Jl(t)}function h1(){return k.createElement("svg",{width:"15",height:"15",className:"DocSearch-Control-Key-Icon"},k.createElement("path",{d:"M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953",strokeWidth:"1.2",stroke:"currentColor",fill:"none",strokeLinecap:"square"}))}function gg(){return k.createElement("svg",{width:"20",height:"20",className:"DocSearch-Search-Icon",viewBox:"0 0 20 20","aria-hidden":"true"},k.createElement("path",{d:"M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}var m1=["translations"],hl="Ctrl",p1=k.forwardRef((function(t,e){var n=t.translations,r=void 0===n?{}:n,i=nr(t,m1),o=r.buttonText,s=void 0===o?"Search":o,a=r.buttonAriaLabel,l=void 0===a?"Search":a,c=fn(fs(null),2),u=c[0],d=c[1];ds((function(){typeof navigator<"u"&&(/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)?d("⌘"):d(hl))}),[]);var h=fn(u===hl?[hl,"Ctrl",k.createElement(h1,null)]:["Meta","Command",u],3),f=h[0],p=h[1],m=h[2];return k.createElement("button",Qe({type:"button",className:"DocSearch DocSearch-Button","aria-label":"".concat(l," (").concat(p,"+K)")},i,{ref:e}),k.createElement("span",{className:"DocSearch-Button-Container"},k.createElement(gg,null),k.createElement("span",{className:"DocSearch-Button-Placeholder"},s)),k.createElement("span",{className:"DocSearch-Button-Keys"},null!==u&&k.createElement(k.Fragment,null,k.createElement(Ef,{reactsToKey:f},m),k.createElement(Ef,{reactsToKey:"k"},"K"))))}));function Ef(t){var e=t.reactsToKey,n=t.children,r=fn(fs(!1),2),i=r[0],o=r[1];return ds((function(){if(e)return window.addEventListener("keydown",t),window.addEventListener("keyup",n),function(){window.removeEventListener("keydown",t),window.removeEventListener("keyup",n)};function t(t){t.key===e&&o(!0)}function n(t){t.key!==e&&"Meta"!==t.key||o(!1)}}),[e]),k.createElement("kbd",{className:i?"DocSearch-Button-Key DocSearch-Button-Key--pressed":"DocSearch-Button-Key"},n)}function _g(t,e){var n=void 0;return function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];n&&clearTimeout(n),n=setTimeout((function(){return t.apply(void 0,i)}),e)}}function ts(t){return t.reduce((function(t,e){return t.concat(e)}),[])}var g1=0;function Ql(t){return 0===t.collections.length?0:t.collections.reduce((function(t,e){return t+e.items.length}),0)}function Af(t){return t!==Object(t)}function bg(t,e){if(t===e)return!0;if(Af(t)||Af(e)||"function"==typeof t||"function"==typeof e)return t===e;if(Object.keys(t).length!==Object.keys(e).length)return!1;for(var n=0,r=Object.keys(t);n<r.length;n++){var i=r[n];if(!(i in e)||!bg(t[i],e[i]))return!1}return!0}var Js=function(){},_1=[{segment:"autocomplete-core",version:"1.17.9"}];function Tf(t){var e=t.item,n=t.items,r=void 0===n?[]:n;return{index:e.__autocomplete_indexName,items:[e],positions:[1+r.findIndex((function(t){return t.objectID===e.objectID}))],queryID:e.__autocomplete_queryID,algoliaSource:["autocomplete"]}}function Bf(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}var b1=["items"],y1=["items"];function Bo(t){return(Bo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Es(t){return function(t){if(Array.isArray(t))return ml(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ml(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ml(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ml(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function yg(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function Pf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Ai(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Pf(Object(n),!0).forEach((function(e){v1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Pf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function v1(t,e,n){return r=function(t){if("object"!==Bo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Bo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Bo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function As(t){return t.map((function(t){var e=t.items,n=yg(t,b1);return Ai(Ai({},n),{},{objectIDs:(null==e?void 0:e.map((function(t){return t.objectID})))||n.objectIDs})}))}function w1(t){var e=t.items.reduce((function(t,e){var n;return t[e.__autocomplete_indexName]=(null!==(n=t[e.__autocomplete_indexName])&&void 0!==n?n:[]).concat(e),t}),{});return Object.keys(e).map((function(t){return{index:t,items:e[t],algoliaSource:["autocomplete"]}}))}function lo(t){return t.objectID&&t.__autocomplete_indexName&&t.__autocomplete_queryID}function Po(t){return(Po="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Ti(t){return function(t){if(Array.isArray(t))return pl(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pl(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?pl(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pl(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Mf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Fn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Mf(Object(n),!0).forEach((function(e){S1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Mf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function S1(t,e,n){return r=function(t){if("object"!==Po(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Po(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Po(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}var vg="2.15.0",I1="https://cdn.jsdelivr.net/npm/search-insights@".concat(vg,"/dist/search-insights.min.js"),O1=_g((function(t){var e=t.onItemsChange,n=t.items,r=t.insights,i=t.state;e({insights:r,insightsEvents:w1({items:n}).map((function(t){return Fn({eventName:"Items Viewed"},t)})),state:i})}),400);function Nf(t){var e,n,r=Fn({onItemsChange:function(t){var e=t.insights,n=t.insightsEvents,r=t.state;e.viewedObjectIDs.apply(e,Ti(n.map((function(t){return Fn(Fn({},t),{},{algoliaSource:Cf(t.algoliaSource,r.context)})}))))},onSelect:function(t){var e=t.insights,n=t.insightsEvents,r=t.state;e.clickedObjectIDsAfterSearch.apply(e,Ti(n.map((function(t){return Fn(Fn({},t),{},{algoliaSource:Cf(t.algoliaSource,r.context)})}))))},onActive:Js,__autocomplete_clickAnalytics:!0},t),i=r.insightsClient,o=r.insightsInitParams,s=r.onItemsChange,a=r.onSelect,l=r.onActive,c=r.__autocomplete_clickAnalytics,u=i;if(i||typeof window<"u"&&(e=window,"string"==typeof(n=e.AlgoliaAnalyticsObject||"aa")&&(u=e[n]),u||(e.AlgoliaAnalyticsObject=n,e[n]||(e[n]=function(){e[n].queue||(e[n].queue=[]);for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];e[n].queue.push(r)}),e[n].version=vg,u=e[n],function(t){var e="[Autocomplete]: Could not load search-insights.js. Please load it manually following https://alg.li/insights-autocomplete";try{var n=t.document.createElement("script");n.async=!0,n.src=I1,n.onerror=function(){console.error(e)},document.body.appendChild(n)}catch{console.error(e)}}(e))),!u)return{};o&&u("init",Fn({partial:!0},o));var d=function(t){var e,n,r,i,o=(n=(e=function(t){if(Array.isArray(t))return t}(i=(t.version||"").split(".").map(Number))||function(t){var e=null==t?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(null!=e){var n,r,i,o,s=[],a=!0,l=!1;try{for(i=(e=e.call(t)).next;!(a=(n=i.call(e)).done)&&(s.push(n.value),2!==s.length);a=!0);}catch(t){l=!0,r=t}finally{try{if(!a&&null!=e.return&&(o=e.return(),Object(o)!==o))return}finally{if(l)throw r}}return s}}(i)||function(t){if(t){if("string"==typeof t)return Bf(t,2);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Bf(t,2):void 0}}(i)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0],r=e[1],n>=3||2===n&&r>=4||1===n&&r>=10);function s(e,n,r){if(o&&void 0!==r){var i=r[0].__autocomplete_algoliaCredentials,s={"X-Algolia-Application-Id":i.appId,"X-Algolia-API-Key":i.apiKey};t.apply(void 0,[e].concat(Es(n),[{headers:s}]))}else t.apply(void 0,[e].concat(Es(n)))}return{init:function(e,n){t("init",{appId:e,apiKey:n})},setAuthenticatedUserToken:function(e){t("setAuthenticatedUserToken",e)},setUserToken:function(e){t("setUserToken",e)},clickedObjectIDsAfterSearch:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&s("clickedObjectIDsAfterSearch",As(e),e[0].items)},clickedObjectIDs:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&s("clickedObjectIDs",As(e),e[0].items)},clickedFilters:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];n.length>0&&t.apply(void 0,["clickedFilters"].concat(n))},convertedObjectIDsAfterSearch:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&s("convertedObjectIDsAfterSearch",As(e),e[0].items)},convertedObjectIDs:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&s("convertedObjectIDs",As(e),e[0].items)},convertedFilters:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];n.length>0&&t.apply(void 0,["convertedFilters"].concat(n))},viewedObjectIDs:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e.length>0&&e.reduce((function(t,e){var n=e.items,r=yg(e,y1);return[].concat(Es(t),Es(function(t){for(var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,n=[],r=0;r<t.objectIDs.length;r+=e)n.push(Ai(Ai({},t),{},{objectIDs:t.objectIDs.slice(r,r+e)}));return n}(Ai(Ai({},r),{},{objectIDs:(null==n?void 0:n.map((function(t){return t.objectID})))||r.objectIDs})).map((function(t){return{items:n,payload:t}}))))}),[]).forEach((function(t){var e=t.items;return s("viewedObjectIDs",[t.payload],e)}))},viewedFilters:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];n.length>0&&t.apply(void 0,["viewedFilters"].concat(n))}}}(u),h={current:[]},f=_g((function(t){var e=t.state;if(e.isOpen){var n=e.collections.reduce((function(t,e){return[].concat(Ti(t),Ti(e.items))}),[]).filter(lo);bg(h.current.map((function(t){return t.objectID})),n.map((function(t){return t.objectID})))||(h.current=n,n.length>0&&O1({onItemsChange:s,items:n,insights:d,state:e}))}}),0);return{name:"aa.algoliaInsightsPlugin",subscribe:function(t){var e=t.setContext,n=t.onSelect,r=t.onActive;function i(t){e({algoliaInsightsPlugin:{__algoliaSearchParameters:Fn(Fn({},c?{clickAnalytics:!0}:{}),t?{userToken:k1(t)}:{}),insights:d}})}u("addAlgoliaAgent","insights-plugin"),i(),u("onUserTokenChange",(function(t){i(t)})),u("getUserToken",null,(function(t,e){i(e)})),n((function(t){var e=t.item,n=t.state,r=t.event,i=t.source;lo(e)&&a({state:n,event:r,insights:d,item:e,insightsEvents:[Fn({eventName:"Item Selected"},Tf({item:e,items:i.getItems().filter(lo)}))]})})),r((function(t){var e=t.item,n=t.source,r=t.state,i=t.event;lo(e)&&l({state:r,event:i,insights:d,item:e,insightsEvents:[Fn({eventName:"Item Active"},Tf({item:e,items:n.getItems().filter(lo)}))]})}))},onStateChange:function(t){var e=t.state;f({state:e})},__autocomplete_pluginOptions:t}}function Cf(){var t,e=arguments.length>1?arguments[1]:void 0;return[].concat(Ti(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]),["autocomplete-internal"],Ti(null!==(t=e.algoliaInsightsPlugin)&&void 0!==t&&t.__automaticInsights?["autocomplete-automatic"]:[]))}function k1(t){return"number"==typeof t?t.toString():t}function Qs(t,e){var n=e;return{then:function(e,r){return Qs(t.then(Ts(e,n,t),Ts(r,n,t)),n)},catch:function(e){return Qs(t.catch(Ts(e,n,t)),n)},finally:function(e){return e&&n.onCancelList.push(e),Qs(t.finally(Ts(e&&function(){return n.onCancelList=[],e()},n,t)),n)},cancel:function(){n.isCanceled=!0;var t=n.onCancelList;n.onCancelList=[],t.forEach((function(t){t()}))},isCanceled:function(){return!0===n.isCanceled}}}function Ff(t){return Qs(t,{isCanceled:!1,onCancelList:[]})}function Ts(t,e,n){return t?function(n){return e.isCanceled?n:t(n)}:n}function jf(t,e,n,r){if(!n)return null;if(t<0&&(null===e||null!==r&&0===e))return n+t;var i=(null===e?-1:e)+t;return i<=-1||i>=n?null===r?null:0:i}function Lf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Rf(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Lf(Object(n),!0).forEach((function(e){D1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Lf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function D1(t,e,n){return r=function(t){if("object"!==Mo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Mo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Mo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Mo(t){return(Mo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function ei(t){var e,n,r=(n=(e=t).collections.map((function(t){return t.items.length})).reduce((function(t,e,n){var r=(t[n-1]||0)+e;return t.push(r),t}),[]).reduce((function(t,n){return n<=e.activeItemId?t+1:t}),0),e.collections[n]);if(!r)return null;var i=r.items[function(t){for(var e=t.state,n=t.collection,r=!1,i=0,o=0;!1===r;){var s=e.collections[i];if(s===n){r=!0;break}o+=s.items.length,i++}return e.activeItemId-o}({state:t,collection:r})],o=r.source;return{item:i,itemInputValue:o.getItemInputValue({item:i,state:t}),itemUrl:o.getItemUrl({item:i,state:t}),source:o}}function yn(t,e,n){return[t,null==n?void 0:n.sourceId,e].filter(Boolean).join("-").replace(/\s/g,"")}var E1=/((gt|sm)-|galaxy nexus)|samsung[- ]|samsungbrowser/i;function Uf(t){return t.nativeEvent||t}function No(t){return(No="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Vf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function A1(t,e,n){return r=function(t){if("object"!==No(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==No(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===No(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Co(t){return(Co="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function zf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Bs(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?zf(Object(n),!0).forEach((function(e){T1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):zf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function T1(t,e,n){return r=function(t){if("object"!==Co(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Co(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Co(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Fo(t){return(Fo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function gl(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Wf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function fi(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Wf(Object(n),!0).forEach((function(e){B1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Wf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function B1(t,e,n){return r=function(t){if("object"!==Fo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Fo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Fo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function jo(t){return(jo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Hf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Ps(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Hf(Object(n),!0).forEach((function(e){wg(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Hf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function wg(t,e,n){return r=function(t){if("object"!==jo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==jo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===jo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Lo(t){return(Lo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function xf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function di(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?xf(Object(n),!0).forEach((function(e){P1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):xf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function P1(t,e,n){return r=function(t){if("object"!==Lo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Lo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Lo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Sg(t){return function(t){if(Array.isArray(t))return _l(t)}(t)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return _l(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_l(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _l(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function Ms(t){return!!t.execute}function M1(t){var e=t.reduce((function(t,e){if(!Ms(e))return t.push(e),t;var n=e.searchClient,r=e.execute,i=e.requesterId,o=e.requests,s=t.find((function(t){return Ms(e)&&Ms(t)&&t.searchClient===n&&!!i&&t.requesterId===i}));if(s){var a;(a=s.items).push.apply(a,Sg(o))}else{var l={execute:r,requesterId:i,items:o,searchClient:n};t.push(l)}return t}),[]).map((function(t){if(!Ms(t))return Promise.resolve(t);var e=t,n=e.execute,r=e.items;return n({searchClient:e.searchClient,requests:r})}));return Promise.all(e).then((function(t){return ts(t)}))}function Ro(t){return(Ro="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}var N1=["event","nextState","props","query","refresh","store"];function qf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Kr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?qf(Object(n),!0).forEach((function(e){C1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):qf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function C1(t,e,n){return r=function(t){if("object"!==Ro(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Ro(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Ro(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}var Yf,bl,Ns,co=null,Kf=(Yf=-1,bl=-1,Ns=void 0,function(t){var e=++Yf;return Promise.resolve(t).then((function(t){return Ns&&e<bl?Ns:(bl=e,Ns=t,t)}))});function Jr(t){var e=t.event,n=t.nextState,r=void 0===n?{}:n,i=t.props,o=t.query,s=t.refresh,a=t.store,l=function(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}(t,N1);co&&i.environment.clearTimeout(co);var c=l.setCollections,u=l.setIsOpen,d=l.setQuery,h=l.setActiveItemId,f=l.setStatus,p=l.setContext;if(d(o),h(i.defaultActiveItemId),!o&&!1===i.openOnFocus){var m,b=a.getState().collections.map((function(t){return Kr(Kr({},t),{},{items:[]})}));f("idle"),c(b),u(null!==(m=r.isOpen)&&void 0!==m?m:i.shouldPanelOpen({state:a.getState()}));var y=Ff(Kf(b).then((function(){return Promise.resolve()})));return a.pendingRequests.add(y)}f("loading"),co=i.environment.setTimeout((function(){f("stalled")}),i.stallThreshold);var g=Ff(Kf(i.getSources(Kr({query:o,refresh:s,state:a.getState()},l)).then((function(t){return Promise.all(t.map((function(t){return Promise.resolve(t.getItems(Kr({query:o,refresh:s,state:a.getState()},l))).then((function(e){return function(t,e,n){if(null!=(i=t)&&i.execute){var r="algolia"===t.requesterId?Object.assign.apply(Object,[{}].concat(Sg(Object.keys(n.context).map((function(t){var e;return null===(e=n.context[t])||void 0===e?void 0:e.__algoliaSearchParameters}))))):{};return di(di({},t),{},{requests:t.queries.map((function(n){return{query:"algolia"===t.requesterId?di(di({},n),{},{params:di(di({},r),n.params)}):n,sourceId:e,transformResponse:t.transformResponse}}))})}var i;return{items:t,sourceId:e}}(e,t.sourceId,a.getState())}))}))).then(M1).then((function(e){var n,r,i,o=e.some((function(t){return e=t.items,!Array.isArray(e)&&!(null==e||!e._automaticInsights);var e}));return o&&p({algoliaInsightsPlugin:Kr(Kr({},(null===(n=a.getState().context)||void 0===n?void 0:n.algoliaInsightsPlugin)||{}),{},{__automaticInsights:o})}),r=e,i=a,t.map((function(t){var e,n=r.filter((function(e){return e.sourceId===t.sourceId})),o=n.map((function(t){return t.items})),s=n[0].transformResponse,a=s?s({results:e=o,hits:e.map((function(t){return t.hits})).filter(Boolean),facetHits:e.map((function(t){var e;return null===(e=t.facetHits)||void 0===e?void 0:e.map((function(t){return{label:t.value,count:t.count,_highlightResult:{label:{value:t.highlighted}}}}))})).filter(Boolean)}):o;return t.onResolve({source:t,results:o,items:a,state:i.getState()}),a.every(Boolean),'The `getItems` function from source "'.concat(t.sourceId,'" must return an array of items but returned ').concat(JSON.stringify(void 0),".\n\nDid you forget to return items?\n\nSee: https://www.algolia.com/doc/ui-libraries/autocomplete/core-concepts/sources/#param-getitems"),{source:t,items:a}}))})).then((function(t){return n=(e={collections:t,props:i,state:a.getState()}).props,r=e.state,o=e.collections.reduce((function(t,e){return Ps(Ps({},t),{},wg({},e.source.sourceId,Ps(Ps({},e.source),{},{getItems:function(){return ts(e.items)}})))}),{}),s=n.plugins.reduce((function(t,e){return e.reshape?e.reshape(t):t}),{sourcesBySourceId:o,state:r}).sourcesBySourceId,ts(n.reshape({sourcesBySourceId:s,sources:Object.values(s),state:r})).filter(Boolean).map((function(t){return{source:t,items:t.getItems()}}));var e,n,r,o,s}))})))).then((function(t){var n;f("idle"),c(t);var d=i.shouldPanelOpen({state:a.getState()});u(null!==(n=r.isOpen)&&void 0!==n?n:i.openOnFocus&&!o&&d||d);var h=ei(a.getState());if(null!==a.getState().activeItemId&&h){var p=h.item,m=h.itemInputValue,b=h.itemUrl,y=h.source;y.onActive(Kr({event:e,item:p,itemInputValue:m,itemUrl:b,refresh:s,source:y,state:a.getState()},l))}})).finally((function(){f("idle"),co&&i.environment.clearTimeout(co)}));return a.pendingRequests.add(g)}function Uo(t){return(Uo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}var F1=["event","props","refresh","store"];function Jf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Qr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Jf(Object(n),!0).forEach((function(e){j1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Jf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function j1(t,e,n){return r=function(t){if("object"!==Uo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Uo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Uo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Vo(t){return(Vo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}var L1=["props","refresh","store"],R1=["inputElement","formElement","panelElement"],U1=["inputElement"],V1=["inputElement","maxLength"],z1=["source"],W1=["item","source"];function Qf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function He(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Qf(Object(n),!0).forEach((function(e){H1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Qf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function H1(t,e,n){return r=function(t){if("object"!==Vo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Vo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Vo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function hi(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function zo(t){return(zo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Gf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function x1(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Gf(Object(n),!0).forEach((function(e){Ig(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Gf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Ig(t,e,n){return r=function(t){if("object"!==zo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==zo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===zo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function q1(t){var e,n,r,i,o=t.plugins,s=t.options,a=null===(e=((null===(n=s.__autocomplete_metadata)||void 0===n?void 0:n.userAgents)||[])[0])||void 0===e?void 0:e.segment,l=a?Ig({},a,Object.keys((null===(r=s.__autocomplete_metadata)||void 0===r?void 0:r.options)||{})):{};return{plugins:o.map((function(t){return{name:t.name,options:Object.keys(t.__autocomplete_pluginOptions||[])}})),options:x1({"autocomplete-core":Object.keys(s)},l),ua:_1.concat((null===(i=s.__autocomplete_metadata)||void 0===i?void 0:i.userAgents)||[])}}function Xf(t){var e,n=t.state;return!1===n.isOpen||null===n.activeItemId?null:(null===(e=ei(n))||void 0===e?void 0:e.itemInputValue)||null}function Wo(t){return(Wo="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function Zf(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Yt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Zf(Object(n),!0).forEach((function(e){Y1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Zf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function Y1(t,e,n){return r=function(t){if("object"!==Wo(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Wo(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Wo(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}var K1=function(t,e){switch(e.type){case"setActiveItemId":case"mousemove":return Yt(Yt({},t),{},{activeItemId:e.payload});case"setQuery":return Yt(Yt({},t),{},{query:e.payload,completion:null});case"setCollections":return Yt(Yt({},t),{},{collections:e.payload});case"setIsOpen":return Yt(Yt({},t),{},{isOpen:e.payload});case"setStatus":return Yt(Yt({},t),{},{status:e.payload});case"setContext":return Yt(Yt({},t),{},{context:Yt(Yt({},t.context),e.payload)});case"ArrowDown":var n=Yt(Yt({},t),{},{activeItemId:e.payload.hasOwnProperty("nextActiveItemId")?e.payload.nextActiveItemId:jf(1,t.activeItemId,Ql(t),e.props.defaultActiveItemId)});return Yt(Yt({},n),{},{completion:Xf({state:n})});case"ArrowUp":var r=Yt(Yt({},t),{},{activeItemId:jf(-1,t.activeItemId,Ql(t),e.props.defaultActiveItemId)});return Yt(Yt({},r),{},{completion:Xf({state:r})});case"Escape":return t.isOpen?Yt(Yt({},t),{},{activeItemId:null,isOpen:!1,completion:null}):Yt(Yt({},t),{},{activeItemId:null,query:"",status:"idle",collections:[]});case"submit":return Yt(Yt({},t),{},{activeItemId:null,isOpen:!1,status:"idle"});case"reset":return Yt(Yt({},t),{},{activeItemId:!0===e.props.openOnFocus?e.props.defaultActiveItemId:null,status:"idle",completion:null,query:""});case"focus":return Yt(Yt({},t),{},{activeItemId:e.props.defaultActiveItemId,isOpen:(e.props.openOnFocus||!!t.query)&&e.props.shouldPanelOpen({state:t})});case"blur":return e.props.debug?t:Yt(Yt({},t),{},{isOpen:!1,activeItemId:null});case"mouseleave":return Yt(Yt({},t),{},{activeItemId:e.props.defaultActiveItemId});default:return"The reducer action ".concat(JSON.stringify(e.type)," is not supported."),t}};function Ho(t){return(Ho="function"==typeof Symbol&&"symbol"==mt(Symbol.iterator)?function(t){return mt(t)}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":mt(t)})(t)}function $f(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function Gr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?$f(Object(n),!0).forEach((function(e){J1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):$f(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function J1(t,e,n){return r=function(t){if("object"!==Ho(t)||null===t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,"string");if("object"!==Ho(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(e),(e="symbol"===Ho(r)?r:String(r))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var r}function Q1(t){var e,n,r,i,o,s=[],a=(e=t,n=s,i=typeof window<"u"?window:{},o=e.plugins||[],fi(fi({debug:!1,openOnFocus:!1,enterKeyHint:void 0,ignoreCompositionEvents:!1,placeholder:"",autoFocus:!1,defaultActiveItemId:null,stallThreshold:300,insights:void 0,environment:i,shouldPanelOpen:function(t){return Ql(t.state)>0},reshape:function(t){return t.sources}},e),{},{id:null!==(r=e.id)&&void 0!==r?r:"autocomplete-".concat(g1++),plugins:o,initialState:fi({activeItemId:null,query:"",completion:null,collections:[],isOpen:!1,status:"idle",context:{}},e.initialState),onStateChange:function(t){var n;null===(n=e.onStateChange)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onStateChange)||void 0===n?void 0:n.call(e,t)}))},onSubmit:function(t){var n;null===(n=e.onSubmit)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onSubmit)||void 0===n?void 0:n.call(e,t)}))},onReset:function(t){var n;null===(n=e.onReset)||void 0===n||n.call(e,t),o.forEach((function(e){var n;return null===(n=e.onReset)||void 0===n?void 0:n.call(e,t)}))},getSources:function(t){return Promise.all([].concat((r=o.map((function(t){return t.getSources})),function(t){if(Array.isArray(t))return gl(t)}(r)||function(t){if(typeof Symbol<"u"&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||function(t,e){if(t){if("string"==typeof t)return gl(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?gl(t,e):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),[e.getSources]).filter(Boolean).map((function(e){return n=e,r=t,i=[],Promise.resolve(n(r)).then((function(t){return Promise.all(t.filter((function(t){return!!t})).map((function(t){if(t.sourceId,i.includes(t.sourceId))throw new Error("[Autocomplete] The `sourceId` ".concat(JSON.stringify(t.sourceId)," is not unique."));i.push(t.sourceId);var e={getItemInputValue:function(t){return t.state.query},getItemUrl:function(){},onSelect:function(t){(0,t.setIsOpen)(!1)},onActive:Js,onResolve:Js};Object.keys(e).forEach((function(t){e[t].__default=!0}));var n=Rf(Rf({},e),t);return Promise.resolve(n)})))}));var n,r,i}))).then((function(t){return ts(t)})).then((function(t){return t.map((function(t){return fi(fi({},t),{},{onSelect:function(e){t.onSelect(e),n.forEach((function(t){var n;return null===(n=t.onSelect)||void 0===n?void 0:n.call(t,e)}))},onActive:function(e){t.onActive(e),n.forEach((function(t){var n;return null===(n=t.onActive)||void 0===n?void 0:n.call(t,e)}))},onResolve:function(e){t.onResolve(e),n.forEach((function(t){var n;return null===(n=t.onResolve)||void 0===n?void 0:n.call(t,e)}))}})}))}));var r},navigator:fi({navigate:function(t){var e=t.itemUrl;i.location.assign(e)},navigateNewTab:function(t){var e=t.itemUrl,n=i.open(e,"_blank","noopener");null==n||n.focus()},navigateNewWindow:function(t){var e=t.itemUrl;i.open(e,"_blank","noopener")}},e.navigator)})),l=function(t,e){var n,r=e.initialState;return{getState:function(){return r},dispatch:function(n,i){var o=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Vf(Object(n),!0).forEach((function(e){A1(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Vf(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}({},r);!function(t){var e,n,r=t.prevState,i=t.state;if(a.onStateChange(Gr({prevState:r,state:i,refresh:d,navigator:a.navigator},c)),!f()&&null!==(e=i.context)&&void 0!==e&&null!==(n=e.algoliaInsightsPlugin)&&void 0!==n&&n.__automaticInsights&&!1!==a.insights){var o=Nf({__autocomplete_clickAnalytics:!1});a.plugins.push(o),h([o])}}({state:r=t(r,{type:n,props:e,payload:i}),prevState:o})},pendingRequests:(n=[],{add:function(t){return n.push(t),t.finally((function(){n=n.filter((function(e){return e!==t}))}))},cancelAll:function(){n.forEach((function(t){return t.cancel()}))},isEmpty:function(){return 0===n.length}})}}(K1,a),c=function(t){var e=t.store;return{setActiveItemId:function(t){e.dispatch("setActiveItemId",t)},setQuery:function(t){e.dispatch("setQuery",t)},setCollections:function(t){var n=0,r=t.map((function(t){return Bs(Bs({},t),{},{items:ts(t.items).map((function(t){return Bs(Bs({},t),{},{__autocomplete_id:n++})}))})}));e.dispatch("setCollections",r)},setIsOpen:function(t){e.dispatch("setIsOpen",t)},setStatus:function(t){e.dispatch("setStatus",t)},setContext:function(t){e.dispatch("setContext",t)}}}({store:l}),u=function(t){var e=t.props,n=t.refresh,r=t.store,i=hi(t,L1);return{getEnvironmentProps:function(t){var n=t.inputElement,i=t.formElement,o=t.panelElement;function s(t){!r.getState().isOpen&&r.pendingRequests.isEmpty()||t.target===n||!1===[i,o].some((function(e){return(n=e)===(r=t.target)||n.contains(r);var n,r}))&&(r.dispatch("blur",null),e.debug||r.pendingRequests.cancelAll())}return He({onTouchStart:s,onMouseDown:s,onTouchMove:function(t){!1!==r.getState().isOpen&&n===e.environment.document.activeElement&&t.target!==n&&n.blur()}},hi(t,R1))},getRootProps:function(t){return He({role:"combobox","aria-expanded":r.getState().isOpen,"aria-haspopup":"listbox","aria-controls":r.getState().isOpen?r.getState().collections.map((function(t){var n=t.source;return yn(e.id,"list",n)})).join(" "):void 0,"aria-labelledby":yn(e.id,"label")},t)},getFormProps:function(t){return t.inputElement,He({action:"",noValidate:!0,role:"search",onSubmit:function(o){var s;o.preventDefault(),e.onSubmit(He({event:o,refresh:n,state:r.getState()},i)),r.dispatch("submit",null),null===(s=t.inputElement)||void 0===s||s.blur()},onReset:function(o){var s;o.preventDefault(),e.onReset(He({event:o,refresh:n,state:r.getState()},i)),r.dispatch("reset",null),null===(s=t.inputElement)||void 0===s||s.focus()}},hi(t,U1))},getLabelProps:function(t){return He({htmlFor:yn(e.id,"input"),id:yn(e.id,"label")},t)},getInputProps:function(t){var o;function s(t){(e.openOnFocus||r.getState().query)&&Jr(He({event:t,props:e,query:r.getState().completion||r.getState().query,refresh:n,store:r},i)),r.dispatch("focus",null)}var a=t||{};a.inputElement;var l,c=a.maxLength,u=void 0===c?512:c,d=hi(a,V1),h=ei(r.getState()),f=!(!(l=(null===(o=e.environment.navigator)||void 0===o?void 0:o.userAgent)||"")||!l.match(E1)),p=e.enterKeyHint||(null!=h&&h.itemUrl&&!f?"go":"search");return He({"aria-autocomplete":"both","aria-activedescendant":r.getState().isOpen&&null!==r.getState().activeItemId?yn(e.id,"item-".concat(r.getState().activeItemId),null==h?void 0:h.source):void 0,"aria-controls":r.getState().isOpen?r.getState().collections.map((function(t){var n=t.source;return yn(e.id,"list",n)})).join(" "):void 0,"aria-labelledby":yn(e.id,"label"),value:r.getState().completion||r.getState().query,id:yn(e.id,"input"),autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",enterKeyHint:p,spellCheck:"false",autoFocus:e.autoFocus,placeholder:e.placeholder,maxLength:u,type:"search",onChange:function(t){var o=t.currentTarget.value;e.ignoreCompositionEvents&&Uf(t).isComposing?i.setQuery(o):Jr(He({event:t,props:e,query:o.slice(0,u),refresh:n,store:r},i))},onCompositionEnd:function(t){Jr(He({event:t,props:e,query:t.currentTarget.value.slice(0,u),refresh:n,store:r},i))},onKeyDown:function(t){Uf(t).isComposing||function(t){var e=t.event,n=t.props,r=t.refresh,i=t.store,o=function(t,e){if(null==t)return{};var n,r,i=function(t,e){if(null==t)return{};var n,r,i={},o=Object.keys(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(r=0;r<o.length;r++)n=o[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}(t,F1);if("ArrowUp"===e.key||"ArrowDown"===e.key){var s=function(){var t=ei(i.getState()),e=n.environment.document.getElementById(yn(n.id,"item-".concat(i.getState().activeItemId),null==t?void 0:t.source));e&&(e.scrollIntoViewIfNeeded?e.scrollIntoViewIfNeeded(!1):e.scrollIntoView(!1))},a=function(){var t=ei(i.getState());if(null!==i.getState().activeItemId&&t){var n=t.item,s=t.itemInputValue,a=t.itemUrl,l=t.source;l.onActive(Qr({event:e,item:n,itemInputValue:s,itemUrl:a,refresh:r,source:l,state:i.getState()},o))}};e.preventDefault(),!1===i.getState().isOpen&&(n.openOnFocus||i.getState().query)?Jr(Qr({event:e,props:n,query:i.getState().query,refresh:r,store:i},o)).then((function(){i.dispatch(e.key,{nextActiveItemId:n.defaultActiveItemId}),a(),setTimeout(s,0)})):(i.dispatch(e.key,{}),a(),s())}else if("Escape"===e.key)e.preventDefault(),i.dispatch(e.key,null),i.pendingRequests.cancelAll();else if("Tab"===e.key)i.dispatch("blur",null),i.pendingRequests.cancelAll();else if("Enter"===e.key){if(null===i.getState().activeItemId||i.getState().collections.every((function(t){return 0===t.items.length})))return void(n.debug||i.pendingRequests.cancelAll());e.preventDefault();var l=ei(i.getState()),c=l.item,u=l.itemInputValue,d=l.itemUrl,h=l.source;if(e.metaKey||e.ctrlKey)void 0!==d&&(h.onSelect(Qr({event:e,item:c,itemInputValue:u,itemUrl:d,refresh:r,source:h,state:i.getState()},o)),n.navigator.navigateNewTab({itemUrl:d,item:c,state:i.getState()}));else if(e.shiftKey)void 0!==d&&(h.onSelect(Qr({event:e,item:c,itemInputValue:u,itemUrl:d,refresh:r,source:h,state:i.getState()},o)),n.navigator.navigateNewWindow({itemUrl:d,item:c,state:i.getState()}));else if(!e.altKey){if(void 0!==d)return h.onSelect(Qr({event:e,item:c,itemInputValue:u,itemUrl:d,refresh:r,source:h,state:i.getState()},o)),void n.navigator.navigate({itemUrl:d,item:c,state:i.getState()});Jr(Qr({event:e,nextState:{isOpen:!1},props:n,query:u,refresh:r,store:i},o)).then((function(){h.onSelect(Qr({event:e,item:c,itemInputValue:u,itemUrl:d,refresh:r,source:h,state:i.getState()},o))}))}}}(He({event:t,props:e,refresh:n,store:r},i))},onFocus:s,onBlur:Js,onClick:function(n){t.inputElement!==e.environment.document.activeElement||r.getState().isOpen||s(n)}},d)},getPanelProps:function(t){return He({onMouseDown:function(t){t.preventDefault()},onMouseLeave:function(){r.dispatch("mouseleave",null)}},t)},getListProps:function(t){var n=t||{},r=n.source,i=hi(n,z1);return He({role:"listbox","aria-labelledby":yn(e.id,"label"),id:yn(e.id,"list",r)},i)},getItemProps:function(t){var o=t.item,s=t.source,a=hi(t,W1);return He({id:yn(e.id,"item-".concat(o.__autocomplete_id),s),role:"option","aria-selected":r.getState().activeItemId===o.__autocomplete_id,onMouseMove:function(t){if(o.__autocomplete_id!==r.getState().activeItemId){r.dispatch("mousemove",o.__autocomplete_id);var e=ei(r.getState());if(null!==r.getState().activeItemId&&e){var s=e.item,a=e.itemInputValue,l=e.itemUrl,c=e.source;c.onActive(He({event:t,item:s,itemInputValue:a,itemUrl:l,refresh:n,source:c,state:r.getState()},i))}}},onMouseDown:function(t){t.preventDefault()},onClick:function(t){var a=s.getItemInputValue({item:o,state:r.getState()}),l=s.getItemUrl({item:o,state:r.getState()});(l?Promise.resolve():Jr(He({event:t,nextState:{isOpen:!1},props:e,query:a,refresh:n,store:r},i))).then((function(){s.onSelect(He({event:t,item:o,itemInputValue:a,itemUrl:l,refresh:n,source:s,state:r.getState()},i))}))}},a)}}}(Gr({props:a,refresh:d,store:l,navigator:a.navigator},c));function d(){return Jr(Gr({event:new Event("input"),nextState:{isOpen:l.getState().isOpen},props:a,navigator:a.navigator,query:l.getState().query,refresh:d,store:l},c))}function h(t){t.forEach((function(t){var e;return null===(e=t.subscribe)||void 0===e?void 0:e.call(t,Gr(Gr({},c),{},{navigator:a.navigator,refresh:d,onSelect:function(t){s.push({onSelect:t})},onActive:function(t){s.push({onActive:t})},onResolve:function(t){s.push({onResolve:t})}}))}))}function f(){return a.plugins.some((function(t){return"aa.algoliaInsightsPlugin"===t.name}))}if(a.insights&&!f()){var p="boolean"==typeof a.insights?{}:a.insights;a.plugins.push(Nf(p))}return h(a.plugins),function(t){var e,n,r=t.metadata,i=t.environment;if(null!==(e=i.navigator)&&void 0!==e&&null!==(n=e.userAgent)&&void 0!==n&&n.includes("Algolia Crawler")){var o=i.document.createElement("meta"),s=i.document.querySelector("head");o.name="algolia:metadata",setTimeout((function(){o.content=JSON.stringify(r),s.appendChild(o)}),0)}}({metadata:q1({plugins:a.plugins,options:t}),environment:a.environment}),Gr(Gr({refresh:d,navigator:a.navigator},u),c)}function G1(t){var e=t.translations,n=(void 0===e?{}:e).searchByText,r=void 0===n?"Search by":n;return k.createElement("a",{href:"https://www.algolia.com/ref/docsearch/?utm_source=".concat(window.location.hostname,"&utm_medium=referral&utm_content=powered_by&utm_campaign=docsearch"),target:"_blank",rel:"noopener noreferrer"},k.createElement("span",{className:"DocSearch-Label"},r),k.createElement("svg",{width:"77",height:"19","aria-label":"Algolia",role:"img",id:"Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 2196.2 500"},k.createElement("defs",null,k.createElement("style",null,".cls-1,.cls-2{fill:#003dff;}.cls-2{fill-rule:evenodd;}")),k.createElement("path",{className:"cls-2",d:"M1070.38,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),k.createElement("rect",{className:"cls-1",x:"1845.88",y:"104.73",width:"62.58",height:"277.9",rx:"5.9",ry:"5.9"}),k.createElement("path",{className:"cls-2",d:"M1851.78,71.38h50.77c3.26,0,5.9-2.64,5.9-5.9V5.9c0-3.62-3.24-6.39-6.82-5.83l-50.77,7.95c-2.87,.45-4.99,2.92-4.99,5.83v51.62c0,3.26,2.64,5.9,5.9,5.9Z"}),k.createElement("path",{className:"cls-2",d:"M1764.03,275.3V5.91c0-3.63-3.24-6.39-6.82-5.83l-50.46,7.94c-2.87,.45-4.99,2.93-4.99,5.84l.17,273.22c0,12.92,0,92.7,95.97,95.49,3.33,.1,6.09-2.58,6.09-5.91v-40.78c0-2.96-2.19-5.51-5.12-5.84-34.85-4.01-34.85-47.57-34.85-54.72Z"}),k.createElement("path",{className:"cls-2",d:"M1631.95,142.72c-11.14-12.25-24.83-21.65-40.78-28.31-15.92-6.53-33.26-9.85-52.07-9.85-18.78,0-36.15,3.17-51.92,9.85-15.59,6.66-29.29,16.05-40.76,28.31-11.47,12.23-20.38,26.87-26.76,44.03-6.38,17.17-9.24,37.37-9.24,58.36,0,20.99,3.19,36.87,9.55,54.21,6.38,17.32,15.14,32.11,26.45,44.36,11.29,12.23,24.83,21.62,40.6,28.46,15.77,6.83,40.12,10.33,52.4,10.48,12.25,0,36.78-3.82,52.7-10.48,15.92-6.68,29.46-16.23,40.78-28.46,11.29-12.25,20.05-27.04,26.25-44.36,6.22-17.34,9.24-33.22,9.24-54.21,0-20.99-3.34-41.19-10.03-58.36-6.38-17.17-15.14-31.8-26.43-44.03Zm-44.43,163.75c-11.47,15.75-27.56,23.7-48.09,23.7-20.55,0-36.63-7.8-48.1-23.7-11.47-15.75-17.21-34.01-17.21-61.2,0-26.89,5.59-49.14,17.06-64.87,11.45-15.75,27.54-23.52,48.07-23.52,20.55,0,36.63,7.78,48.09,23.52,11.47,15.57,17.36,37.98,17.36,64.87,0,27.19-5.72,45.3-17.19,61.2Z"}),k.createElement("path",{className:"cls-2",d:"M894.42,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),k.createElement("path",{className:"cls-2",d:"M2133.97,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-14.52,22.58-22.99,49.63-22.99,78.73,0,44.89,20.13,84.92,51.59,111.1,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47,1.23,0,2.46-.03,3.68-.09,.36-.02,.71-.05,1.07-.07,.87-.05,1.75-.11,2.62-.2,.34-.03,.68-.08,1.02-.12,.91-.1,1.82-.21,2.73-.34,.21-.03,.42-.07,.63-.1,32.89-5.07,61.56-30.82,70.9-62.81v57.83c0,3.26,2.64,5.9,5.9,5.9h50.42c3.26,0,5.9-2.64,5.9-5.9V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,206.92c-12.2,10.16-27.97,13.98-44.84,15.12-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-42.24,0-77.12-35.89-77.12-79.37,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33v142.83Z"}),k.createElement("path",{className:"cls-2",d:"M1314.05,104.73h-49.33c-48.36,0-90.91,25.48-115.75,64.1-11.79,18.34-19.6,39.64-22.11,62.59-.58,5.3-.88,10.68-.88,16.14s.31,11.15,.93,16.59c4.28,38.09,23.14,71.61,50.66,94.52,2.93,2.6,6.05,4.98,9.31,7.14,12.86,8.49,28.11,13.47,44.52,13.47h0c17.99,0,34.61-5.93,48.16-15.97,16.29-11.58,28.88-28.54,34.48-47.75v50.26h-.11v11.08c0,21.84-5.71,38.27-17.34,49.36-11.61,11.08-31.04,16.63-58.25,16.63-11.12,0-28.79-.59-46.6-2.41-2.83-.29-5.46,1.5-6.27,4.22l-12.78,43.11c-1.02,3.46,1.27,7.02,4.83,7.53,21.52,3.08,42.52,4.68,54.65,4.68,48.91,0,85.16-10.75,108.89-32.21,21.48-19.41,33.15-48.89,35.2-88.52V110.63c0-3.26-2.64-5.9-5.9-5.9h-56.32Zm0,64.1s.65,139.13,0,143.36c-12.08,9.77-27.11,13.59-43.49,14.7-.16,.01-.33,.03-.49,.04-1.12,.07-2.24,.1-3.36,.1-1.32,0-2.63-.03-3.94-.1-40.41-2.11-74.52-37.26-74.52-79.38,0-10.25,1.96-20.01,5.42-28.98,11.22-29.12,38.77-49.74,71.06-49.74h49.33Z"}),k.createElement("path",{className:"cls-1",d:"M249.83,0C113.3,0,2,110.09,.03,246.16c-2,138.19,110.12,252.7,248.33,253.5,42.68,.25,83.79-10.19,120.3-30.03,3.56-1.93,4.11-6.83,1.08-9.51l-23.38-20.72c-4.75-4.21-11.51-5.4-17.36-2.92-25.48,10.84-53.17,16.38-81.71,16.03-111.68-1.37-201.91-94.29-200.13-205.96,1.76-110.26,92-199.41,202.67-199.41h202.69V407.41l-115-102.18c-3.72-3.31-9.42-2.66-12.42,1.31-18.46,24.44-48.53,39.64-81.93,37.34-46.33-3.2-83.87-40.5-87.34-86.81-4.15-55.24,39.63-101.52,94-101.52,49.18,0,89.68,37.85,93.91,85.95,.38,4.28,2.31,8.27,5.52,11.12l29.95,26.55c3.4,3.01,8.79,1.17,9.63-3.3,2.16-11.55,2.92-23.58,2.07-35.92-4.82-70.34-61.8-126.93-132.17-131.26-80.68-4.97-148.13,58.14-150.27,137.25-2.09,77.1,61.08,143.56,138.19,145.26,32.19,.71,62.03-9.41,86.14-26.95l150.26,133.2c6.44,5.71,16.61,1.14,16.61-7.47V9.48C499.66,4.25,495.42,0,490.18,0H249.83Z"})))}function Cs(t){return k.createElement("svg",{width:"15",height:"15","aria-label":t.ariaLabel,role:"img"},k.createElement("g",{fill:"none",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.2"},t.children))}function X1(t){var e=t.translations,n=void 0===e?{}:e,r=n.selectText,i=void 0===r?"to select":r,o=n.selectKeyAriaLabel,s=void 0===o?"Enter key":o,a=n.navigateText,l=void 0===a?"to navigate":a,c=n.navigateUpKeyAriaLabel,u=void 0===c?"Arrow up":c,d=n.navigateDownKeyAriaLabel,h=void 0===d?"Arrow down":d,f=n.closeText,p=void 0===f?"to close":f,m=n.closeKeyAriaLabel,b=void 0===m?"Escape key":m,y=n.searchByText,g=void 0===y?"Search by":y;return k.createElement(k.Fragment,null,k.createElement("div",{className:"DocSearch-Logo"},k.createElement(G1,{translations:{searchByText:g}})),k.createElement("ul",{className:"DocSearch-Commands"},k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Cs,{ariaLabel:s},k.createElement("path",{d:"M12 3.53088v3c0 1-1 2-2 2H4M7 11.53088l-3-3 3-3"}))),k.createElement("span",{className:"DocSearch-Label"},i)),k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Cs,{ariaLabel:h},k.createElement("path",{d:"M7.5 3.5v8M10.5 8.5l-3 3-3-3"}))),k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Cs,{ariaLabel:u},k.createElement("path",{d:"M7.5 11.5v-8M10.5 6.5l-3-3-3 3"}))),k.createElement("span",{className:"DocSearch-Label"},l)),k.createElement("li",null,k.createElement("kbd",{className:"DocSearch-Commands-Key"},k.createElement(Cs,{ariaLabel:b},k.createElement("path",{d:"M13.6167 8.936c-.1065.3583-.6883.962-1.4875.962-.7993 0-1.653-.9165-1.653-2.1258v-.5678c0-1.2548.7896-2.1016 1.653-2.1016.8634 0 1.3601.4778 1.4875 1.0724M9 6c-.1352-.4735-.7506-.9219-1.46-.8972-.7092.0246-1.344.57-1.344 1.2166s.4198.8812 1.3445.9805C8.465 7.3992 8.968 7.9337 9 8.5c.032.5663-.454 1.398-1.4595 1.398C6.6593 9.898 6 9 5.963 8.4851m-1.4748.5368c-.2635.5941-.8099.876-1.5443.876s-1.7073-.6248-1.7073-2.204v-.4603c0-1.0416.721-2.131 1.7073-2.131.9864 0 1.6425 1.031 1.5443 2.2492h-2.956"}))),k.createElement("span",{className:"DocSearch-Label"},p))))}function Z1(t){var e=t.hit,n=t.children;return k.createElement("a",{href:e.url},n)}function $1(){return k.createElement("svg",{viewBox:"0 0 38 38",stroke:"currentColor",strokeOpacity:".5"},k.createElement("g",{fill:"none",fillRule:"evenodd"},k.createElement("g",{transform:"translate(1 1)",strokeWidth:"2"},k.createElement("circle",{strokeOpacity:".3",cx:"18",cy:"18",r:"18"}),k.createElement("path",{d:"M36 18c0-9.94-8.06-18-18-18"},k.createElement("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"1s",repeatCount:"indefinite"})))))}function tw(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M3.18 6.6a8.23 8.23 0 1112.93 9.94h0a8.23 8.23 0 01-11.63 0"}),k.createElement("path",{d:"M6.44 7.25H2.55V3.36M10.45 6v5.6M10.45 11.6L13 13"})))}function Gl(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M10 10l5.09-5.09L10 10l5.09 5.09L10 10zm0 0L4.91 4.91 10 10l-5.09 5.09L10 10z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function ew(){return k.createElement("svg",{className:"DocSearch-Hit-Select-Icon",width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M18 3v4c0 2-2 4-4 4H2"}),k.createElement("path",{d:"M8 17l-6-6 6-6"})))}var nw=function(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M17 6v12c0 .52-.2 1-1 1H4c-.7 0-1-.33-1-1V2c0-.55.42-1 1-1h8l5 5zM14 8h-3.13c-.51 0-.87-.34-.87-.87V4",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))};function rw(t){switch(t.type){case"lvl1":return k.createElement(nw,null);case"content":return k.createElement(ow,null);default:return k.createElement(iw,null)}}function iw(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M13 13h4-4V8H7v5h6v4-4H7V8H3h4V3v5h6V3v5h4-4v5zm-6 0v4-4H3h4z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"}))}function ow(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M17 5H3h14zm0 5H3h14zm0 5H3h14z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function td(){return k.createElement("svg",{width:"20",height:"20",viewBox:"0 0 20 20"},k.createElement("path",{d:"M10 14.2L5 17l1-5.6-4-4 5.5-.7 2.5-5 2.5 5 5.6.8-4 4 .9 5.5z",stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinejoin:"round"}))}function sw(){return k.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M19 4.8a16 16 0 00-2-1.2m-3.3-1.2A16 16 0 001.1 4.7M16.7 8a12 12 0 00-2.8-1.4M10 6a12 12 0 00-6.7 2M12.3 14.7a4 4 0 00-4.5 0M14.5 11.4A8 8 0 0010 10M3 16L18 2M10 18h0"}))}function aw(){return k.createElement("svg",{width:"40",height:"40",viewBox:"0 0 20 20",fill:"none",fillRule:"evenodd",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"},k.createElement("path",{d:"M15.5 4.8c2 3 1.7 7-1 9.7h0l4.3 4.3-4.3-4.3a7.8 7.8 0 01-9.8 1m-2.2-2.2A7.8 7.8 0 0113.2 2.4M2 18L18 2"}))}function lw(t){var e=t.translations,n=void 0===e?{}:e,r=n.titleText,i=void 0===r?"Unable to fetch results":r,o=n.helpText,s=void 0===o?"You might want to check your network connection.":o;return k.createElement("div",{className:"DocSearch-ErrorScreen"},k.createElement("div",{className:"DocSearch-Screen-Icon"},k.createElement(sw,null)),k.createElement("p",{className:"DocSearch-Title"},i),k.createElement("p",{className:"DocSearch-Help"},s))}var cw=["translations"];function uw(t){var e=t.translations,n=void 0===e?{}:e,r=nr(t,cw),i=n.noResultsText,o=void 0===i?"No results for":i,s=n.suggestedQueryText,a=void 0===s?"Try searching for":s,l=n.reportMissingResultsText,c=void 0===l?"Believe this query should return results?":l,u=n.reportMissingResultsLinkText,d=void 0===u?"Let us know.":u,h=r.state.context.searchSuggestions;return k.createElement("div",{className:"DocSearch-NoResults"},k.createElement("div",{className:"DocSearch-Screen-Icon"},k.createElement(aw,null)),k.createElement("p",{className:"DocSearch-Title"},o,' "',k.createElement("strong",null,r.state.query),'"'),h&&h.length>0&&k.createElement("div",{className:"DocSearch-NoResults-Prefill-List"},k.createElement("p",{className:"DocSearch-Help"},a,":"),k.createElement("ul",null,h.slice(0,3).reduce((function(t,e){return[].concat(To(t),[k.createElement("li",{key:e},k.createElement("button",{className:"DocSearch-Prefill",key:e,type:"button",onClick:function(){r.setQuery(e.toLowerCase()+" "),r.refresh(),r.inputRef.current.focus()}},e))])}),[]))),r.getMissingResultsUrl&&k.createElement("p",{className:"DocSearch-Help"},"".concat(c," "),k.createElement("a",{href:r.getMissingResultsUrl({query:r.state.query}),target:"_blank",rel:"noopener noreferrer"},d)))}var fw=["hit","attribute","tagName"];function ed(t,e){return e.split(".").reduce((function(t,e){return null!=t&&t[e]?t[e]:null}),t)}function mi(t){var e=t.hit,n=t.attribute,r=t.tagName;return tr(void 0===r?"span":r,Vt(Vt({},nr(t,fw)),{},{dangerouslySetInnerHTML:{__html:ed(e,"_snippetResult.".concat(n,".value"))||ed(e,n)}}))}function Xl(t){return t.collection&&0!==t.collection.items.length?k.createElement("section",{className:"DocSearch-Hits"},k.createElement("div",{className:"DocSearch-Hit-source"},t.title),k.createElement("ul",t.getListProps(),t.collection.items.map((function(e,n){return k.createElement(dw,Qe({key:[t.title,e.objectID].join(":"),item:e,index:n},t))})))):null}function dw(t){var e=t.item,n=t.index,r=t.renderIcon,i=t.renderAction,o=t.getItemProps,s=t.onItemClick,a=t.collection,l=t.hitComponent,c=fn(k.useState(!1),2),u=c[0],d=c[1],h=fn(k.useState(!1),2),f=h[0],p=h[1],m=k.useRef(null),b=l;return k.createElement("li",Qe({className:["DocSearch-Hit",e.__docsearch_parent&&"DocSearch-Hit--Child",u&&"DocSearch-Hit--deleting",f&&"DocSearch-Hit--favoriting"].filter(Boolean).join(" "),onTransitionEnd:function(){m.current&&m.current()}},o({item:e,source:a.source,onClick:function(t){s(e,t)}})),k.createElement(b,{hit:e},k.createElement("div",{className:"DocSearch-Hit-Container"},r({item:e,index:n}),e.hierarchy[e.type]&&"lvl1"===e.type&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(mi,{className:"DocSearch-Hit-title",hit:e,attribute:"hierarchy.lvl1"}),e.content&&k.createElement(mi,{className:"DocSearch-Hit-path",hit:e,attribute:"content"})),e.hierarchy[e.type]&&("lvl2"===e.type||"lvl3"===e.type||"lvl4"===e.type||"lvl5"===e.type||"lvl6"===e.type)&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(mi,{className:"DocSearch-Hit-title",hit:e,attribute:"hierarchy.".concat(e.type)}),k.createElement(mi,{className:"DocSearch-Hit-path",hit:e,attribute:"hierarchy.lvl1"})),"content"===e.type&&k.createElement("div",{className:"DocSearch-Hit-content-wrapper"},k.createElement(mi,{className:"DocSearch-Hit-title",hit:e,attribute:"content"}),k.createElement(mi,{className:"DocSearch-Hit-path",hit:e,attribute:"hierarchy.lvl1"})),i({item:e,runDeleteTransition:function(t){d(!0),m.current=t},runFavoriteTransition:function(t){p(!0),m.current=t}}))))}function nd(t,e,n){return t.reduce((function(t,r){var i=e(r);return t.hasOwnProperty(i)||(t[i]=[]),t[i].length<(n||5)&&t[i].push(r),t}),{})}function rd(t){return t}function Fs(t){return 1===t.button||t.altKey||t.ctrlKey||t.metaKey||t.shiftKey}function hw(){}var Og=/(<mark>|<\/mark>)/g,mw=RegExp(Og.source);function kg(t){var e,n,r=t;if(!r.__docsearch_parent&&!t._highlightResult)return t.hierarchy.lvl0;var i=r.__docsearch_parent?null===(e=r.__docsearch_parent)||void 0===e||null===(e=e._highlightResult)||void 0===e||null===(e=e.hierarchy)||void 0===e?void 0:e.lvl0:null===(n=t._highlightResult)||void 0===n||null===(n=n.hierarchy)||void 0===n?void 0:n.lvl0;return i?i.value&&mw.test(i.value)?i.value.replace(Og,""):i.value:t.hierarchy.lvl0}function pw(t){return k.createElement("div",{className:"DocSearch-Dropdown-Container"},t.state.collections.map((function(e){if(0===e.items.length)return null;var n=kg(e.items[0]);return k.createElement(Xl,Qe({},t,{key:e.source.sourceId,title:n,collection:e,renderIcon:function(t){var n,r=t.item,i=t.index;return k.createElement(k.Fragment,null,r.__docsearch_parent&&k.createElement("svg",{className:"DocSearch-Hit-Tree",viewBox:"0 0 24 54"},k.createElement("g",{stroke:"currentColor",fill:"none",fillRule:"evenodd",strokeLinecap:"round",strokeLinejoin:"round"},r.__docsearch_parent!==(null===(n=e.items[i+1])||void 0===n?void 0:n.__docsearch_parent)?k.createElement("path",{d:"M8 6v21M20 27H8.3"}):k.createElement("path",{d:"M8 6v42M20 27H8.3"}))),k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(rw,{type:r.type})))},renderAction:function(){return k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement(ew,null))}}))})),t.resultsFooterComponent&&k.createElement("section",{className:"DocSearch-HitsFooter"},k.createElement(t.resultsFooterComponent,{state:t.state})))}var gw=["translations"];function _w(t){var e=t.translations,n=void 0===e?{}:e,r=nr(t,gw),i=n.recentSearchesTitle,o=void 0===i?"Recent":i,s=n.noRecentSearchesText,a=void 0===s?"No recent searches":s,l=n.saveRecentSearchButtonTitle,c=void 0===l?"Save this search":l,u=n.removeRecentSearchButtonTitle,d=void 0===u?"Remove this search from history":u,h=n.favoriteSearchesTitle,f=void 0===h?"Favorite":h,p=n.removeFavoriteSearchButtonTitle,m=void 0===p?"Remove this search from favorites":p;return"idle"===r.state.status&&!1===r.hasCollections?r.disableUserPersonalization?null:k.createElement("div",{className:"DocSearch-StartScreen"},k.createElement("p",{className:"DocSearch-Help"},a)):!1===r.hasCollections?null:k.createElement("div",{className:"DocSearch-Dropdown-Container"},k.createElement(Xl,Qe({},r,{title:o,collection:r.state.collections[0],renderIcon:function(){return k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(tw,null))},renderAction:function(t){var e=t.item,n=t.runFavoriteTransition,i=t.runDeleteTransition;return k.createElement(k.Fragment,null,k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:c,type:"submit",onClick:function(t){t.preventDefault(),t.stopPropagation(),n((function(){r.favoriteSearches.add(e),r.recentSearches.remove(e),r.refresh()}))}},k.createElement(td,null))),k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:d,type:"submit",onClick:function(t){t.preventDefault(),t.stopPropagation(),i((function(){r.recentSearches.remove(e),r.refresh()}))}},k.createElement(Gl,null))))}})),k.createElement(Xl,Qe({},r,{title:f,collection:r.state.collections[1],renderIcon:function(){return k.createElement("div",{className:"DocSearch-Hit-icon"},k.createElement(td,null))},renderAction:function(t){var e=t.item,n=t.runDeleteTransition;return k.createElement("div",{className:"DocSearch-Hit-action"},k.createElement("button",{className:"DocSearch-Hit-action-button",title:m,type:"submit",onClick:function(t){t.preventDefault(),t.stopPropagation(),n((function(){r.favoriteSearches.remove(e),r.refresh()}))}},k.createElement(Gl,null)))}})))}var bw=["translations"],yw=k.memo((function(t){var e=t.translations,n=void 0===e?{}:e,r=nr(t,bw);if("error"===r.state.status)return k.createElement(lw,{translations:null==n?void 0:n.errorScreen});var i=r.state.collections.some((function(t){return t.items.length>0}));return r.state.query?!1===i?k.createElement(uw,Qe({},r,{translations:null==n?void 0:n.noResultsScreen})):k.createElement(pw,r):k.createElement(_w,Qe({},r,{hasCollections:i,translations:null==n?void 0:n.startScreen}))}),(function(t,e){return"loading"===e.state.status||"stalled"===e.state.status})),vw=["translations"];function ww(t){var e=t.translations,n=void 0===e?{}:e,r=nr(t,vw),i=n.resetButtonTitle,o=void 0===i?"Clear the query":i,s=n.resetButtonAriaLabel,a=void 0===s?"Clear the query":s,l=n.cancelButtonText,c=void 0===l?"Cancel":l,u=n.cancelButtonAriaLabel,d=void 0===u?"Cancel":u,h=n.searchInputLabel,f=void 0===h?"Search":h,p=r.getFormProps({inputElement:r.inputRef.current}).onReset;return k.useEffect((function(){r.autoFocus&&r.inputRef.current&&r.inputRef.current.focus()}),[r.autoFocus,r.inputRef]),k.useEffect((function(){r.isFromSelection&&r.inputRef.current&&r.inputRef.current.select()}),[r.isFromSelection,r.inputRef]),k.createElement(k.Fragment,null,k.createElement("form",{className:"DocSearch-Form",onSubmit:function(t){t.preventDefault()},onReset:p},k.createElement("label",Qe({className:"DocSearch-MagnifierLabel"},r.getLabelProps()),k.createElement(gg,null),k.createElement("span",{className:"DocSearch-VisuallyHiddenForAccessibility"},f)),k.createElement("div",{className:"DocSearch-LoadingIndicator"},k.createElement($1,null)),k.createElement("input",Qe({className:"DocSearch-Input",ref:r.inputRef},r.getInputProps({inputElement:r.inputRef.current,autoFocus:r.autoFocus,maxLength:64}))),k.createElement("button",{type:"reset",title:o,className:"DocSearch-Reset","aria-label":a,hidden:!r.state.query},k.createElement(Gl,null))),k.createElement("button",{className:"DocSearch-Cancel",type:"reset","aria-label":d,onClick:r.onClose},c))}var Sw=["_highlightResult","_snippetResult"];function id(t){var e,n=t.key,r=t.limit,i=void 0===r?5:r,o=(e=n,!1===function(){var t="__TEST_KEY__";try{return localStorage.setItem(t,""),localStorage.removeItem(t),!0}catch{return!1}}()?{setItem:function(){},getItem:function(){return[]}}:{setItem:function(t){return window.localStorage.setItem(e,JSON.stringify(t))},getItem:function(){var t=window.localStorage.getItem(e);return t?JSON.parse(t):[]}}),s=o.getItem().slice(0,i);return{add:function(t){var e=t;e._highlightResult,e._snippetResult;var n=nr(e,Sw),r=s.findIndex((function(t){return t.objectID===n.objectID}));r>-1&&s.splice(r,1),s.unshift(n),s=s.slice(0,i),o.setItem(s)},remove:function(t){s=s.filter((function(e){return e.objectID!==t.objectID})),o.setItem(s)},getAll:function(){return s}}}function Iw(t){var e,n="algolia-client-js-".concat(t.key);function r(){return void 0===e&&(e=t.localStorage||window.localStorage),e}function i(){return JSON.parse(r().getItem(n)||"{}")}function o(t){r().setItem(n,JSON.stringify(t))}return{get:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return Promise.resolve().then((function(){var n,r,s;return n=t.timeToLive?1e3*t.timeToLive:null,r=i(),o(s=Object.fromEntries(Object.entries(r).filter((function(t){return void 0!==fn(t,2)[1].timestamp})))),n&&o(Object.fromEntries(Object.entries(s).filter((function(t){var e=fn(t,2)[1],r=(new Date).getTime();return!(e.timestamp+n<r)})))),i()[JSON.stringify(e)]})).then((function(t){return Promise.all([t?t.value:n(),void 0!==t])})).then((function(t){var e=fn(t,2),n=e[0],i=e[1];return Promise.all([n,i||r.miss(n)])})).then((function(t){return fn(t,1)[0]}))},set:function(t,e){return Promise.resolve().then((function(){var o=i();return o[JSON.stringify(t)]={timestamp:(new Date).getTime(),value:e},r().setItem(n,JSON.stringify(o)),e}))},delete:function(t){return Promise.resolve().then((function(){var e=i();delete e[JSON.stringify(t)],r().setItem(n,JSON.stringify(e))}))},clear:function(){return Promise.resolve().then((function(){r().removeItem(n)}))}}}function Oo(t){var e=To(t.caches),n=e.shift();return void 0===n?{get:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return e().then((function(t){return Promise.all([t,n.miss(t)])})).then((function(t){return fn(t,1)[0]}))},set:function(t,e){return Promise.resolve(e)},delete:function(t){return Promise.resolve()},clear:function(){return Promise.resolve()}}:{get:function(t,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}};return n.get(t,r,i).catch((function(){return Oo({caches:e}).get(t,r,i)}))},set:function(t,r){return n.set(t,r).catch((function(){return Oo({caches:e}).set(t,r)}))},delete:function(t){return n.delete(t).catch((function(){return Oo({caches:e}).delete(t)}))},clear:function(){return n.clear().catch((function(){return Oo({caches:e}).clear()}))}}}function yl(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{serializable:!0},e={};return{get:function(n,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{miss:function(){return Promise.resolve()}},o=JSON.stringify(n);if(o in e)return Promise.resolve(t.serializable?JSON.parse(e[o]):e[o]);var s=r();return s.then((function(t){return i.miss(t)})).then((function(){return s}))},set:function(n,r){return e[JSON.stringify(n)]=t.serializable?JSON.stringify(r):r,Promise.resolve(r)},delete:function(t){return delete e[JSON.stringify(t)],Promise.resolve()},clear:function(){return e={},Promise.resolve()}}}function Ow(t){var e,n,r=t.algoliaAgents,i=t.client,o=t.version,s=(e=o,n={value:"Algolia for JavaScript (".concat(e,")"),add:function(t){var e="; ".concat(t.segment).concat(void 0!==t.version?" (".concat(t.version,")"):"");return-1===n.value.indexOf(e)&&(n.value="".concat(n.value).concat(e)),n}},n).add({segment:i,version:o});return r.forEach((function(t){return s.add(t)})),s}var od=12e4;function sd(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"up",n=Date.now();return Vt(Vt({},t),{},{status:e,lastUpdate:n,isUp:function(){return"up"===e||Date.now()-n>od},isTimedOut:function(){return"timed out"===e&&Date.now()-n<=od}})}var Dg=function(){function t(e,n){var r;return Gi(this,t),Zi(r=Qi(this,t,[e]),"name","AlgoliaError"),n&&(r.name=n),r}return $i(t,Jl(Error)),Xi(t)}(),Eg=function(){function t(e,n,r){var i;return Gi(this,t),Zi(i=Qi(this,t,[e,r]),"stackTrace",void 0),i.stackTrace=n,i}return $i(t,Dg),Xi(t)}(),kw=function(){function t(e){return Gi(this,t),Qi(this,t,["Unreachable hosts - your application id may be incorrect. If the error persists, please reach out to the Algolia Support team: https://alg.li/support.",e,"RetryError"])}return $i(t,Eg),Xi(t)}(),Zl=function(){function t(e,n,r){var i,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ApiError";return Gi(this,t),Zi(i=Qi(this,t,[e,r,o]),"status",void 0),i.status=n,i}return $i(t,Eg),Xi(t)}(),Dw=function(){function t(e,n){var r;return Gi(this,t),Zi(r=Qi(this,t,[e,"DeserializationError"]),"response",void 0),r.response=n,r}return $i(t,Dg),Xi(t)}(),Ew=function(){function t(e,n,r,i){var o;return Gi(this,t),Zi(o=Qi(this,t,[e,n,i,"DetailedApiError"]),"error",void 0),o.error=r,o}return $i(t,Zl),Xi(t)}();function Aw(t,e,n){var r,i=(r=n,Object.keys(r).filter((function(t){return void 0!==r[t]})).sort().map((function(t){return"".concat(t,"=").concat(encodeURIComponent("[object Array]"===Object.prototype.toString.call(r[t])?r[t].join(","):r[t]).replace(/\+/g,"%20"))})).join("&")),o="".concat(t.protocol,"://").concat(t.url).concat(t.port?":".concat(t.port):"","/").concat("/"===e.charAt(0)?e.substring(1):e);return i.length&&(o+="?".concat(i)),o}function Tw(t,e){if("GET"!==t.method&&(void 0!==t.data||void 0!==e.data)){var n=Array.isArray(t.data)?t.data:Vt(Vt({},t.data),e.data);return JSON.stringify(n)}}function Bw(t,e,n){var r=Vt(Vt(Vt({Accept:"application/json"},t),e),n),i={};return Object.keys(r).forEach((function(t){var e=r[t];i[t.toLowerCase()]=e})),i}function Pw(t){try{return JSON.parse(t.content)}catch(e){throw new Dw(e.message,t)}}function Mw(t,e){var n=t.content,r=t.status;try{var i=JSON.parse(n);return"error"in i?new Ew(i.message,r,i.error,e):new Zl(i.message,r,e)}catch{}return new Zl(n,r,e)}function Nw(t){return t.map((function(t){return Ag(t)}))}function Ag(t){var e=t.request.headers["x-algolia-api-key"]?{"x-algolia-api-key":"*****"}:{};return Vt(Vt({},t),{},{request:Vt(Vt({},t.request),{},{headers:Vt(Vt({},t.request.headers),e)})})}var Cw=["appId","apiKey","authMode","algoliaAgents"],Fw=["params"],ad="5.19.0";function jw(t){return[{url:"".concat(t,"-dsn.algolia.net"),accept:"read",protocol:"https"},{url:"".concat(t,".algolia.net"),accept:"write",protocol:"https"}].concat(function(t){for(var e=t,n=t.length-1;n>0;n--){var r=Math.floor(Math.random()*(n+1)),i=t[n];e[n]=t[r],e[r]=i}return e}([{url:"".concat(t,"-1.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(t,"-2.algolianet.com"),accept:"readWrite",protocol:"https"},{url:"".concat(t,"-3.algolianet.com"),accept:"readWrite",protocol:"https"}]))}var $l="3.8.3",Lw=["footer","searchBox"];function Rw(t){var e,n,r,i=t.appId,o=t.apiKey,s=t.indexName,a=t.placeholder,l=void 0===a?"Search docs":a,c=t.searchParameters,u=t.maxResultsPerGroup,d=t.onClose,h=void 0===d?hw:d,f=t.transformItems,p=void 0===f?rd:f,m=t.hitComponent,b=void 0===m?Z1:m,y=t.resultsFooterComponent,g=void 0===y?function(){return null}:y,v=t.navigator,_=t.initialScrollY,w=void 0===_?0:_,S=t.transformSearchClient,I=void 0===S?rd:S,O=t.disableUserPersonalization,$=void 0!==O&&O,x=t.initialQuery,E=void 0===x?"":x,A=t.translations,D=void 0===A?{}:A,T=t.getMissingResultsUrl,C=t.insights,L=void 0!==C&&C,M=D.footer,P=D.searchBox,B=nr(D,Lw),N=fn(k.useState({query:"",collections:[],completion:null,context:{},isOpen:!1,activeItemId:null,status:"idle"}),2),j=N[0],F=N[1],U=k.useRef(null),R=k.useRef(null),V=k.useRef(null),W=k.useRef(null),z=k.useRef(null),q=k.useRef(10),Y=k.useRef(typeof window<"u"?window.getSelection().toString().slice(0,64):"").current,H=k.useRef(E||Y).current,J=(e=i,n=o,r=I,k.useMemo((function(){var t=function(t,e){if(!t||"string"!=typeof t)throw new Error("`appId` is missing.");if(!e||"string"!=typeof e)throw new Error("`apiKey` is missing.");return n=Vt({appId:t,apiKey:e,timeouts:{connect:1e3,read:2e3,write:3e4},logger:{debug:function(t,e){return Promise.resolve()},info:function(t,e){return Promise.resolve()},error:function(t,e){return Promise.resolve()}},requester:{send:function(t){return new Promise((function(e){var n=new XMLHttpRequest;n.open(t.method,t.url,!0),Object.keys(t.headers).forEach((function(e){return n.setRequestHeader(e,t.headers[e])}));var r,i=function(t,r){return setTimeout((function(){n.abort(),e({status:0,content:r,isTimedOut:!0})}),t)},o=i(t.connectTimeout,"Connection timeout");n.onreadystatechange=function(){n.readyState>n.OPENED&&void 0===r&&(clearTimeout(o),r=i(t.responseTimeout,"Socket timeout"))},n.onerror=function(){0===n.status&&(clearTimeout(o),clearTimeout(r),e({content:n.responseText||"Network request failed",status:n.status,isTimedOut:!1}))},n.onload=function(){clearTimeout(o),clearTimeout(r),e({content:n.responseText,status:n.status,isTimedOut:!1})},n.send(t.data)}))}},algoliaAgents:[{segment:"Browser"}],authMode:"WithinQueryParameters",responsesCache:yl(),requestsCache:yl({serializable:!1}),hostsCache:Oo({caches:[Iw({key:"".concat(ad,"-").concat(t)}),yl()]})},void 0),r=n.appId,i=n.apiKey,o=n.authMode,s=n.algoliaAgents,a=nr(n,Cw),l=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"WithinHeaders",r={"x-algolia-api-key":e,"x-algolia-application-id":t};return{headers:function(){return"WithinHeaders"===n?r:{}},queryParameters:function(){return"WithinQueryParameters"===n?r:{}}}}(r,i,o),c=function(t){var e=t.hosts,n=t.hostsCache,r=t.baseHeaders,i=t.logger,o=t.baseQueryParameters,s=t.algoliaAgent,a=t.timeouts,l=t.requester,c=t.requestsCache,u=t.responsesCache;function d(t){return h.apply(this,arguments)}function h(){return(h=dl(Zr().mark((function t(e){var r,i,o,s,a;return Zr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Promise.all(e.map((function(t){return n.get(t,(function(){return Promise.resolve(sd(t))}))})));case 2:return r=t.sent,i=r.filter((function(t){return t.isUp()})),o=r.filter((function(t){return t.isTimedOut()})),s=[].concat(To(i),To(o)),a=s.length>0?s:e,t.abrupt("return",{hosts:a,getTimeout:function(t,e){return(0===o.length&&0===t?1:o.length+3+t)*e}});case 8:case"end":return t.stop()}}),t)})))).apply(this,arguments)}function f(t,e){return p.apply(this,arguments)}function p(){return p=dl(Zr().mark((function t(c,u){var h,f,p,m,b,y,g,v,_,w,S,I,O,$=arguments;return Zr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(h=!($.length>2&&void 0!==$[2])||$[2],f=[],p=Tw(c,u),m=Bw(r,c.headers,u.headers),b="GET"===c.method?Vt(Vt({},c.data),u.data):{},y=Vt(Vt(Vt({},o),c.queryParameters),b),s.value&&(y["x-algolia-agent"]=s.value),u&&u.queryParameters)for(g=0,v=Object.keys(u.queryParameters);g<v.length;g++)_=v[g],u.queryParameters[_]&&"[object Object]"!==Object.prototype.toString.call(u.queryParameters[_])?y[_]=u.queryParameters[_].toString():y[_]=u.queryParameters[_];return w=0,S=function(){var t=dl(Zr().mark((function t(e,r){var o,s,d,b,g,v;return Zr().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0!==(o=e.pop())){t.next=3;break}throw new kw(Nw(f));case 3:return s=Vt(Vt({},a),u.timeouts),d={data:p,headers:m,method:c.method,url:Aw(o,c.path,y),connectTimeout:r(w,s.connect),responseTimeout:r(w,h?s.read:s.write)},b=function(t){var n={request:d,response:t,host:o,triesLeft:e.length};return f.push(n),n},t.next=8,l.send(d);case 8:if(O=(I=g=t.sent).isTimedOut,$=I.status,!O&&((_={isTimedOut:O,status:$}).isTimedOut||~~_.status)&&(2==~~($/100)||4==~~($/100))){t.next=16;break}return v=b(g),g.isTimedOut&&w++,i.info("Retryable failure",Ag(v)),t.next=15,n.set(o,sd(o,g.isTimedOut?"timed out":"down"));case 15:return t.abrupt("return",S(e,r));case 16:if(2!=~~(g.status/100)){t.next=18;break}return t.abrupt("return",Pw(g));case 18:throw b(g),Mw(g,f);case 20:case"end":return t.stop()}var _,I,O,$}),t)})));return function(e,n){return t.apply(this,arguments)}}(),I=e.filter((function(t){return"readWrite"===t.accept||(h?"read"===t.accept:"write"===t.accept)})),t.next=13,d(I);case 13:return O=t.sent,t.abrupt("return",S(To(O.hosts).reverse(),O.getTimeout));case 15:case"end":return t.stop()}}),t)}))),p.apply(this,arguments)}return{hostsCache:n,requester:l,timeouts:a,logger:i,algoliaAgent:s,baseHeaders:r,baseQueryParameters:o,hosts:e,request:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.useReadTransporter||"GET"===t.method;if(!n)return f(t,e,n);var i=function(){return f(t,e)};if(!0!==(e.cacheable||t.cacheable))return i();var s={request:t,requestOptions:e,transporter:{queryParameters:o,headers:r}};return u.get(s,(function(){return c.get(s,(function(){return c.set(s,i()).then((function(t){return Promise.all([c.delete(s),t])}),(function(t){return Promise.all([c.delete(s),Promise.reject(t)])})).then((function(t){var e=fn(t,2);return e[0],e[1]}))}))}),{miss:function(t){return u.set(s,t)}})},requestsCache:c,responsesCache:u}}(Vt(Vt({hosts:jw(r)},a),{},{algoliaAgent:Ow({algoliaAgents:s,client:"Lite",version:ad}),baseHeaders:Vt(Vt({"content-type":"text/plain"},l.headers()),a.baseHeaders),baseQueryParameters:Vt(Vt({},l.queryParameters()),a.baseQueryParameters)})),{transporter:c,appId:r,apiKey:i,clearCache:function(){return Promise.all([c.requestsCache.clear(),c.responsesCache.clear()]).then((function(){}))},get _ua(){return c.algoliaAgent.value},addAlgoliaAgent:function(t,e){c.algoliaAgent.add({segment:t,version:e})},setClientApiKey:function(t){var e=t.apiKey;o&&"WithinHeaders"!==o?c.baseQueryParameters["x-algolia-api-key"]=e:c.baseHeaders["x-algolia-api-key"]=e},searchForHits:function(t,e){return this.search(t,e)},searchForFacets:function(t,e){return this.search(t,e)},customPost:function(t,e){var n=t.path,r=t.parameters,i=t.body;if(!n)throw new Error("Parameter `path` is required when calling `customPost`.");var o={method:"POST",path:"/{path}".replace("{path}",n),queryParameters:r||{},headers:{},data:i||{}};return c.request(o,e)},getRecommendations:function(t,e){if(t&&Array.isArray(t)&&(t={requests:t}),!t)throw new Error("Parameter `getRecommendationsParams` is required when calling `getRecommendations`.");if(!t.requests)throw new Error("Parameter `getRecommendationsParams.requests` is required when calling `getRecommendations`.");var n={method:"POST",path:"/1/indexes/*/recommendations",queryParameters:{},headers:{},data:t,useReadTransporter:!0,cacheable:!0};return c.request(n,e)},search:function(t,e){if(t&&Array.isArray(t)){var n={requests:t.map((function(t){var e=t.params,n=nr(t,Fw);return"facet"===n.type?Vt(Vt(Vt({},n),e),{},{type:"facet"}):Vt(Vt(Vt({},n),e),{},{facet:void 0,maxFacetHits:void 0,facetQuery:void 0})}))};t=n}if(!t)throw new Error("Parameter `searchMethodParams` is required when calling `search`.");if(!t.requests)throw new Error("Parameter `searchMethodParams.requests` is required when calling `search`.");var r={method:"POST",path:"/1/indexes/*/queries",queryParameters:{},headers:{},data:t,useReadTransporter:!0,cacheable:!0};return c.request(r,e)}};var n,r,i,o,s,a,l,c}(e,n);return t.addAlgoliaAgent("docsearch",$l),!1===/docsearch.js \(.*\)/.test(t.transporter.algoliaAgent.value)&&t.addAlgoliaAgent("docsearch-react",$l),r(t)}),[e,n,r])),K=k.useRef(id({key:"__DOCSEARCH_FAVORITE_SEARCHES__".concat(s),limit:10})).current,G=k.useRef(id({key:"__DOCSEARCH_RECENT_SEARCHES__".concat(s),limit:0===K.getAll().length?7:4})).current,Q=k.useCallback((function(t){if(!$){var e="content"===t.type?t.__docsearch_parent:t;e&&-1===K.getAll().findIndex((function(t){return t.objectID===e.objectID}))&&G.add(e)}}),[K,G,$]),X=k.useCallback((function(t){if(j.context.algoliaInsightsPlugin&&t.__autocomplete_id){var e=t,n={eventName:"Item Selected",index:e.__autocomplete_indexName,items:[e],positions:[t.__autocomplete_id],queryID:e.__autocomplete_queryID};j.context.algoliaInsightsPlugin.insights.clickedObjectIDsAfterSearch(n)}}),[j.context.algoliaInsightsPlugin]),Z=k.useMemo((function(){return Q1({id:"docsearch",defaultActiveItemId:0,placeholder:l,openOnFocus:!0,initialState:{query:H,context:{searchSuggestions:[]}},insights:L,navigator:v,onStateChange:function(t){F(t.state)},getSources:function(t){var e=t.query,n=t.state,r=t.setContext,a=t.setStatus;if(!e)return $?[]:[{sourceId:"recentSearches",onSelect:function(t){var e=t.item,n=t.event;Q(e),Fs(n)||h()},getItemUrl:function(t){return t.item.url},getItems:function(){return G.getAll()}},{sourceId:"favoriteSearches",onSelect:function(t){var e=t.item,n=t.event;Q(e),Fs(n)||h()},getItemUrl:function(t){return t.item.url},getItems:function(){return K.getAll()}}];var l=!!L;return J.search({requests:[Vt({query:e,indexName:s,attributesToRetrieve:["hierarchy.lvl0","hierarchy.lvl1","hierarchy.lvl2","hierarchy.lvl3","hierarchy.lvl4","hierarchy.lvl5","hierarchy.lvl6","content","type","url"],attributesToSnippet:["hierarchy.lvl1:".concat(q.current),"hierarchy.lvl2:".concat(q.current),"hierarchy.lvl3:".concat(q.current),"hierarchy.lvl4:".concat(q.current),"hierarchy.lvl5:".concat(q.current),"hierarchy.lvl6:".concat(q.current),"content:".concat(q.current)],snippetEllipsisText:"…",highlightPreTag:"<mark>",highlightPostTag:"</mark>",hitsPerPage:20,clickAnalytics:l},c)]}).catch((function(t){throw"RetryError"===t.name&&a("error"),t})).then((function(t){var e=t.results[0],a=e.hits,c=e.nbHits,d=nd(a,(function(t){return kg(t)}),u);n.context.searchSuggestions.length<Object.keys(d).length&&r({searchSuggestions:Object.keys(d)}),r({nbHits:c});var f={};return l&&(f={__autocomplete_indexName:s,__autocomplete_queryID:e.queryID,__autocomplete_algoliaCredentials:{appId:i,apiKey:o}}),Object.values(d).map((function(t,e){return{sourceId:"hits".concat(e),onSelect:function(t){var e=t.item,n=t.event;Q(e),Fs(n)||h()},getItemUrl:function(t){return t.item.url},getItems:function(){return Object.values(nd(t,(function(t){return t.hierarchy.lvl1}),u)).map(p).map((function(t){return t.map((function(e){var n=null,r=t.find((function(t){return"lvl1"===t.type&&t.hierarchy.lvl1===e.hierarchy.lvl1}));return"lvl1"!==e.type&&r&&(n=r),Vt(Vt({},e),{},{__docsearch_parent:n},f)}))})).flat()}}}))}))}})}),[s,c,u,J,h,G,K,Q,H,l,v,p,$,L,i,o]),tt=Z.getEnvironmentProps,et=Z.getRootProps,nt=Z.refresh;return function(t){var e=t.getEnvironmentProps,n=t.panelElement,r=t.formElement,i=t.inputElement;k.useEffect((function(){if(n&&r&&i){var t=e({panelElement:n,formElement:r,inputElement:i}),o=t.onTouchStart,s=t.onTouchMove;return window.addEventListener("touchstart",o),window.addEventListener("touchmove",s),function(){window.removeEventListener("touchstart",o),window.removeEventListener("touchmove",s)}}}),[e,n,r,i])}({getEnvironmentProps:tt,panelElement:W.current,formElement:V.current,inputElement:z.current}),function(t){var e=t.container;k.useEffect((function(){if(e){var t=e.querySelectorAll("a[href]:not([disabled]), button:not([disabled]), input:not([disabled])"),n=t[0],r=t[t.length-1];return e.addEventListener("keydown",i),function(){e.removeEventListener("keydown",i)}}function i(t){"Tab"===t.key&&(t.shiftKey?document.activeElement===n&&(t.preventDefault(),r.focus()):document.activeElement===r&&(t.preventDefault(),n.focus()))}}),[e])}({container:U.current}),k.useEffect((function(){return document.body.classList.add("DocSearch--active"),function(){var t,e;document.body.classList.remove("DocSearch--active"),null===(t=(e=window).scrollTo)||void 0===t||t.call(e,0,w)}}),[]),k.useLayoutEffect((function(){var t=window.innerWidth-document.body.clientWidth;return document.body.style.marginRight="".concat(t,"px"),function(){document.body.style.marginRight="0px"}}),[]),k.useEffect((function(){window.matchMedia("(max-width: 768px)").matches&&(q.current=5)}),[]),k.useEffect((function(){W.current&&(W.current.scrollTop=0)}),[j.query]),k.useEffect((function(){H.length>0&&(nt(),z.current&&z.current.focus())}),[H,nt]),k.useEffect((function(){function t(){if(R.current){var t=.01*window.innerHeight;R.current.style.setProperty("--docsearch-vh","".concat(t,"px"))}}return t(),window.addEventListener("resize",t),function(){window.removeEventListener("resize",t)}}),[]),k.createElement("div",Qe({ref:U},et({"aria-expanded":!0}),{className:["DocSearch","DocSearch-Container","stalled"===j.status&&"DocSearch-Container--Stalled","error"===j.status&&"DocSearch-Container--Errored"].filter(Boolean).join(" "),role:"button",tabIndex:0,onMouseDown:function(t){t.target===t.currentTarget&&h()}}),k.createElement("div",{className:"DocSearch-Modal",ref:R},k.createElement("header",{className:"DocSearch-SearchBar",ref:V},k.createElement(ww,Qe({},Z,{state:j,autoFocus:0===H.length,inputRef:z,isFromSelection:!!H&&H===Y,translations:P,onClose:h}))),k.createElement("div",{className:"DocSearch-Dropdown",ref:W},k.createElement(yw,Qe({},Z,{indexName:s,state:j,hitComponent:b,resultsFooterComponent:g,disableUserPersonalization:$,recentSearches:G,favoriteSearches:K,inputRef:z,translations:B,getMissingResultsUrl:T,onItemClick:function(t,e){X(t),Q(t),Fs(e)||h()}}))),k.createElement("footer",{className:"DocSearch-Footer"},k.createElement(X1,{translations:M}))))}function Uw(t){var e,n,r,i,o,s,a,l,c=k.useRef(null),u=fn(k.useState(!1),2),d=u[0],h=u[1],f=fn(k.useState((null==t?void 0:t.initialQuery)||void 0),2),p=f[0],m=f[1],b=k.useCallback((function(){h(!0)}),[h]),y=k.useCallback((function(){h(!1),m(null==t?void 0:t.initialQuery)}),[h,t.initialQuery]);return r={isOpen:d,onOpen:b,onClose:y,onInput:k.useCallback((function(t){h(!0),m(t.key)}),[h,m]),searchButtonRef:c},i=r.isOpen,o=r.onOpen,s=r.onClose,a=r.onInput,l=r.searchButtonRef,k.useEffect((function(){function t(t){var e,n,r;if("Escape"===t.code&&i||"k"===(null===(e=t.key)||void 0===e?void 0:e.toLowerCase())&&(t.metaKey||t.ctrlKey)||(r=(n=t.target).tagName,!n.isContentEditable&&"INPUT"!==r&&"SELECT"!==r&&"TEXTAREA"!==r&&"/"===t.key&&!i))return t.preventDefault(),void(i?s():document.body.classList.contains("DocSearch--active")||o());l&&l.current===document.activeElement&&a&&/[a-zA-Z0-9]/.test(String.fromCharCode(t.keyCode))&&a(t)}return window.addEventListener("keydown",t),function(){window.removeEventListener("keydown",t)}}),[i,o,s,a,l]),k.createElement(k.Fragment,null,k.createElement(p1,{ref:c,translations:null==t||null===(e=t.translations)||void 0===e?void 0:e.button,onClick:b}),d&&dg(k.createElement(Rw,Qe({},t,{initialScrollY:window.scrollY,initialQuery:p,translations:null==t||null===(n=t.translations)||void 0===n?void 0:n.modal,onClose:y})),document.body))}function Vw(t){mg(k.createElement(Uw,Ul({},t,{transformSearchClient:function(e){return e.addAlgoliaAgent("docsearch.js",$l),t.transformSearchClient?t.transformSearchClient(e):e}})),function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window;return"string"==typeof t?e.document.querySelector(t):t}(t.container,t.environment))}function zw(t){let e;return{c(){e=Y("div"),this.h()},l(t){e=q(t,"DIV",{id:!0,class:!0}),J(e).forEach(w),this.h()},h(){C(e,"id","docsearch"),C(e,"class",t[0]+" "+t[1]+" "+t[2]+" "+t[3])},m(t,n){L(t,e,n)},p:Xt,i:Xt,o:Xt,d(t){t&&w(e)}}}function Ww(t,e,n){let{algolia:r}=e;return Cr((()=>{Vw({container:"#docsearch",appId:null==r?void 0:r.appId,apiKey:null==r?void 0:r.apiKey,indexName:null==r?void 0:r.indexName})})),t.$$set=t=>{"algolia"in t&&n(4,r=t.algolia)},["\n\t**:[&.DocSearch-Button]:bg-base-100\n\t**:[&.DocSearch-Button]:border-base-300\n\t**:[&.DocSearch-Button]:hover:bg-base-200/40\n\t**:[&.DocSearch-Button]:transition-colors\n\t**:[&.DocSearch-Button]:duration-200\n\t**:[&.DocSearch-Button]:rounded-md\n\t**:[&.DocSearch-Button]:flex\n\t**:[&.DocSearch-Button]:gap-16\n\t**:[&.DocSearch-Button]:cursor-pointer\n\t**:[&.DocSearch-Button]:py-1\n\t**:[&.DocSearch-Button]:pl-2\n\t**:[&.DocSearch-Button]:sm:pr-1\n\t**:[&.DocSearch-Button]:pr-20\n\t**:[&.DocSearch-Button]:sm:text-xs\n\t**:[&.DocSearch-Button]:border\n\t**:[&.DocSearch-Button]:font-sans\n\t**:[&.DocSearch-Button]:font-medium\n\t**:[&.DocSearch-Button]:items-center;\n\t","\n\t**:[&DocSearch-Button-Placeholder]:text-base-content-muted\n\t","\n\t**:[&.DocSearch-Search-Icon]:hidden\n\t","\n\t**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:text-base-content-muted\n\t**:[&.DocSearch-Button-Key,&.DocSearch-Control-Key-Icon]:*:text-base-content-muted\n\t",r]}class Hw extends re{constructor(t){super(),ie(this,t,Ww,zw,Zt,{algolia:4})}}function xw(t){let e;const n=t[3].default,r=ge(n,t,t[11],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||2048&i)&&_e(r,n,t,t[11],e?ye(n,t[11],i,null):be(t[11]),null)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function qw(t){let e,n;const r=[{class:jr("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",t[1]&&"pl-8",t[0])},t[2]];let i={$$slots:{default:[xw]},$$scope:{ctx:t}};for(let t=0;t<r.length;t+=1)i=te(i,r[t]);return e=new Kv({props:i}),e.$on("click",t[4]),e.$on("keydown",t[5]),e.$on("focusin",t[6]),e.$on("focusout",t[7]),e.$on("pointerdown",t[8]),e.$on("pointerleave",t[9]),e.$on("pointermove",t[10]),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,[n]){const i=7&n?rn(r,[3&n&&{class:jr("relative flex cursor-pointer select-none items-center rounded-xs px-2 py-1.5 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",t[1]&&"pl-8",t[0])},4&n&&Ma(t[2])]):{};2048&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function Yw(t,e,n){const r=["class","inset"];let i=Ue(e,r),{$$slots:o={},$$scope:s}=e,{class:a}=e,{inset:l}=e;return t.$$set=t=>{e=te(te({},e),Sr(t)),n(2,i=Ue(e,r)),"class"in t&&n(0,a=t.class),"inset"in t&&n(1,l=t.inset),"$$scope"in t&&n(11,s=t.$$scope)},[a,l,i,o,function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},function(e){ne.call(this,t,e)},s]}class Fc extends re{constructor(t){super(),ie(this,t,Yw,qw,Zt,{class:0,inset:1})}}function Kw(t){let e;const n=t[5].default,r=ge(n,t,t[7],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||128&i)&&_e(r,n,t,t[7],e?ye(n,t[7],i,null):be(t[7]),null)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function Jw(t){let e,n;const r=[{transition:t[2]},{transitionConfig:t[3]},{sideOffset:t[1]},{class:jr("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",t[0])},t[4]];let i={$$slots:{default:[Kw]},$$scope:{ctx:t}};for(let t=0;t<r.length;t+=1)i=te(i,r[t]);return e=new y0({props:i}),e.$on("keydown",t[6]),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,[n]){const i=31&n?rn(r,[4&n&&{transition:t[2]},8&n&&{transitionConfig:t[3]},2&n&&{sideOffset:t[1]},1&n&&{class:jr("z-50 min-w-[8rem] rounded-md border border-base-300 bg-popover p-1 shadow-md bg-base-100 focus:outline-none antialiased text-base-conetnt print:hidden",t[0])},16&n&&Ma(t[4])]):{};128&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function Qw(t,e,n){const r=["class","sideOffset","transition","transitionConfig"];let i=Ue(e,r),{$$slots:o={},$$scope:s}=e,{class:a}=e,{sideOffset:l=4}=e,{transition:c=u_}=e,{transitionConfig:u}=e;return t.$$set=t=>{e=te(te({},e),Sr(t)),n(4,i=Ue(e,r)),"class"in t&&n(0,a=t.class),"sideOffset"in t&&n(1,l=t.sideOffset),"transition"in t&&n(2,c=t.transition),"transitionConfig"in t&&n(3,u=t.transitionConfig),"$$scope"in t&&n(7,s=t.$$scope)},[a,l,c,u,i,o,function(e){ne.call(this,t,e)},s]}class Gw extends re{constructor(t){super(),ie(this,t,Qw,Jw,Zt,{class:0,sideOffset:1,transition:2,transitionConfig:3})}}function Xw(t){let e,n,r;const i=t[3].default,o=ge(i,t,t[2],null);let s=[{class:n=jr("ml-auto text-xs tracking-widest opacity-60",t[0])},t[1]],a={};for(let t=0;t<s.length;t+=1)a=te(a,s[t]);return{c(){e=Y("span"),o&&o.c(),this.h()},l(t){e=q(t,"SPAN",{class:!0});var n=J(e);o&&o.l(n),n.forEach(w),this.h()},h(){Ge(e,a)},m(t,n){L(t,e,n),o&&o.m(e,null),r=!0},p(t,[l]){o&&o.p&&(!r||4&l)&&_e(o,i,t,t[2],r?ye(i,t[2],l,null):be(t[2]),null),Ge(e,a=rn(s,[(!r||1&l&&n!==(n=jr("ml-auto text-xs tracking-widest opacity-60",t[0])))&&{class:n},2&l&&t[1]]))},i(t){r||(I(o,t),r=!0)},o(t){E(o,t),r=!1},d(t){t&&w(e),o&&o.d(t)}}}function Zw(t,e,n){const r=["class"];let i=Ue(e,r),{$$slots:o={},$$scope:s}=e,{class:a}=e;return t.$$set=t=>{e=te(te({},e),Sr(t)),n(1,i=Ue(e,r)),"class"in t&&n(0,a=t.class),"$$scope"in t&&n(2,s=t.$$scope)},[a,i,s,o]}class Tg extends re{constructor(t){super(),ie(this,t,Zw,Xw,Zt,{class:0})}}const $w=i0,t2=D0,e2=t0;function n2(t){let e,n;return e=new Pn({props:{src:h_,class:"h-6 w-6"}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p:Xt,i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function r2(t){let e,n;return e=new M0({props:{builders:[t[15]],variant:"ghost",size:"sm",class:"px-1","aria-label":"Menu",$$slots:{default:[n2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};32768&n&&(r.builders=[t[15]]),65536&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function i2(t){let e;return{c(){e=wt("⌘P")},l(t){e=vt(t,"⌘P")},m(t,n){L(t,e,n)},d(t){t&&w(e)}}}function o2(t){let e,n,r;return n=new Tg({props:{$$slots:{default:[i2]},$$scope:{ctx:t}}}),{c(){e=wt("Print PDF\n\t\t\t\t"),lt(n.$$.fragment)},l(t){e=vt(t,"Print PDF\n\t\t\t\t"),at(n.$$.fragment,t)},m(t,i){L(t,e,i),st(n,t,i),r=!0},p(t,e){const r={};65536&e&&(r.$$scope={dirty:e,ctx:t}),n.$set(r)},i(t){r||(I(n.$$.fragment,t),r=!0)},o(t){E(n.$$.fragment,t),r=!1},d(t){t&&w(e),ot(n,t)}}}function ld(t){let e,n;return e=new Fc({props:{$$slots:{default:[s2]},$$scope:{ctx:t}}}),e.$on("click",t[11]),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};65544&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function s2(t){let e,n,r=t[3]?"Hide ":"Show ";return{c(){e=wt(r),n=wt(" Queries")},l(t){e=vt(t,r),n=vt(t," Queries")},m(t,r){L(t,e,r),L(t,n,r)},p(t,n){8&n&&r!==(r=t[3]?"Hide ":"Show ")&&Re(e,r)},d(t){t&&(w(e),w(n))}}}function a2(t){let e,n;return e=new Fc({props:{$$slots:{default:[c2]},$$scope:{ctx:t}}}),e.$on("click",t[12]),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};65542&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function l2(t){let e,n,r,i,o;return i=new Pn({props:{src:t[1],class:"h-4 w-4 ml-1"}}),{c(){e=Y("span"),n=wt(t[2]),r=rt(),lt(i.$$.fragment),this.h()},l(o){e=q(o,"SPAN",{class:!0});var s=J(e);n=vt(s,t[2]),s.forEach(w),r=nt(o),at(i.$$.fragment,o),this.h()},h(){C(e,"class","text-xs leading-none")},m(t,s){L(t,e,s),W(e,n),L(t,r,s),st(i,t,s),o=!0},p(t,e){(!o||4&e)&&Re(n,t[2]);const r={};2&e&&(r.src=t[1]),i.$set(r)},i(t){o||(I(i.$$.fragment,t),o=!0)},o(t){E(i.$$.fragment,t),o=!1},d(t){t&&(w(e),w(r)),ot(i,t)}}}function c2(t){let e,n,r;return n=new Tg({props:{class:"tracking-normal flex flex-row items-center",$$slots:{default:[l2]},$$scope:{ctx:t}}}),{c(){e=wt("Appearance\n\t\t\t\t\t"),lt(n.$$.fragment)},l(t){e=vt(t,"Appearance\n\t\t\t\t\t"),at(n.$$.fragment,t)},m(t,i){L(t,e,i),st(n,t,i),r=!0},p(t,e){const r={};65542&e&&(r.$$scope={dirty:e,ctx:t}),n.$set(r)},i(t){r||(I(n.$$.fragment,t),r=!0)},o(t){E(n.$$.fragment,t),r=!1},d(t){t&&w(e),ot(n,t)}}}function u2(t){let e,n,r,i,o;e=new Fc({props:{$$slots:{default:[o2]},$$scope:{ctx:t}}}),e.$on("click",t[4]);let s=!t[0]&&ld(t),a=t[8].appearance.switcher&&a2(t);return{c(){lt(e.$$.fragment),n=rt(),s&&s.c(),r=rt(),a&&a.c(),i=ut()},l(t){at(e.$$.fragment,t),n=nt(t),s&&s.l(t),r=nt(t),a&&a.l(t),i=ut()},m(t,l){st(e,t,l),L(t,n,l),s&&s.m(t,l),L(t,r,l),a&&a.m(t,l),L(t,i,l),o=!0},p(t,n){const i={};65536&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i),t[0]?s&&(jt(),E(s,1,1,(()=>{s=null})),Lt()):s?(s.p(t,n),1&n&&I(s,1)):(s=ld(t),s.c(),I(s,1),s.m(r.parentNode,r)),t[8].appearance.switcher&&a.p(t,n)},i(t){o||(I(e.$$.fragment,t),I(s),I(a),o=!0)},o(t){E(e.$$.fragment,t),E(s),E(a),o=!1},d(t){t&&(w(n),w(r),w(i)),ot(e,t),s&&s.d(t),a&&a.d(t)}}}function f2(t){let e,n,r,i;e=new e2({props:{$$slots:{default:[u2]},$$scope:{ctx:t}}});let o=Ol;return{c(){lt(e.$$.fragment),n=rt(),r=ut()},l(t){at(e.$$.fragment,t),n=nt(t),r=ut()},m(t,o){st(e,t,o),L(t,n,o),L(t,r,o),i=!0},p(t,n){const r={};65551&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){i||(I(e.$$.fragment,t),I(o),i=!0)},o(t){E(e.$$.fragment,t),E(o),i=!1},d(t){t&&(w(n),w(r)),ot(e,t)}}}function d2(t){let e,n,r,i;return e=new t2({props:{asChild:!0,$$slots:{default:[r2,({builder:t})=>({15:t}),({builder:t})=>t?32768:0]},$$scope:{ctx:t}}}),r=new Gw({props:{class:"w-52 text-xs",$$slots:{default:[f2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment),n=rt(),lt(r.$$.fragment)},l(t){at(e.$$.fragment,t),n=nt(t),at(r.$$.fragment,t)},m(t,o){st(e,t,o),L(t,n,o),st(r,t,o),i=!0},p(t,n){const i={};98304&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i);const o={};65551&n&&(o.$$scope={dirty:n,ctx:t}),r.$set(o)},i(t){i||(I(e.$$.fragment,t),I(r.$$.fragment,t),i=!0)},o(t){E(e.$$.fragment,t),E(r.$$.fragment,t),i=!1},d(t){t&&w(n),ot(e,t),ot(r,t)}}}function h2(t){let e,n;return e=new $w({props:{$$slots:{default:[d2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,[n]){const r={};65551&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function m2(t,e,n){let r,i,o,s,a;Te(t,qc,(t=>n(3,a=t)));const l=new Event("export-beforeprint"),c=new Event("export-afterprint"),{selectedAppearance:u,activeAppearance:d,cycleAppearance:h,themesConfig:f}=Ah();Te(t,u,(t=>n(10,s=t))),Te(t,d,(t=>n(9,o=t)));let{neverShowQueries:p}=e;return t.$$set=t=>{"neverShowQueries"in t&&n(0,p=t.neverShowQueries)},t.$$.update=()=>{1024&t.$$.dirty&&n(2,r="system"===s?"System":"light"===s?"Light":"Dark"),512&t.$$.dirty&&n(1,i="light"===o?f_:d_)},[p,i,r,a,function(){window.dispatchEvent(l),setTimeout((()=>window.print()),0),setTimeout((()=>window.dispatchEvent(c)),0)},u,d,h,f,o,s,t=>{t.preventDefault(),qc.update((t=>!t))},t=>{t.preventDefault(),h()}]}class p2 extends re{constructor(t){super(),ie(this,t,m2,h2,Zt,{neverShowQueries:0})}}function g2(t){let e,n,r,i,o,s,a,l,c,u,d;const h=[y2,b2],f=[];function p(t,e){return t[0]?0:1}return r=p(t),i=f[r]=h[r](t),l=new Dc({props:{logo:t[2],lightLogo:t[3],darkLogo:t[4],title:t[1]}}),{c(){e=Y("div"),n=Y("button"),i.c(),s=rt(),a=Y("a"),lt(l.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var r=J(e);n=q(r,"BUTTON",{type:!0,class:!0});var o=J(n);i.l(o),o.forEach(w),s=nt(r),a=q(r,"A",{href:!0,class:!0});var c=J(a);at(l.$$.fragment,c),c.forEach(w),r.forEach(w),this.h()},h(){C(n,"type","button"),C(n,"class",o="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+("hide"===t[9]?"block":"md:hidden")),C(a,"href",Wt("/")),C(a,"class","text-sm font-bold text-base-content hidden md:block"),C(e,"class","flex gap-x-4 items-center")},m(i,o){L(i,e,o),W(e,n),f[r].m(n,null),W(e,s),W(e,a),st(l,a,null),c=!0,u||(d=Pt(n,"click",t[15]),u=!0)},p(t,e){let s=r;r=p(t),r!==s&&(jt(),E(f[s],1,1,(()=>{f[s]=null})),Lt(),i=f[r],i||(i=f[r]=h[r](t),i.c()),I(i,1),i.m(n,null)),(!c||512&e&&o!==(o="text-base-content hover:bg-base-200 rounded-lg p-1 -ml-1 transition-all duration-500 "+("hide"===t[9]?"block":"md:hidden")))&&C(n,"class",o);const a={};4&e&&(a.logo=t[2]),8&e&&(a.lightLogo=t[3]),16&e&&(a.darkLogo=t[4]),2&e&&(a.title=t[1]),l.$set(a)},i(t){c||(I(i),I(l.$$.fragment,t),c=!0)},o(t){E(i),E(l.$$.fragment,t),c=!1},d(t){t&&w(e),f[r].d(),ot(l),u=!1,d()}}}function _2(t){let e,n,r;return n=new Dc({props:{logo:t[2],lightLogo:t[3],darkLogo:t[4],title:t[1]}}),{c(){e=Y("a"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"href",Wt("/")),C(e,"class","block text-sm font-bold text-base-content")},m(t,i){L(t,e,i),st(n,e,null),r=!0},p(t,e){const r={};4&e&&(r.logo=t[2]),8&e&&(r.lightLogo=t[3]),16&e&&(r.darkLogo=t[4]),2&e&&(r.title=t[1]),n.$set(r)},i(t){r||(I(n.$$.fragment,t),r=!0)},o(t){E(n.$$.fragment,t),r=!1},d(t){t&&w(e),ot(n)}}}function b2(t){let e,n,r,i,o="Open sidebar";return r=new Pn({props:{class:"w-5 h-5",src:m_}}),{c(){e=Y("span"),e.textContent=o,n=rt(),lt(r.$$.fragment),this.h()},l(t){e=q(t,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-73kebv"!==vr(e)&&(e.textContent=o),n=nt(t),at(r.$$.fragment,t),this.h()},h(){C(e,"class","sr-only")},m(t,o){L(t,e,o),L(t,n,o),st(r,t,o),i=!0},i(t){i||(I(r.$$.fragment,t),i=!0)},o(t){E(r.$$.fragment,t),i=!1},d(t){t&&(w(e),w(n)),ot(r,t)}}}function y2(t){let e,n,r,i,o="Close sidebar";return r=new Pn({props:{class:"w-5 h-5",src:Xs}}),{c(){e=Y("span"),e.textContent=o,n=rt(),lt(r.$$.fragment),this.h()},l(t){e=q(t,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-13q18xv"!==vr(e)&&(e.textContent=o),n=nt(t),at(r.$$.fragment,t),this.h()},h(){C(e,"class","sr-only")},m(t,o){L(t,e,o),L(t,n,o),st(r,t,o),i=!0},i(t){i||(I(r.$$.fragment,t),i=!0)},o(t){E(r.$$.fragment,t),i=!1},d(t){t&&(w(e),w(n)),ot(r,t)}}}function cd(t){let e,n;return e=new Hw({props:{algolia:t[10]}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};1024&n&&(r.algolia=t[10]),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function ud(t){let e,n,r,i;return n=new Pn({props:{src:P_,class:"w-4 h-4 text-base-content"}}),{c(){e=Y("a"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0,target:!0,rel:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"href",r=Wt(t[11])),C(e,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),C(e,"target","_blank"),C(e,"rel","noreferrer")},m(t,r){L(t,e,r),st(n,e,null),i=!0},p(t,n){(!i||2048&n&&r!==(r=Wt(t[11])))&&C(e,"href",r)},i(t){i||(I(n.$$.fragment,t),i=!0)},o(t){E(n.$$.fragment,t),i=!1},d(t){t&&w(e),ot(n)}}}function fd(t){let e,n,r,i;return n=new Pn({props:{src:M_,class:"w-4 h-4 text-base-content"}}),{c(){e=Y("a"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0,target:!0,rel:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"href",r=Wt(t[12])),C(e,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),C(e,"target","_blank"),C(e,"rel","noreferrer")},m(t,r){L(t,e,r),st(n,e,null),i=!0},p(t,n){(!i||4096&n&&r!==(r=Wt(t[12])))&&C(e,"href",r)},i(t){i||(I(n.$$.fragment,t),i=!0)},o(t){E(n.$$.fragment,t),i=!1},d(t){t&&w(e),ot(n)}}}function dd(t){let e,n,r;return n=new Pn({props:{src:N_,fill:"currentColor",class:"w-4 h-4 text-base-content "}}),{c(){e=Y("a"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0,target:!0,rel:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"href",t[13]),C(e,"class","hover:bg-gray-50 rounded-lg p-2 transition-all duration-200"),C(e,"target","_blank"),C(e,"rel","noreferrer")},m(t,i){L(t,e,i),st(n,e,null),r=!0},p(t,n){(!r||8192&n)&&C(e,"href",t[13])},i(t){r||(I(n.$$.fragment,t),r=!0)},o(t){E(n.$$.fragment,t),r=!1},d(t){t&&w(e),ot(n)}}}function hd(t){let e,n,r,i;return n=new Pn({props:{src:C_,class:"w-4 h-4 text-base-content "}}),{c(){e=Y("a"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"A",{href:!0,class:!0,target:!0,rel:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"href",r=Wt(t[14])),C(e,"class","hover:bg-base-200 rounded-lg p-2 transition-all duration-200"),C(e,"target","_blank"),C(e,"rel","noreferrer")},m(t,r){L(t,e,r),st(n,e,null),i=!0},p(t,n){(!i||16384&n&&r!==(r=Wt(t[14])))&&C(e,"href",r)},i(t){i||(I(n.$$.fragment,t),i=!0)},o(t){E(n.$$.fragment,t),i=!1},d(t){t&&w(e),ot(n)}}}function v2(t){let e,n,r,i,o,s,a,l,c,u,d,h,f,p,m,b;const y=[_2,g2],g=[];function v(t,e){return t[8]||"never"===t[9]?0:1}r=v(t),i=g[r]=y[r](t);let _=t[10]&&cd(t),S=t[11]&&ud(t),O=t[12]&&fd(t),$=t[13]&&dd(t),x=t[14]&&hd(t);return p=new p2({props:{neverShowQueries:t[5]}}),{c(){e=Y("header"),n=Y("div"),i.c(),o=rt(),s=Y("div"),_&&_.c(),a=rt(),l=Y("div"),S&&S.c(),c=rt(),O&&O.c(),u=rt(),$&&$.c(),d=rt(),x&&x.c(),h=rt(),f=Y("div"),lt(p.$$.fragment),this.h()},l(t){e=q(t,"HEADER",{class:!0});var r=J(e);n=q(r,"DIV",{class:!0,style:!0});var m=J(n);i.l(m),o=nt(m),s=q(m,"DIV",{class:!0});var b=J(s);_&&_.l(b),a=nt(b),l=q(b,"DIV",{class:!0});var y=J(l);S&&S.l(y),c=nt(y),O&&O.l(y),u=nt(y),$&&$.l(y),d=nt(y),x&&x.l(y),y.forEach(w),h=nt(b),f=q(b,"DIV",{class:!0});var g=J(f);at(p.$$.fragment,g),g.forEach(w),b.forEach(w),m.forEach(w),r.forEach(w),this.h()},h(){C(l,"class","flex gap-2 items-center"),C(f,"class","relative"),C(s,"class","flex gap-2 text-sm items-center"),C(n,"class",m=(t[6]?"max-w-full ":t[7]?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between"),Gs(n,"max-width",t[7]+"px"),C(e,"class","fixed w-full top-0 z-40 flex h-12 shrink-0 justify-start items-center gap-x-4 border-b border-base-300/50 bg-base-100/90 backdrop-blur print:hidden")},m(t,i){L(t,e,i),W(e,n),g[r].m(n,null),W(n,o),W(n,s),_&&_.m(s,null),W(s,a),W(s,l),S&&S.m(l,null),W(l,c),O&&O.m(l,null),W(l,u),$&&$.m(l,null),W(l,d),x&&x.m(l,null),W(s,h),W(s,f),st(p,f,null),b=!0},p(t,[e]){let h=r;r=v(t),r===h?g[r].p(t,e):(jt(),E(g[h],1,1,(()=>{g[h]=null})),Lt(),i=g[r],i?i.p(t,e):(i=g[r]=y[r](t),i.c()),I(i,1),i.m(n,o)),t[10]?_?(_.p(t,e),1024&e&&I(_,1)):(_=cd(t),_.c(),I(_,1),_.m(s,a)):_&&(jt(),E(_,1,1,(()=>{_=null})),Lt()),t[11]?S?(S.p(t,e),2048&e&&I(S,1)):(S=ud(t),S.c(),I(S,1),S.m(l,c)):S&&(jt(),E(S,1,1,(()=>{S=null})),Lt()),t[12]?O?(O.p(t,e),4096&e&&I(O,1)):(O=fd(t),O.c(),I(O,1),O.m(l,u)):O&&(jt(),E(O,1,1,(()=>{O=null})),Lt()),t[13]?$?($.p(t,e),8192&e&&I($,1)):($=dd(t),$.c(),I($,1),$.m(l,d)):$&&(jt(),E($,1,1,(()=>{$=null})),Lt()),t[14]?x?(x.p(t,e),16384&e&&I(x,1)):(x=hd(t),x.c(),I(x,1),x.m(l,null)):x&&(jt(),E(x,1,1,(()=>{x=null})),Lt());const f={};32&e&&(f.neverShowQueries=t[5]),p.$set(f),(!b||192&e&&m!==(m=(t[6]?"max-w-full ":t[7]?"":" max-w-7xl ")+"mx-auto px-5 sm:px-6 md:px-12 flex flex-1 items-center justify-between"))&&C(n,"class",m),(!b||128&e)&&Gs(n,"max-width",t[7]+"px")},i(t){b||(I(i),I(_),I(S),I(O),I($),I(x),I(p.$$.fragment,t),b=!0)},o(t){E(i),E(_),E(S),E(O),E($),E(x),E(p.$$.fragment,t),b=!1},d(t){t&&w(e),g[r].d(),_&&_.d(),S&&S.d(),O&&O.d(),$&&$.d(),x&&x.d(),ot(p)}}}function w2(t,e,n){let{mobileSidebarOpen:r}=e,{title:i}=e,{logo:o}=e,{lightLogo:s}=e,{darkLogo:a}=e,{neverShowQueries:l}=e,{fullWidth:c}=e,{maxWidth:u}=e,{hideSidebar:d}=e,{sidebarFrontMatter:h}=e,{algolia:f}=e,{githubRepo:p}=e,{xProfile:m}=e,{blueskyProfile:b}=e,{slackCommunity:y}=e;return t.$$set=t=>{"mobileSidebarOpen"in t&&n(0,r=t.mobileSidebarOpen),"title"in t&&n(1,i=t.title),"logo"in t&&n(2,o=t.logo),"lightLogo"in t&&n(3,s=t.lightLogo),"darkLogo"in t&&n(4,a=t.darkLogo),"neverShowQueries"in t&&n(5,l=t.neverShowQueries),"fullWidth"in t&&n(6,c=t.fullWidth),"maxWidth"in t&&n(7,u=t.maxWidth),"hideSidebar"in t&&n(8,d=t.hideSidebar),"sidebarFrontMatter"in t&&n(9,h=t.sidebarFrontMatter),"algolia"in t&&n(10,f=t.algolia),"githubRepo"in t&&n(11,p=t.githubRepo),"xProfile"in t&&n(12,m=t.xProfile),"blueskyProfile"in t&&n(13,b=t.blueskyProfile),"slackCommunity"in t&&n(14,y=t.slackCommunity)},[r,i,o,s,a,l,c,u,d,h,f,p,m,b,y,()=>{n(0,r=!r)}]}class S2 extends re{constructor(t){super(),ie(this,t,w2,v2,Zt,{mobileSidebarOpen:0,title:1,logo:2,lightLogo:3,darkLogo:4,neverShowQueries:5,fullWidth:6,maxWidth:7,hideSidebar:8,sidebarFrontMatter:9,algolia:10,githubRepo:11,xProfile:12,blueskyProfile:13,slackCommunity:14})}}function I2(t){return Cr((()=>{})),[]}class O2 extends re{constructor(t){super(),ie(this,t,I2,null,Zt,{})}}function k2(t){let e,n,r='<span class="sr-only">Loading...</span> <div class="h-8 rounded-full bg-base-200 w-48 mb-8"></div> <div class="flex gap-3"><div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div> <div class="h-32 rounded-md bg-base-200 w-[22%] mb-3"></div></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[65%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[100%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[70%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[75%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[90%] mb-3"></div> <div class="h-2 rounded-full bg-base-200 max-w-[80%] mb-3"></div> <div class="h-56 rounded-md bg-base-200 max-w-[100%] mb-3"></div>';return{c(){e=Y("div"),e.innerHTML=r,this.h()},l(t){e=q(t,"DIV",{role:!0,class:!0,"data-svelte-h":!0}),"svelte-1u7962h"!==vr(e)&&(e.innerHTML=r),this.h()},h(){C(e,"role","status"),C(e,"class","animate-pulse")},m(t,n){L(t,e,n)},p:Xt,i(t){t&&(n||En((()=>{n=qi(e,xo,{}),n.start()})))},o:Xt,d(t){t&&w(e)}}}class D2 extends re{constructor(t){super(),ie(this,t,null,k2,Zt,{})}}function E2(t){let e,n;const r=t[1].default,i=ge(r,t,t[0],null);return{c(){e=Y("span"),i&&i.c(),this.h()},l(t){e=q(t,"SPAN",{class:!0});var n=J(e);i&&i.l(n),n.forEach(w),this.h()},h(){C(e,"class","rounded-sm px-0.5 py-[1px] bg-positive/10 border border-positive/20 text-positive text-base sm:text-xs")},m(t,r){L(t,e,r),i&&i.m(e,null),n=!0},p(t,[e]){i&&i.p&&(!n||1&e)&&_e(i,r,t,t[0],n?ye(r,t[0],e,null):be(t[0]),null)},i(t){n||(I(i,t),n=!0)},o(t){E(i,t),n=!1},d(t){t&&w(e),i&&i.d(t)}}}function A2(t,e,n){let{$$slots:r={},$$scope:i}=e;return t.$$set=t=>{"$$scope"in t&&n(0,i=t.$$scope)},[i,r]}class Mn extends re{constructor(t){super(),ie(this,t,A2,E2,Zt,{})}}function md(t,e,n){const r=t.slice();return r[18]=e[n],r}function pd(t,e,n){const r=t.slice();return r[21]=e[n],r}function gd(t,e,n){const r=t.slice();return r[24]=e[n],r}function _d(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[24].href.toUpperCase()+"/";return e[27]=n,e}function T2(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[21].href.toUpperCase()+"/";return e[27]=n,e}function bd(t,e,n){const r=t.slice();return r[18]=e[n],r}function yd(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[18].href.toUpperCase()+"/";return e[27]=n,e}function vd(t,e,n){const r=t.slice();return r[18]=e[n],r}function wd(t,e,n){const r=t.slice();return r[21]=e[n],r}function Sd(t,e,n){const r=t.slice();return r[24]=e[n],r}function Id(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[24].href.toUpperCase()+"/";return e[27]=n,e}function B2(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[21].href.toUpperCase()+"/";return e[27]=n,e}function Od(t,e,n){const r=t.slice();return r[18]=e[n],r}function kd(t){const e=t.slice(),n=e[8].url.pathname.toUpperCase()===e[18].href.toUpperCase()+"/";return e[27]=n,e}function Dd(t){let e,n,r,i,o,s,a,l,c,u,d,h,f,p,m,b,y,g,v,_,S,O,$,x,k,A,D="Close sidebar";l=new Dc({props:{logo:t[2],title:t[1]}}),p=new Pn({props:{src:Xs,class:"w-5 h-5"}});let T=he(t[11]),M=[];for(let e=0;e<T.length;e+=1)M[e]=Ed(Od(t,T,e));const P=t=>E(M[t],1,1,(()=>{M[t]=null}));let B=he(t[11]),N=[];for(let e=0;e<B.length;e+=1)N[e]=Pd(vd(t,B,e));const j=t=>E(N[t],1,1,(()=>{N[t]=null}));return{c(){e=Y("div"),r=rt(),i=Y("div"),o=Y("div"),s=Y("div"),a=Y("a"),lt(l.$$.fragment),c=rt(),u=Y("span"),d=Y("button"),h=Y("span"),h.textContent=D,f=rt(),lt(p.$$.fragment),m=rt(),b=Y("div"),y=Y("div"),g=Y("a"),v=wt(t[3]),_=rt();for(let t=0;t<M.length;t+=1)M[t].c();S=rt();for(let t=0;t<N.length;t+=1)N[t].c();this.h()},l(n){e=q(n,"DIV",{class:!0,role:!0,tabindex:!0}),J(e).forEach(w),r=nt(n),i=q(n,"DIV",{class:!0});var I=J(i);o=q(I,"DIV",{class:!0});var O=J(o);s=q(O,"DIV",{class:!0});var $=J(s);a=q($,"A",{href:!0,class:!0});var x=J(a);at(l.$$.fragment,x),x.forEach(w),c=nt($),u=q($,"SPAN",{role:!0,tabindex:!0});var E=J(u);d=q(E,"BUTTON",{type:!0,class:!0});var k=J(d);h=q(k,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-13q18xv"!==vr(h)&&(h.textContent=D),f=nt(k),at(p.$$.fragment,k),k.forEach(w),E.forEach(w),$.forEach(w),m=nt(O),b=q(O,"DIV",{class:!0,id:!0});var A=J(b);y=q(A,"DIV",{class:!0});var T=J(y);g=q(T,"A",{class:!0,href:!0});var C=J(g);v=vt(C,t[3]),C.forEach(w),_=nt(T);for(let t=0;t<M.length;t+=1)M[t].l(T);T.forEach(w),S=nt(A);for(let t=0;t<N.length;t+=1)N[t].l(A);A.forEach(w),O.forEach(w),I.forEach(w),this.h()},h(){C(e,"class","fixed inset-0 bg-base-100/80 z-50 backdrop-blur-sm"),C(e,"role","button"),C(e,"tabindex","-1"),C(a,"href",Wt("/")),C(a,"class","block mt-1 text-sm font-bold"),C(h,"class","sr-only"),C(d,"type","button"),C(d,"class","hover:bg-base-200 rounded-lg p-1 transition-all duration-500"),C(u,"role","button"),C(u,"tabindex","-1"),C(s,"class","py-3 px-8 mb-3 flex items-start justify-between"),C(g,"class","sticky top-0 bg-base-100 shadow shadow-base-100 text-base-heading font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100"),C(g,"href",Wt("/")),C(y,"class","flex flex-col pb-6"),C(b,"class","flex-1 px-8 sm:pb-0 pb-4 overflow-auto text-base sm:text-sm pretty-scrollbar"),C(b,"id","mobileScrollable"),C(o,"class","flex flex-col h-full pb-4"),C(i,"class","bg-base-100 border-r border-base-200 shadow-lg fixed inset-0 z-50 flex sm:w-72 h-screen w-screen flex-col overflow-hidden select-none")},m(n,w){L(n,e,w),L(n,r,w),L(n,i,w),W(i,o),W(o,s),W(s,a),st(l,a,null),W(s,c),W(s,u),W(u,d),W(d,h),W(d,f),st(p,d,null),W(o,m),W(o,b),W(b,y),W(y,g),W(g,v),W(y,_);for(let t=0;t<M.length;t+=1)M[t]&&M[t].m(y,null);W(b,S);for(let t=0;t<N.length;t+=1)N[t]&&N[t].m(b,null);x=!0,k||(A=[Pt(e,"click",t[13]),Pt(e,"keypress",t[14]),Pt(d,"click",t[15]),Pt(u,"click",t[16]),Pt(u,"keypress",t[17])],k=!0)},p(t,e){const n={};if(4&e[0]&&(n.logo=t[2]),2&e[0]&&(n.title=t[1]),l.$set(n),(!x||8&e[0])&&Re(v,t[3]),2304&e[0]){let n;for(T=he(t[11]),n=0;n<T.length;n+=1){const r=Od(t,T,n);M[n]?(M[n].p(r,e),I(M[n],1)):(M[n]=Ed(r),M[n].c(),I(M[n],1),M[n].m(y,null))}for(jt(),n=T.length;n<M.length;n+=1)P(n);Lt()}if(2432&e[0]){let n;for(B=he(t[11]),n=0;n<B.length;n+=1){const r=vd(t,B,n);N[n]?(N[n].p(r,e),I(N[n],1)):(N[n]=Pd(r),N[n].c(),I(N[n],1),N[n].m(b,null))}for(jt(),n=B.length;n<N.length;n+=1)j(n);Lt()}},i(t){if(!x){t&&En((()=>{x&&(n||(n=Dn(e,xo,{duration:100},!0)),n.run(1))})),I(l.$$.fragment,t),I(p.$$.fragment,t);for(let t=0;t<T.length;t+=1)I(M[t]);for(let t=0;t<B.length;t+=1)I(N[t]);t&&En((()=>{x&&($&&$.end(1),O=qi(i,ii,{x:-50,duration:300}),O.start())})),x=!0}},o(t){t&&(n||(n=Dn(e,xo,{duration:100},!1)),n.run(0)),E(l.$$.fragment,t),E(p.$$.fragment,t),M=M.filter(Boolean);for(let t=0;t<M.length;t+=1)E(M[t]);N=N.filter(Boolean);for(let t=0;t<N.length;t+=1)E(N[t]);O&&O.invalidate(),t&&($=is(i,ii,{x:-100,duration:200})),x=!1},d(t){t&&(w(e),w(r),w(i)),t&&n&&n.end(),ot(l),ot(p),An(M,t),An(N,t),t&&$&&$.end(),k=!1,zn(A)}}}function P2(t){var e,n;let r,i,o,s,a,l,c=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",u=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&M2(t);return{c(){r=Y("a"),i=wt(c),o=rt(),u&&u.c(),s=rt(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=J(r);i=vt(e,c),o=nt(e),u&&u.l(e),s=nt(e),e.forEach(w),this.h()},h(){C(r,"class",a="group inline-block py-1 capitalize transition-colors duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")),C(r,"href",Wt(t[18].href))},m(t,e){L(t,r,e),W(r,i),W(r,o),u&&u.m(r,null),W(r,s),l=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&u.p(t,e),(!l||256&e[0]&&a!==(a="group inline-block py-1 capitalize transition-colors duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(r,"class",a)},i(t){l||(I(u),l=!0)},o(t){E(u),l=!1},d(t){t&&w(r),u&&u.d()}}}function M2(t){let e,n;return e=new Mn({props:{$$slots:{default:[N2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function N2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function Ed(t){var e,n;let r,i,o=0===t[18].children.length&&t[18].href&&(!1!==(null==(e=t[18].frontMatter)?void 0:e.sidebar_link)||void 0===(null==(n=t[18].frontMatter)?void 0:n.sidebar_link))&&P2(kd(t));return{c(){o&&o.c(),r=ut()},l(t){o&&o.l(t),r=ut()},m(t,e){o&&o.m(t,e),L(t,r,e),i=!0},p(t,e){var n,r;0===t[18].children.length&&t[18].href&&(!1!==(null==(n=t[18].frontMatter)?void 0:n.sidebar_link)||void 0===(null==(r=t[18].frontMatter)?void 0:r.sidebar_link))&&o.p(kd(t),e)},i(t){i||(I(o),i=!0)},o(t){E(o),i=!1},d(t){t&&w(r),o&&o.d(t)}}}function C2(t){let e,n,r,i,o,s;const a=[j2,F2],l=[];var c,u,d;n=!(c=t)[18].href||!1===(null==(u=c[18].frontMatter)?void 0:u.sidebar_link)&&void 0!==(null==(d=c[18].frontMatter)?void 0:d.sidebar_link)?1:0,r=l[n]=a[n](t);let h=he(t[18].children),f=[];for(let e=0;e<h.length;e+=1)f[e]=Bd(wd(t,h,e));const p=t=>E(f[t],1,1,(()=>{f[t]=null}));return{c(){e=Y("div"),r.c(),i=rt();for(let t=0;t<f.length;t+=1)f[t].c();o=rt(),this.h()},l(t){e=q(t,"DIV",{class:!0});var n=J(e);r.l(n),i=nt(n);for(let t=0;t<f.length;t+=1)f[t].l(n);o=nt(n),n.forEach(w),this.h()},h(){C(e,"class","flex flex-col pb-6")},m(t,r){L(t,e,r),l[n].m(e,null),W(e,i);for(let t=0;t<f.length;t+=1)f[t]&&f[t].m(e,null);W(e,o),s=!0},p(t,n){if(r.p(t,n),2432&n[0]){let r;for(h=he(t[18].children),r=0;r<h.length;r+=1){const i=wd(t,h,r);f[r]?(f[r].p(i,n),I(f[r],1)):(f[r]=Bd(i),f[r].c(),I(f[r],1),f[r].m(e,o))}for(jt(),r=h.length;r<f.length;r+=1)p(r);Lt()}},i(t){if(!s){I(r);for(let t=0;t<h.length;t+=1)I(f[t]);s=!0}},o(t){E(r),f=f.filter(Boolean);for(let t=0;t<f.length;t+=1)E(f[t]);s=!1},d(t){t&&w(e),l[n].d(),An(f,t)}}}function F2(t){var e,n;let r,i,o,s,a=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",l=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&L2(t);return{c(){r=Y("span"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"SPAN",{class:!0,href:!0});var e=J(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(w),this.h()},h(){C(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"),C(r,"href",Wt(t[18].href))},m(t,e){L(t,r,e),W(r,i),W(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(I(l),s=!0)},o(t){E(l),s=!1},d(t){t&&w(r),l&&l.d()}}}function j2(t){var e,n;let r,i,o,s,a=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",l=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&U2(t);return{c(){r=Y("a"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=J(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(w),this.h()},h(){C(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize transition-colors duration-100 text-base-heading"),C(r,"href",Wt(t[18].href))},m(t,e){L(t,r,e),W(r,i),W(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(I(l),s=!0)},o(t){E(l),s=!1},d(t){t&&w(r),l&&l.d()}}}function L2(t){let e,n;return e=new Mn({props:{$$slots:{default:[R2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function R2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function U2(t){let e,n;return e=new Mn({props:{$$slots:{default:[V2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function V2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function z2(t){var e,n;let r,i,o,s,a=((null==(e=t[21].frontMatter)?void 0:e.title)??t[21].label)+"",l=(null==(n=t[21].frontMatter)?void 0:n.sidebar_badge)&&H2(t);return{c(){r=Y("span"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"SPAN",{class:!0});var e=J(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(w),this.h()},h(){C(r,"class","group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted")},m(t,e){L(t,r,e),W(r,i),W(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[21].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(I(l),s=!0)},o(t){E(l),s=!1},d(t){t&&w(r),l&&l.d()}}}function W2(t){var e,n;let r,i,o,s,a,l=((null==(e=t[21].frontMatter)?void 0:e.title)??t[21].label)+"",c=(null==(n=t[21].frontMatter)?void 0:n.sidebar_badge)&&q2(t);return{c(){r=Y("a"),i=wt(l),o=rt(),c&&c.c(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=J(r);i=vt(e,l),o=nt(e),c&&c.l(e),e.forEach(w),this.h()},h(){C(r,"class",s="group inline-block py-1 capitalize transition-colors duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")),C(r,"href",Wt(t[21].href))},m(t,e){L(t,r,e),W(r,i),W(r,o),c&&c.m(r,null),a=!0},p(t,e){var n;null!=(n=t[21].frontMatter)&&n.sidebar_badge&&c.p(t,e),(!a||256&e[0]&&s!==(s="group inline-block py-1 capitalize transition-colors duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(r,"class",s)},i(t){a||(I(c),a=!0)},o(t){E(c),a=!1},d(t){t&&w(r),c&&c.d()}}}function H2(t){let e,n;return e=new Mn({props:{$$slots:{default:[x2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function x2(t){let e,n=t[21].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function q2(t){let e,n;return e=new Mn({props:{$$slots:{default:[Y2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function Y2(t){let e,n=t[21].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function Ad(t){let e,n,r=he(t[21].children),i=[];for(let e=0;e<r.length;e+=1)i[e]=Td(Sd(t,r,e));const o=t=>E(i[t],1,1,(()=>{i[t]=null}));return{c(){for(let t=0;t<i.length;t+=1)i[t].c();e=ut()},l(t){for(let e=0;e<i.length;e+=1)i[e].l(t);e=ut()},m(t,r){for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,r);L(t,e,r),n=!0},p(t,n){if(2304&n[0]){let s;for(r=he(t[21].children),s=0;s<r.length;s+=1){const o=Sd(t,r,s);i[s]?(i[s].p(o,n),I(i[s],1)):(i[s]=Td(o),i[s].c(),I(i[s],1),i[s].m(e.parentNode,e))}for(jt(),s=r.length;s<i.length;s+=1)o(s);Lt()}},i(t){if(!n){for(let t=0;t<r.length;t+=1)I(i[t]);n=!0}},o(t){i=i.filter(Boolean);for(let t=0;t<i.length;t+=1)E(i[t]);n=!1},d(t){t&&w(e),An(i,t)}}}function K2(t){var e,n;let r,i,o,s,a,l=((null==(e=t[24].frontMatter)?void 0:e.title)??t[24].label)+"",c=(null==(n=t[24].frontMatter)?void 0:n.sidebar_badge)&&J2(t);return{c(){r=Y("a"),i=wt(l),o=rt(),c&&c.c(),this.h()},l(t){r=q(t,"A",{href:!0,class:!0});var e=J(r);i=vt(e,l),o=nt(e),c&&c.l(e),e.forEach(w),this.h()},h(){C(r,"href",Wt(t[24].href)),C(r,"class",s="group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+(t[27]?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content"))},m(t,e){L(t,r,e),W(r,i),W(r,o),c&&c.m(r,null),a=!0},p(t,e){var n;null!=(n=t[24].frontMatter)&&n.sidebar_badge&&c.p(t,e),(!a||256&e[0]&&s!==(s="group inline-block py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 capitalize transition-all duration-1000 border-l ml-[1px] "+(t[27]?"text-primary border-primary":"text-base-content-muted hover:text-base-content hover:border-base-content")))&&C(r,"class",s)},i(t){a||(I(c),a=!0)},o(t){E(c),a=!1},d(t){t&&w(r),c&&c.d()}}}function J2(t){let e,n;return e=new Mn({props:{$$slots:{default:[Q2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function Q2(t){let e,n=t[24].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function Td(t){var e,n;let r,i,o=t[24].href&&(!1!==(null==(e=t[24].frontMatter)?void 0:e.sidebar_link)||void 0===(null==(n=t[24].frontMatter)?void 0:n.sidebar_link))&&K2(Id(t));return{c(){o&&o.c(),r=ut()},l(t){o&&o.l(t),r=ut()},m(t,e){o&&o.m(t,e),L(t,r,e),i=!0},p(t,e){var n,r;t[24].href&&(!1!==(null==(n=t[24].frontMatter)?void 0:n.sidebar_link)||void 0===(null==(r=t[24].frontMatter)?void 0:r.sidebar_link))&&o.p(Id(t),e)},i(t){i||(I(o),i=!0)},o(t){E(o),i=!1},d(t){t&&w(r),o&&o.d(t)}}}function Bd(t){let e,n,r,i,o;const s=[W2,z2],a=[];function l(t,e){return 0===e?B2(t):t}var c,u,d;e=!(c=t)[21].href||!1===(null==(u=c[21].frontMatter)?void 0:u.sidebar_link)&&void 0!==(null==(d=c[21].frontMatter)?void 0:d.sidebar_link)?1:0,n=a[e]=s[e](l(t,e));let h=t[21].children.length>0&&t[7]>2&&Ad(t);return{c(){n.c(),r=rt(),h&&h.c(),i=ut()},l(t){n.l(t),r=nt(t),h&&h.l(t),i=ut()},m(t,n){a[e].m(t,n),L(t,r,n),h&&h.m(t,n),L(t,i,n),o=!0},p(t,r){n.p(l(t,e),r),t[21].children.length>0&&t[7]>2?h?(h.p(t,r),128&r[0]&&I(h,1)):(h=Ad(t),h.c(),I(h,1),h.m(i.parentNode,i)):h&&(jt(),E(h,1,1,(()=>{h=null})),Lt())},i(t){o||(I(n),I(h),o=!0)},o(t){E(n),E(h),o=!1},d(t){t&&(w(r),w(i)),a[e].d(t),h&&h.d(t)}}}function Pd(t){let e,n,r=t[18].children.length>0&&C2(t);return{c(){r&&r.c(),e=ut()},l(t){r&&r.l(t),e=ut()},m(t,i){r&&r.m(t,i),L(t,e,i),n=!0},p(t,e){t[18].children.length>0&&r.p(t,e)},i(t){n||(I(r),n=!0)},o(t){E(r),n=!1},d(t){t&&w(e),r&&r.d(t)}}}function Md(t){let e,n,r,i,o,s,a,l=he(t[11]),c=[];for(let e=0;e<l.length;e+=1)c[e]=Nd(bd(t,l,e));const u=t=>E(c[t],1,1,(()=>{c[t]=null}));let d=he(t[11]),h=[];for(let e=0;e<d.length;e+=1)h[e]=Rd(md(t,d,e));const f=t=>E(h[t],1,1,(()=>{h[t]=null}));return{c(){e=Y("div"),n=Y("div"),r=Y("a"),i=wt(t[3]),o=rt();for(let t=0;t<c.length;t+=1)c[t].c();s=rt();for(let t=0;t<h.length;t+=1)h[t].c();this.h()},l(a){e=q(a,"DIV",{class:!0});var l=J(e);n=q(l,"DIV",{class:!0});var u=J(n);r=q(u,"A",{class:!0,href:!0});var d=J(r);i=vt(d,t[3]),d.forEach(w),o=nt(u);for(let t=0;t<c.length;t+=1)c[t].l(u);u.forEach(w),s=nt(l);for(let t=0;t<h.length;t+=1)h[t].l(l);l.forEach(w),this.h()},h(){C(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize hover:underline text-base-heading"),C(r,"href",Wt("/")),C(n,"class","flex flex-col pb-6"),C(e,"class","hidden: md:block fixed w-48 top-20 bottom-8 overflow-y-auto flex-1 text-sm pretty-scrollbar"),Fr(e,"top-8",t[5])},m(t,l){L(t,e,l),W(e,n),W(n,r),W(r,i),W(n,o);for(let t=0;t<c.length;t+=1)c[t]&&c[t].m(n,null);W(e,s);for(let t=0;t<h.length;t+=1)h[t]&&h[t].m(e,null);a=!0},p(t,r){if((!a||8&r[0])&&Re(i,t[3]),2304&r[0]){let e;for(l=he(t[11]),e=0;e<l.length;e+=1){const i=bd(t,l,e);c[e]?(c[e].p(i,r),I(c[e],1)):(c[e]=Nd(i),c[e].c(),I(c[e],1),c[e].m(n,null))}for(jt(),e=l.length;e<c.length;e+=1)u(e);Lt()}if(2432&r[0]){let n;for(d=he(t[11]),n=0;n<d.length;n+=1){const i=md(t,d,n);h[n]?(h[n].p(i,r),I(h[n],1)):(h[n]=Rd(i),h[n].c(),I(h[n],1),h[n].m(e,null))}for(jt(),n=d.length;n<h.length;n+=1)f(n);Lt()}(!a||32&r[0])&&Fr(e,"top-8",t[5])},i(t){if(!a){for(let t=0;t<l.length;t+=1)I(c[t]);for(let t=0;t<d.length;t+=1)I(h[t]);a=!0}},o(t){c=c.filter(Boolean);for(let t=0;t<c.length;t+=1)E(c[t]);h=h.filter(Boolean);for(let t=0;t<h.length;t+=1)E(h[t]);a=!1},d(t){t&&w(e),An(c,t),An(h,t)}}}function G2(t){var e,n;let r,i,o,s,a,l,c=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",u=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&X2(t);return{c(){r=Y("a"),i=wt(c),o=rt(),u&&u.c(),s=rt(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=J(r);i=vt(e,c),o=nt(e),u&&u.l(e),s=nt(e),e.forEach(w),this.h()},h(){C(r,"class",a="group inline-block py-1 capitalize transition-all duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")),C(r,"href",Wt(t[18].href))},m(t,e){L(t,r,e),W(r,i),W(r,o),u&&u.m(r,null),W(r,s),l=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&u.p(t,e),(!l||256&e[0]&&a!==(a="group inline-block py-1 capitalize transition-all duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(r,"class",a)},i(t){l||(I(u),l=!0)},o(t){E(u),l=!1},d(t){t&&w(r),u&&u.d()}}}function X2(t){let e,n;return e=new Mn({props:{$$slots:{default:[Z2]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function Z2(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function Nd(t){var e,n;let r,i,o=0===t[18].children.length&&t[18].href&&(!1!==(null==(e=t[18].frontMatter)?void 0:e.sidebar_link)||void 0===(null==(n=t[18].frontMatter)?void 0:n.sidebar_link))&&G2(yd(t));return{c(){o&&o.c(),r=ut()},l(t){o&&o.l(t),r=ut()},m(t,e){o&&o.m(t,e),L(t,r,e),i=!0},p(t,e){var n,r;0===t[18].children.length&&t[18].href&&(!1!==(null==(n=t[18].frontMatter)?void 0:n.sidebar_link)||void 0===(null==(r=t[18].frontMatter)?void 0:r.sidebar_link))&&o.p(yd(t),e)},i(t){i||(I(o),i=!0)},o(t){E(o),i=!1},d(t){t&&w(r),o&&o.d(t)}}}function $2(t){let e,n,r,i,o,s;const a=[eS,tS],l=[];var c,u,d;n=!(c=t)[18].href||!1===(null==(u=c[18].frontMatter)?void 0:u.sidebar_link)&&void 0!==(null==(d=c[18].frontMatter)?void 0:d.sidebar_link)?1:0,r=l[n]=a[n](t);let h=he(t[18].children),f=[];for(let e=0;e<h.length;e+=1)f[e]=Ld(pd(t,h,e));const p=t=>E(f[t],1,1,(()=>{f[t]=null}));return{c(){e=Y("div"),r.c(),i=rt();for(let t=0;t<f.length;t+=1)f[t].c();o=rt(),this.h()},l(t){e=q(t,"DIV",{class:!0});var n=J(e);r.l(n),i=nt(n);for(let t=0;t<f.length;t+=1)f[t].l(n);o=nt(n),n.forEach(w),this.h()},h(){C(e,"class","flex flex-col pb-6")},m(t,r){L(t,e,r),l[n].m(e,null),W(e,i);for(let t=0;t<f.length;t+=1)f[t]&&f[t].m(e,null);W(e,o),s=!0},p(t,n){if(r.p(t,n),2432&n[0]){let r;for(h=he(t[18].children),r=0;r<h.length;r+=1){const i=pd(t,h,r);f[r]?(f[r].p(i,n),I(f[r],1)):(f[r]=Ld(i),f[r].c(),I(f[r],1),f[r].m(e,o))}for(jt(),r=h.length;r<f.length;r+=1)p(r);Lt()}},i(t){if(!s){I(r);for(let t=0;t<h.length;t+=1)I(f[t]);s=!0}},o(t){E(r),f=f.filter(Boolean);for(let t=0;t<f.length;t+=1)E(f[t]);s=!1},d(t){t&&w(e),l[n].d(),An(f,t)}}}function tS(t){var e,n;let r,i,o,s,a=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",l=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&nS(t);return{c(){r=Y("span"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"SPAN",{class:!0,href:!0});var e=J(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(w),this.h()},h(){C(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group inline-block capitalize text-base-heading z-10"),C(r,"href",Wt(t[18].href))},m(t,e){L(t,r,e),W(r,i),W(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(I(l),s=!0)},o(t){E(l),s=!1},d(t){t&&w(r),l&&l.d()}}}function eS(t){var e,n;let r,i,o,s,a=((null==(e=t[18].frontMatter)?void 0:e.title)??t[18].label)+"",l=(null==(n=t[18].frontMatter)?void 0:n.sidebar_badge)&&iS(t);return{c(){r=Y("a"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"A",{class:!0,href:!0});var e=J(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(w),this.h()},h(){C(r,"class","sticky top-0 bg-base-100 shadow shadow-base-100 font-semibold pb-1 mb-1 group block capitalize hover:underline text-base-heading z-10"),C(r,"href",Wt(t[18].href))},m(t,e){L(t,r,e),W(r,i),W(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[18].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(I(l),s=!0)},o(t){E(l),s=!1},d(t){t&&w(r),l&&l.d()}}}function nS(t){let e,n;return e=new Mn({props:{$$slots:{default:[rS]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function rS(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function iS(t){let e,n;return e=new Mn({props:{$$slots:{default:[oS]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function oS(t){let e,n=t[18].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function sS(t){var e,n;let r,i,o,s,a=((null==(e=t[21].frontMatter)?void 0:e.title)??t[21].label)+"",l=(null==(n=t[21].frontMatter)?void 0:n.sidebar_badge)&&lS(t);return{c(){r=Y("span"),i=wt(a),o=rt(),l&&l.c(),this.h()},l(t){r=q(t,"SPAN",{class:!0});var e=J(r);i=vt(e,a),o=nt(e),l&&l.l(e),e.forEach(w),this.h()},h(){C(r,"class","group inline-block py-1 capitalize transition-all duration-100 text-base-content-muted")},m(t,e){L(t,r,e),W(r,i),W(r,o),l&&l.m(r,null),s=!0},p(t,e){var n;null!=(n=t[21].frontMatter)&&n.sidebar_badge&&l.p(t,e)},i(t){s||(I(l),s=!0)},o(t){E(l),s=!1},d(t){t&&w(r),l&&l.d()}}}function aS(t){var e,n;let r,i,o,s,a,l=((null==(e=t[21].frontMatter)?void 0:e.title)??t[21].label)+"",c=(null==(n=t[21].frontMatter)?void 0:n.sidebar_badge)&&uS(t);return{c(){r=Y("a"),i=wt(l),o=rt(),c&&c.c(),this.h()},l(t){r=q(t,"A",{href:!0,class:!0});var e=J(r);i=vt(e,l),o=nt(e),c&&c.l(e),e.forEach(w),this.h()},h(){C(r,"href",Wt(t[21].href)),C(r,"class",s="group inline-block py-1 capitalize transition-all duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content"))},m(t,e){L(t,r,e),W(r,i),W(r,o),c&&c.m(r,null),a=!0},p(t,e){var n;null!=(n=t[21].frontMatter)&&n.sidebar_badge&&c.p(t,e),(!a||256&e[0]&&s!==(s="group inline-block py-1 capitalize transition-all duration-100 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(r,"class",s)},i(t){a||(I(c),a=!0)},o(t){E(c),a=!1},d(t){t&&w(r),c&&c.d()}}}function lS(t){let e,n;return e=new Mn({props:{$$slots:{default:[cS]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function cS(t){let e,n=t[21].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function uS(t){let e,n;return e=new Mn({props:{$$slots:{default:[fS]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function fS(t){let e,n=t[21].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function Cd(t){let e,n,r=he(t[21].children),i=[];for(let e=0;e<r.length;e+=1)i[e]=jd(gd(t,r,e));const o=t=>E(i[t],1,1,(()=>{i[t]=null}));return{c(){for(let t=0;t<i.length;t+=1)i[t].c();e=ut()},l(t){for(let e=0;e<i.length;e+=1)i[e].l(t);e=ut()},m(t,r){for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,r);L(t,e,r),n=!0},p(t,n){if(2304&n[0]){let s;for(r=he(t[21].children),s=0;s<r.length;s+=1){const o=gd(t,r,s);i[s]?(i[s].p(o,n),I(i[s],1)):(i[s]=jd(o),i[s].c(),I(i[s],1),i[s].m(e.parentNode,e))}for(jt(),s=r.length;s<i.length;s+=1)o(s);Lt()}},i(t){if(!n){for(let t=0;t<r.length;t+=1)I(i[t]);n=!0}},o(t){i=i.filter(Boolean);for(let t=0;t<i.length;t+=1)E(i[t]);n=!1},d(t){t&&w(e),An(i,t)}}}function dS(t){var e,n;let r,i,o,s,a,l,c,u=((null==(e=t[24].frontMatter)?void 0:e.title)??t[24].label)+"",d=(null==(n=t[24].frontMatter)?void 0:n.sidebar_badge)&&hS(t),h=t[27]&&Fd(t);return{c(){r=Y("div"),i=Y("a"),o=wt(u),s=rt(),d&&d.c(),l=rt(),h&&h.c(),this.h()},l(t){r=q(t,"DIV",{class:!0});var e=J(r);i=q(e,"A",{href:!0,class:!0});var n=J(i);o=vt(n,u),s=nt(n),d&&d.l(n),n.forEach(w),l=nt(e),h&&h.l(e),e.forEach(w),this.h()},h(){C(i,"href",Wt(t[24].href)),C(i,"class",a="group inline-block w-full capitalize transition-all duration-200 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")),C(r,"class","relative py-1 first:pt-0.5 first:mt-1 last:pb-0.5 last:mb-1 pl-3 border-l ml-[1px] transition-all duration-200 hover:border-base-content")},m(t,e){L(t,r,e),W(r,i),W(i,o),W(i,s),d&&d.m(i,null),W(r,l),h&&h.m(r,null),c=!0},p(t,e){var n;null!=(n=t[24].frontMatter)&&n.sidebar_badge&&d.p(t,e),(!c||256&e[0]&&a!==(a="group inline-block w-full capitalize transition-all duration-200 "+(t[27]?"text-primary":"text-base-content-muted hover:text-base-content")))&&C(i,"class",a),t[27]?h?256&e[0]&&I(h,1):(h=Fd(t),h.c(),I(h,1),h.m(r,null)):h&&(jt(),E(h,1,1,(()=>{h=null})),Lt())},i(t){c||(I(d),I(h),c=!0)},o(t){E(d),E(h),c=!1},d(t){t&&w(r),d&&d.d(),h&&h.d()}}}function hS(t){let e,n;return e=new Mn({props:{$$slots:{default:[mS]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};128&n[1]&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function mS(t){let e,n=t[24].frontMatter.sidebar_badge+"";return{c(){e=wt(n)},l(t){e=vt(t,n)},m(t,n){L(t,e,n)},p:Xt,d(t){t&&w(e)}}}function Fd(t){let e,n,r,i;return{c(){e=Y("div"),this.h()},l(t){e=q(t,"DIV",{class:!0}),J(e).forEach(w),this.h()},h(){C(e,"class","absolute top-0 -left-[1px] w-[1px] h-full bg-primary")},m(t,n){L(t,e,n),i=!0},i(o){i||(o&&En((()=>{i&&(r&&r.end(1),n=qi(e,t[9],{key:"trigger"}),n.start())})),i=!0)},o(o){n&&n.invalidate(),o&&(r=is(e,t[10],{key:"trigger"})),i=!1},d(t){t&&w(e),t&&r&&r.end()}}}function jd(t){var e,n;let r,i,o=t[24].href&&(!1!==(null==(e=t[24].frontMatter)?void 0:e.sidebar_link)||void 0===(null==(n=t[24].frontMatter)?void 0:n.sidebar_link))&&dS(_d(t));return{c(){o&&o.c(),r=ut()},l(t){o&&o.l(t),r=ut()},m(t,e){o&&o.m(t,e),L(t,r,e),i=!0},p(t,e){var n,r;t[24].href&&(!1!==(null==(n=t[24].frontMatter)?void 0:n.sidebar_link)||void 0===(null==(r=t[24].frontMatter)?void 0:r.sidebar_link))&&o.p(_d(t),e)},i(t){i||(I(o),i=!0)},o(t){E(o),i=!1},d(t){t&&w(r),o&&o.d(t)}}}function Ld(t){let e,n,r,i,o;const s=[aS,sS],a=[];function l(t,e){return 0===e?T2(t):t}var c,u,d;e=!(c=t)[21].href||!1===(null==(u=c[21].frontMatter)?void 0:u.sidebar_link)&&void 0!==(null==(d=c[21].frontMatter)?void 0:d.sidebar_link)?1:0,n=a[e]=s[e](l(t,e));let h=t[21].children.length>0&&t[7]>2&&Cd(t);return{c(){n.c(),r=rt(),h&&h.c(),i=ut()},l(t){n.l(t),r=nt(t),h&&h.l(t),i=ut()},m(t,n){a[e].m(t,n),L(t,r,n),h&&h.m(t,n),L(t,i,n),o=!0},p(t,r){n.p(l(t,e),r),t[21].children.length>0&&t[7]>2?h?(h.p(t,r),128&r[0]&&I(h,1)):(h=Cd(t),h.c(),I(h,1),h.m(i.parentNode,i)):h&&(jt(),E(h,1,1,(()=>{h=null})),Lt())},i(t){o||(I(n),I(h),o=!0)},o(t){E(n),E(h),o=!1},d(t){t&&(w(r),w(i)),a[e].d(t),h&&h.d(t)}}}function Rd(t){let e,n,r=t[18].children.length>0&&$2(t);return{c(){r&&r.c(),e=ut()},l(t){r&&r.l(t),e=ut()},m(t,i){r&&r.m(t,i),L(t,e,i),n=!0},p(t,e){t[18].children.length>0&&r.p(t,e)},i(t){n||(I(r),n=!0)},o(t){E(r),n=!1},d(t){t&&w(e),r&&r.d(t)}}}function Ud(t){let e,n='<a href="https://www.evidence.dev" class="bg-gradient-to-r inline-block antialiased font-medium">Built with Evidence</a>';return{c(){e=Y("div"),e.innerHTML=n,this.h()},l(t){e=q(t,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-fworv4"!==vr(e)&&(e.innerHTML=n),this.h()},h(){C(e,"class","fixed bottom-0 text-xs py-2")},m(t,n){L(t,e,n)},d(t){t&&w(e)}}}function pS(t){let e,n,r,i,o,s=t[0]&&Dd(t),a=!t[0]&&Md(t),l=t[4]&&Ud();return{c(){s&&s.c(),e=rt(),n=Y("aside"),a&&a.c(),r=rt(),l&&l.c(),this.h()},l(t){s&&s.l(t),e=nt(t),n=q(t,"ASIDE",{class:!0});var i=J(n);a&&a.l(i),r=nt(i),l&&l.l(i),i.forEach(w),this.h()},h(){C(n,"class",i="w-48 flex-none "+("hide"===t[6]?"hidden":"hidden md:flex"))},m(t,i){s&&s.m(t,i),L(t,e,i),L(t,n,i),a&&a.m(n,null),W(n,r),l&&l.m(n,null),o=!0},p(t,c){t[0]?s?(s.p(t,c),1&c[0]&&I(s,1)):(s=Dd(t),s.c(),I(s,1),s.m(e.parentNode,e)):s&&(jt(),E(s,1,1,(()=>{s=null})),Lt()),t[0]?a&&(jt(),E(a,1,1,(()=>{a=null})),Lt()):a?(a.p(t,c),1&c[0]&&I(a,1)):(a=Md(t),a.c(),I(a,1),a.m(n,r)),t[4]?l||(l=Ud(),l.c(),l.m(n,null)):l&&(l.d(1),l=null),(!o||64&c[0]&&i!==(i="w-48 flex-none "+("hide"===t[6]?"hidden":"hidden md:flex")))&&C(n,"class",i)},i(t){o||(I(s),I(a),o=!0)},o(t){E(s),E(a),o=!1},d(t){t&&(w(e),w(n)),s&&s.d(t),a&&a.d(),l&&l.d()}}}function Bg(t){Object.keys(t.children).forEach((function(e){var n;const r=t.children[e];Bg(r),(!r.label&&!r.href||0===r.children.length&&!1===(null==(n=r.frontMatter)?void 0:n.sidebar_link))&&delete t.children[e]}))}function Pg(t){return t.children=Object.values(t.children).sort(((t,e)=>{var n,r,i,o;return isNaN(null==(n=t.frontMatter)?void 0:n.sidebar_position)||isNaN(null==(r=e.frontMatter)?void 0:r.sidebar_position)?isNaN(null==(i=t.frontMatter)?void 0:i.sidebar_position)?isNaN(null==(o=e.frontMatter)?void 0:o.sidebar_position)?t.label.localeCompare(e.label):1:-1:t.frontMatter.sidebar_position-e.frontMatter.sidebar_position||t.label.localeCompare(e.label)})),t.children.forEach(Pg),t}function gS(t,e,n){let r;Te(t,Na,(t=>n(8,r=t)));let{fileTree:i}=e,{title:o}=e,{logo:s}=e,{homePageName:a}=e,{builtWithEvidence:l}=e,{hideHeader:c=!1}=e,{sidebarFrontMatter:u}=e,{sidebarDepth:d=3}=e;const[h,f]=p_({duration:200,easing:g_});i=structuredClone(i),Bg(i),i=Pg(i);let p=null==i?void 0:i.children,{mobileSidebarOpen:m=!1}=e;return Gg((()=>{{let t=document.querySelector("#mobileScrollable");m?b_(t):__(t)}})),t.$$set=t=>{"fileTree"in t&&n(12,i=t.fileTree),"title"in t&&n(1,o=t.title),"logo"in t&&n(2,s=t.logo),"homePageName"in t&&n(3,a=t.homePageName),"builtWithEvidence"in t&&n(4,l=t.builtWithEvidence),"hideHeader"in t&&n(5,c=t.hideHeader),"sidebarFrontMatter"in t&&n(6,u=t.sidebarFrontMatter),"sidebarDepth"in t&&n(7,d=t.sidebarDepth),"mobileSidebarOpen"in t&&n(0,m=t.mobileSidebarOpen)},[m,o,s,a,l,c,u,d,r,h,f,p,i,()=>n(0,m=!1),()=>n(0,m=!1),()=>{n(0,m=!1)},()=>n(0,m=!1),()=>n(0,m=!1)]}class _S extends re{constructor(t){super(),ie(this,t,gS,pS,Zt,{fileTree:12,title:1,logo:2,homePageName:3,builtWithEvidence:4,hideHeader:5,sidebarFrontMatter:6,sidebarDepth:7,mobileSidebarOpen:0},null,[-1,-1])}}function Vd(t,e,n){const r=t.slice();return r[5]=e[n],r}function zd(t){let e,n,r,i="On this page",o=he(t[0]),s=[];for(let e=0;e<o.length;e+=1)s[e]=Wd(Vd(t,o,e));return{c(){e=Y("span"),e.textContent=i,n=rt();for(let t=0;t<s.length;t+=1)s[t].c();r=ut(),this.h()},l(t){e=q(t,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-14mun4z"!==vr(e)&&(e.textContent=i),n=nt(t);for(let e=0;e<s.length;e+=1)s[e].l(t);r=ut(),this.h()},h(){C(e,"class","block text-xs sticky top-0 mb-2 bg-base-100 shadow-base-100 font-medium")},m(t,i){L(t,e,i),L(t,n,i);for(let e=0;e<s.length;e+=1)s[e]&&s[e].m(t,i);L(t,r,i)},p(t,e){if(3&e){let n;for(o=he(t[0]),n=0;n<o.length;n+=1){const i=Vd(t,o,n);s[n]?s[n].p(i,e):(s[n]=Wd(i),s[n].c(),s[n].m(r.parentNode,r))}for(;n<s.length;n+=1)s[n].d(1);s.length=o.length}},d(t){t&&(w(e),w(n),w(r)),An(s,t)}}}function Wd(t){let e,n,r,i,o,s=t[5].innerText+"";return{c(){e=Y("a"),n=wt(s),r=rt(),this.h()},l(t){e=q(t,"A",{href:!0,class:!0});var i=J(e);n=vt(i,s),r=nt(i),i.forEach(w),this.h()},h(){C(e,"href",i="#"+t[5].id),C(e,"class",o=t[1][t[5].nodeName.toLowerCase()]+" block text-xs transition-all duration-200 py-1 hover:underline")},m(t,i){L(t,e,i),W(e,n),W(e,r)},p(t,r){1&r&&s!==(s=t[5].innerText+"")&&Re(n,s),1&r&&i!==(i="#"+t[5].id)&&C(e,"href",i),1&r&&o!==(o=t[1][t[5].nodeName.toLowerCase()]+" block text-xs transition-all duration-200 py-1 hover:underline")&&C(e,"class",o)},d(t){t&&w(e)}}}function bS(t){let e,n=t[0]&&t[0].length>1&&zd(t);return{c(){n&&n.c(),e=ut()},l(t){n&&n.l(t),e=ut()},m(t,r){n&&n.m(t,r),L(t,e,r)},p(t,[r]){t[0]&&t[0].length>1?n?n.p(t,r):(n=zd(t),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},i:Xt,o:Xt,d(t){t&&w(e),n&&n.d(t)}}}function yS(t,e,n){let r,i=[];function o(){n(0,i=Array.from(document.querySelectorAll("h1.markdown, h2.markdown, h3.markdown")))}return Cr((()=>{o(),r=new MutationObserver((()=>{o()})),i.forEach((t=>{r.observe(t,{subtree:!0,characterData:!0,childList:!0})}))})),Xg((()=>{null==r||r.disconnect()})),[i,{h1:"mt-3 font-semibold block bg-base-100 shadow shadow-base-100",h2:"pl-0 text-base-content-muted",h3:"pl-4 text-base-content-muted"}]}class vS extends re{constructor(t){super(),ie(this,t,yS,bS,Zt,{})}}function Hd(t){let e,n,r;return n=new vS({}),{c(){e=Y("div"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"class","fixed w-48 top-20 bottom-20 pl-4 px-3 overflow-auto pretty-scrollbar"),Fr(e,"top-8",t[0])},m(t,i){L(t,e,i),st(n,e,null),r=!0},p(t,n){(!r||1&n)&&Fr(e,"top-8",t[0])},i(t){r||(I(n.$$.fragment,t),r=!0)},o(t){E(n.$$.fragment,t),r=!1},d(t){t&&w(e),ot(n)}}}function wS(t){let e,n,r=!t[1]&&t[2].data.isUserPage&&Hd(t);return{c(){e=Y("aside"),r&&r.c(),this.h()},l(t){e=q(t,"ASIDE",{class:!0});var n=J(e);r&&r.l(n),n.forEach(w),this.h()},h(){C(e,"class","hidden lg:block w-48")},m(t,i){L(t,e,i),r&&r.m(e,null),n=!0},p(t,[n]){!t[1]&&t[2].data.isUserPage?r?(r.p(t,n),6&n&&I(r,1)):(r=Hd(t),r.c(),I(r,1),r.m(e,null)):r&&(jt(),E(r,1,1,(()=>{r=null})),Lt())},i(t){n||(I(r),n=!0)},o(t){E(r),n=!1},d(t){t&&w(e),r&&r.d()}}}function SS(t,e,n){let r,i;Te(t,Th,(t=>n(1,r=t))),Te(t,Na,(t=>n(2,i=t)));let{hideHeader:o=!1}=e;return t.$$set=t=>{"hideHeader"in t&&n(0,o=t.hideHeader)},[o,r,i]}class IS extends re{constructor(t){super(),ie(this,t,SS,wS,Zt,{hideHeader:0})}}function xd(t,e,n){const r=t.slice();return r[3]=e[n],r[5]=n,r}function OS(t){let e,n,r,i,o=t[3].title+"";return{c(){e=Y("a"),n=wt(o),r=rt(),this.h()},l(t){e=q(t,"A",{href:!0,class:!0});var i=J(e);n=vt(i,o),r=nt(i),i.forEach(w),this.h()},h(){C(e,"href",i=Wt(t[3].href)),C(e,"class","hover:underline")},m(t,i){L(t,e,i),W(e,n),W(e,r)},p(t,r){1&r&&o!==(o=t[3].title+"")&&Re(n,o),1&r&&i!==(i=Wt(t[3].href))&&C(e,"href",i)},i:Xt,o:Xt,d(t){t&&w(e)}}}function kS(t){let e,n,r,i;function o(t,e){return t[3].href?ES:DS}e=new Pn({props:{src:y_,size:"12px",theme:"solid"}});let s=o(t),a=s(t);return{c(){lt(e.$$.fragment),n=rt(),a.c(),r=ut()},l(t){at(e.$$.fragment,t),n=nt(t),a.l(t),r=ut()},m(t,o){st(e,t,o),L(t,n,o),a.m(t,o),L(t,r,o),i=!0},p(t,e){s===(s=o(t))&&a?a.p(t,e):(a.d(1),a=s(t),a&&(a.c(),a.m(r.parentNode,r)))},i(t){i||(I(e.$$.fragment,t),i=!0)},o(t){E(e.$$.fragment,t),i=!1},d(t){t&&(w(n),w(r)),ot(e,t),a.d(t)}}}function DS(t){let e,n,r=t[3].title+"";return{c(){e=Y("span"),n=wt(r),this.h()},l(t){e=q(t,"SPAN",{class:!0});var i=J(e);n=vt(i,r),i.forEach(w),this.h()},h(){C(e,"class","cursor-default")},m(t,r){L(t,e,r),W(e,n)},p(t,e){1&e&&r!==(r=t[3].title+"")&&Re(n,r)},d(t){t&&w(e)}}}function ES(t){let e,n,r,i=t[3].title+"";return{c(){e=Y("a"),n=wt(i),this.h()},l(t){e=q(t,"A",{href:!0,class:!0});var r=J(e);n=vt(r,i),r.forEach(w),this.h()},h(){C(e,"href",r=Wt(t[3].href)),C(e,"class","hover:underline")},m(t,r){L(t,e,r),W(e,n)},p(t,o){1&o&&i!==(i=t[3].title+"")&&Re(n,i),1&o&&r!==(r=Wt(t[3].href))&&C(e,"href",r)},d(t){t&&w(e)}}}function qd(t){let e,n,r,i;const o=[kS,OS],s=[];return e=t[5]>0?0:1,n=s[e]=o[e](t),{c(){n.c(),r=ut()},l(t){n.l(t),r=ut()},m(t,n){s[e].m(t,n),L(t,r,n),i=!0},p(t,e){n.p(t,e)},i(t){i||(I(n),i=!0)},o(t){E(n),i=!1},d(t){t&&w(r),s[e].d(t)}}}function AS(t){let e,n,r,i=he(t[0]),o=[];for(let e=0;e<i.length;e+=1)o[e]=qd(xd(t,i,e));const s=t=>E(o[t],1,1,(()=>{o[t]=null}));return{c(){e=Y("div"),n=Y("div");for(let t=0;t<o.length;t+=1)o[t].c();this.h()},l(t){e=q(t,"DIV",{class:!0});var r=J(e);n=q(r,"DIV",{class:!0});var i=J(n);for(let t=0;t<o.length;t+=1)o[t].l(i);i.forEach(w),r.forEach(w),this.h()},h(){C(n,"class","inline-flex items-center text-sm capitalize gap-1 text-base-content-muted mb-2 sm:mb-4"),C(e,"class","flex items-start mt-0 whitespace-nowrap overflow-auto")},m(t,i){L(t,e,i),W(e,n);for(let t=0;t<o.length;t+=1)o[t]&&o[t].m(n,null);r=!0},p(t,[e]){if(1&e){let r;for(i=he(t[0]),r=0;r<i.length;r+=1){const s=xd(t,i,r);o[r]?(o[r].p(s,e),I(o[r],1)):(o[r]=qd(s),o[r].c(),I(o[r],1),o[r].m(n,null))}for(jt(),r=i.length;r<o.length;r+=1)s(r);Lt()}},i(t){if(!r){for(let t=0;t<i.length;t+=1)I(o[t]);r=!0}},o(t){o=o.filter(Boolean);for(let t=0;t<o.length;t+=1)E(o[t]);r=!1},d(t){t&&w(e),An(o,t)}}}function TS(t,e){if("/"===t)return e;const n=t.replace(wh.deployment.basePath,"").split("/").slice(1);let r=e;for(let t of n)if(r=r.children[t]?r.children[t]:Object.values(r.children).find((t=>t.isTemplated)),!r)return null;return r}function BS(t,e){const n=[{href:"/",title:"Home"}];t.forEach(((e,r)=>{""!=e&&`/${e}`!==wh.deployment.basePath&&n.push({href:"/"+t.slice(0,r+1).join("/"),title:decodeURIComponent(e.replace(/_/g," ").replace(/-/g," "))})})),n.length>3&&n.splice(1,n.length-3,{href:n.slice(-3)[0].href,title:"..."});for(const t of n)if("/"===t.href)t.href=Wt("/"),t.title="Home";else{const n=TS(t.href,e);n&&n.isPage?t.title=n.title??t.title:t.href=null}return n}function PS(t,e,n){let r,i;Te(t,Na,(t=>n(2,i=t)));let{fileTree:o}=e;return t.$$set=t=>{"fileTree"in t&&n(1,o=t.fileTree)},t.$$.update=()=>{6&t.$$.dirty&&n(0,r=BS(i.url.pathname.split("/").slice(1),o))},[r,o,i]}class MS extends re{constructor(t){super(),ie(this,t,PS,AS,Zt,{fileTree:1})}}function Yd(t){let e,n,r,i,o,s,a,l,c,u,d,h="Error",f='<a href="https://docs.evidence.dev" target="”_blank”" class="hover:text-base-content-muted transition-colors duration-200">docs</a> <a href="https://evidencedev.slack.com/join/shared_invite/zt-uda6wp6a-hP6Qyz0LUOddwpXW5qG03Q#/shared-invite/email" target="”_blank”" class="hover:text-base-content-muted transition-colors duration-200">slack</a> <a href="mailto:<EMAIL>" class="hover:text-base-content-muted transition-colors duration-200">email</a>';return{c(){e=Y("div"),n=Y("div"),r=Y("h1"),r.textContent=h,i=rt(),o=Y("p"),s=wt(t[1]),a=rt(),l=Y("div"),l.innerHTML=f,this.h()},l(c){e=q(c,"DIV",{class:!0});var u=J(e);n=q(u,"DIV",{class:!0});var d=J(n);r=q(d,"H1",{class:!0,"data-svelte-h":!0}),"svelte-1wczc15"!==vr(r)&&(r.textContent=h),i=nt(d),o=q(d,"P",{class:!0});var p=J(o);s=vt(p,t[1]),p.forEach(w),a=nt(d),l=q(d,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-vfh8n7"!==vr(l)&&(l.innerHTML=f),d.forEach(w),u.forEach(w),this.h()},h(){C(r,"class","text-2xl font-bold tracking-wide border-b pb-4 border-base-300"),C(o,"class","text-xl mt-6 leading-relaxed select-text"),C(l,"class","absolute bottom-0 flex items-end gap-4 text-lg mb-6"),C(n,"class","relative min-w-full h-screen bg-gradient-to-b from-base-200 to-base-300 rounded-lg border-t-8 border-negative shadow-xl p-8"),C(e,"class","fixed flex flex-col z-50 h-screen w-screen bg-base-100/50 justify-center items-center py-20 px-10 sm:px-20 select-none backdrop-blur-sm")},m(t,c){L(t,e,c),W(e,n),W(n,r),W(n,i),W(n,o),W(o,s),W(n,a),W(n,l),d=!0},p(t,e){(!d||2&e)&&Re(s,t[1])},i(t){d||(t&&En((()=>{d&&(c||(c=Dn(n,ii,{y:100,duration:300},!0)),c.run(1))})),t&&En((()=>{d&&(u||(u=Dn(e,xo,{duration:100},!0)),u.run(1))})),d=!0)},o(t){t&&(c||(c=Dn(n,ii,{y:100,duration:300},!1)),c.run(0)),t&&(u||(u=Dn(e,xo,{duration:100},!1)),u.run(0)),d=!1},d(t){t&&w(e),t&&c&&c.end(),t&&u&&u.end()}}}function NS(t){let e,n=t[0]&&Yd(t);return{c(){n&&n.c(),e=ut()},l(t){n&&n.l(t),e=ut()},m(t,r){n&&n.m(t,r),L(t,e,r)},p(t,[r]){t[0]?n?(n.p(t,r),1&r&&I(n,1)):(n=Yd(t),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(jt(),E(n,1,1,(()=>{n=null})),Lt())},i(t){I(n)},o(t){E(n)},d(t){t&&w(e),n&&n.d(t)}}}function CS(t,e,n){return[!1,void 0]}class FS extends re{constructor(t){super(),ie(this,t,CS,NS,Zt,{})}}function jS(t){let e,n,r=JSON.stringify(t[0],null,2)+"";return{c(){e=Y("pre"),n=wt(r),this.h()},l(t){e=q(t,"PRE",{class:!0});var i=J(e);n=vt(i,r),i.forEach(w),this.h()},h(){C(e,"class","text-xs px-2 py-2 bg-base-200 my-2")},m(t,r){L(t,e,r),W(e,n)},p(t,[e]){1&e&&r!==(r=JSON.stringify(t[0],null,2)+"")&&Re(n,r)},i:Xt,o:Xt,d(t){t&&w(e)}}}function LS(t,e,n){let r;const i=Sh();return Te(t,i,(t=>n(0,r=t))),[r,i]}class RS extends re{constructor(t){super(),ie(this,t,LS,jS,Zt,{})}}function US(t){const e=[{type:"unchanged",content:"{"}];function n(t,e){return e.reduce(((t,e)=>null==t?void 0:t[e]),t)}return function r(i,o){const s=Object.keys(i);s.forEach(((a,l)=>{const c=n(t.added,o)??{},u=n(t.deleted,o)??{},d=n(t.updated,o)??{};let h="unchanged";a in c&&(h="added"),a in u&&(h="deleted"),a in d&&(h="updated");const f=(t,e=!1)=>{const n=`"${a}": `;let r=`${"  ".repeat(o.length+1)}${e?"":n}${t}`;return l<s.length-1&&(r+=","),r};if("object"==typeof i[a])return e.push({type:"updated"===h?"unchanged":h,content:f("{")}),r(i[a],o.concat(a)),void e.push({type:"updated"===h?"unchanged":h,content:f("}",!0)});{const r="deleted"===h?n(t.before,o)[a]:i[a];return void e.push({type:h,content:f(JSON.stringify(r))})}}))}(v_(t.before,t.after),[]),e.push({type:"unchanged",content:"}"}),e}const VS={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},zS=(t,e,n)=>{let r;const i=VS[t];return r="string"==typeof i?i:1===e?i.one:i.other.replace("{{count}}",e.toString()),null!=n&&n.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r};function vl(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const WS={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},HS={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},xS={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},qS={date:vl({formats:WS,defaultWidth:"full"}),time:vl({formats:HS,defaultWidth:"full"}),dateTime:vl({formats:xS,defaultWidth:"full"})},YS={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},KS=(t,e,n,r)=>YS[t];function uo(t){return(e,n)=>{let r;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&t.formattingValues){const e=t.defaultFormattingWidth||t.defaultWidth,i=null!=n&&n.width?String(n.width):e;r=t.formattingValues[i]||t.formattingValues[e]}else{const e=t.defaultWidth,i=null!=n&&n.width?String(n.width):t.defaultWidth;r=t.values[i]||t.values[e]}return r[t.argumentCallback?t.argumentCallback(e):e]}}const JS={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},QS={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},GS={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},XS={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},ZS={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},$S={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},tI=(t,e)=>{const n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},eI={ordinalNumber:tI,era:uo({values:JS,defaultWidth:"wide"}),quarter:uo({values:QS,defaultWidth:"wide",argumentCallback:t=>t-1}),month:uo({values:GS,defaultWidth:"wide"}),day:uo({values:XS,defaultWidth:"wide"}),dayPeriod:uo({values:ZS,defaultWidth:"wide",formattingValues:$S,defaultFormattingWidth:"wide"})};function fo(t){return(e,n={})=>{const r=n.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;const s=o[0],a=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],l=Array.isArray(a)?rI(a,(t=>t.test(s))):nI(a,(t=>t.test(s)));let c;return c=t.valueCallback?t.valueCallback(l):l,c=n.valueCallback?n.valueCallback(c):c,{value:c,rest:e.slice(s.length)}}}function nI(t,e){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}function rI(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}function iI(t){return(e,n={})=>{const r=e.match(t.matchPattern);if(!r)return null;const i=r[0],o=e.match(t.parsePattern);if(!o)return null;let s=t.valueCallback?t.valueCallback(o[0]):o[0];return s=n.valueCallback?n.valueCallback(s):s,{value:s,rest:e.slice(i.length)}}}const oI=/^(\d+)(th|st|nd|rd)?/i,sI=/\d+/i,aI={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},lI={any:[/^b/i,/^(a|c)/i]},cI={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},uI={any:[/1/i,/2/i,/3/i,/4/i]},fI={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},dI={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},hI={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},mI={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},pI={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},gI={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},_I={ordinalNumber:iI({matchPattern:oI,parsePattern:sI,valueCallback:t=>parseInt(t,10)}),era:fo({matchPatterns:aI,defaultMatchWidth:"wide",parsePatterns:lI,defaultParseWidth:"any"}),quarter:fo({matchPatterns:cI,defaultMatchWidth:"wide",parsePatterns:uI,defaultParseWidth:"any",valueCallback:t=>t+1}),month:fo({matchPatterns:fI,defaultMatchWidth:"wide",parsePatterns:dI,defaultParseWidth:"any"}),day:fo({matchPatterns:hI,defaultMatchWidth:"wide",parsePatterns:mI,defaultParseWidth:"any"}),dayPeriod:fo({matchPatterns:pI,defaultMatchWidth:"any",parsePatterns:gI,defaultParseWidth:"any"})},Mg={code:"en-US",formatDistance:zS,formatLong:qS,formatRelative:KS,localize:eI,match:_I,options:{weekStartsOn:0,firstWeekContainsDate:1}};let bI={};function ms(){return bI}function yI(t){return e=>{const n=(t?Math[t]:Math.trunc)(e);return 0===n?0:n}}function xe(t){const e=Object.prototype.toString.call(t);return t instanceof Date||"object"==typeof t&&"[object Date]"===e?new t.constructor(+t):"number"==typeof t||"[object Number]"===e||"string"==typeof t||"[object String]"===e?new Date(t):new Date(NaN)}function Ta(t){const e=xe(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function vI(t,e){const n=xe(t),r=xe(e),i=n.getTime()-r.getTime();return i<0?-1:i>0?1:i}const Ng=6048e5,wI=864e5,Kd=6e4,Jd=525600,Qd=43200,Gd=1440;function SI(t,e,n){const r=ms(),i=(null==n?void 0:n.locale)??r.locale??Mg,o=vI(t,e);if(isNaN(o))throw new RangeError("Invalid time value");const s=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:o});let a,l;o>0?(a=xe(e),l=xe(t)):(a=xe(t),l=xe(e));const c=yI((null==n?void 0:n.roundingMethod)??"round"),u=l.getTime()-a.getTime(),d=u/Kd,h=(u-(Ta(l)-Ta(a)))/Kd,f=null==n?void 0:n.unit;let p;if(p=f||(d<1?"second":d<60?"minute":d<Gd?"hour":h<Qd?"day":h<Jd?"month":"year"),"second"===p){const t=c(u/1e3);return i.formatDistance("xSeconds",t,s)}if("minute"===p){const t=c(d);return i.formatDistance("xMinutes",t,s)}if("hour"===p){const t=c(d/60);return i.formatDistance("xHours",t,s)}if("day"===p){const t=c(h/Gd);return i.formatDistance("xDays",t,s)}if("month"===p){const t=c(h/Qd);return 12===t&&"month"!==f?i.formatDistance("xYears",1,s):i.formatDistance("xMonths",t,s)}{const t=c(h/Jd);return i.formatDistance("xYears",t,s)}}function Vr(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}function II(t){return Vr(t,Date.now())}function Xd(t,e){return SI(t,II(t),e)}function Zd(t){const e=xe(t);return e.setHours(0,0,0,0),e}function OI(t,e){const n=Zd(t),r=Zd(e),i=+n-Ta(n),o=+r-Ta(r);return Math.round((i-o)/wI)}function kI(t){const e=xe(t),n=Vr(t,0);return n.setFullYear(e.getFullYear(),0,1),n.setHours(0,0,0,0),n}function DI(t){const e=xe(t);return OI(e,kI(e))+1}function es(t,e){var n,r,i,o;const s=ms(),a=(null==e?void 0:e.weekStartsOn)??(null==(r=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:r.weekStartsOn)??s.weekStartsOn??(null==(o=null==(i=s.locale)?void 0:i.options)?void 0:o.weekStartsOn)??0,l=xe(t),c=l.getDay(),u=(c<a?7:0)+c-a;return l.setDate(l.getDate()-u),l.setHours(0,0,0,0),l}function Ba(t){return es(t,{weekStartsOn:1})}function Cg(t){const e=xe(t),n=e.getFullYear(),r=Vr(t,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const i=Ba(r),o=Vr(t,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);const s=Ba(o);return e.getTime()>=i.getTime()?n+1:e.getTime()>=s.getTime()?n:n-1}function EI(t){const e=Cg(t),n=Vr(t,0);return n.setFullYear(e,0,4),n.setHours(0,0,0,0),Ba(n)}function AI(t){const e=xe(t),n=+Ba(e)-+EI(e);return Math.round(n/Ng)+1}function Fg(t,e){var n,r,i,o;const s=xe(t),a=s.getFullYear(),l=ms(),c=(null==e?void 0:e.firstWeekContainsDate)??(null==(r=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:r.firstWeekContainsDate)??l.firstWeekContainsDate??(null==(o=null==(i=l.locale)?void 0:i.options)?void 0:o.firstWeekContainsDate)??1,u=Vr(t,0);u.setFullYear(a+1,0,c),u.setHours(0,0,0,0);const d=es(u,e),h=Vr(t,0);h.setFullYear(a,0,c),h.setHours(0,0,0,0);const f=es(h,e);return s.getTime()>=d.getTime()?a+1:s.getTime()>=f.getTime()?a:a-1}function TI(t,e){var n,r,i,o;const s=ms(),a=(null==e?void 0:e.firstWeekContainsDate)??(null==(r=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:r.firstWeekContainsDate)??s.firstWeekContainsDate??(null==(o=null==(i=s.locale)?void 0:i.options)?void 0:o.firstWeekContainsDate)??1,l=Fg(t,e),c=Vr(t,0);return c.setFullYear(l,0,a),c.setHours(0,0,0,0),es(c,e)}function BI(t,e){const n=xe(t),r=+es(n,e)-+TI(n,e);return Math.round(r/Ng)+1}function ue(t,e){return(t<0?"-":"")+Math.abs(t).toString().padStart(e,"0")}const Br={y(t,e){const n=t.getFullYear(),r=n>0?n:1-n;return ue("yy"===e?r%100:r,e.length)},M(t,e){const n=t.getMonth();return"M"===e?String(n+1):ue(n+1,2)},d:(t,e)=>ue(t.getDate(),e.length),a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>ue(t.getHours()%12||12,e.length),H:(t,e)=>ue(t.getHours(),e.length),m:(t,e)=>ue(t.getMinutes(),e.length),s:(t,e)=>ue(t.getSeconds(),e.length),S(t,e){const n=e.length,r=t.getMilliseconds();return ue(Math.trunc(r*Math.pow(10,n-3)),e.length)}},pi={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},$d={G:function(t,e,n){const r=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){const e=t.getFullYear(),r=e>0?e:1-e;return n.ordinalNumber(r,{unit:"year"})}return Br.y(t,e)},Y:function(t,e,n,r){const i=Fg(t,r),o=i>0?i:1-i;return"YY"===e?ue(o%100,2):"Yo"===e?n.ordinalNumber(o,{unit:"year"}):ue(o,e.length)},R:function(t,e){return ue(Cg(t),e.length)},u:function(t,e){return ue(t.getFullYear(),e.length)},Q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return ue(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){const r=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return ue(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){const r=t.getMonth();switch(e){case"M":case"MM":return Br.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){const r=t.getMonth();switch(e){case"L":return String(r+1);case"LL":return ue(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){const i=BI(t,r);return"wo"===e?n.ordinalNumber(i,{unit:"week"}):ue(i,e.length)},I:function(t,e,n){const r=AI(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):ue(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):Br.d(t,e)},D:function(t,e,n){const r=DI(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):ue(r,e.length)},E:function(t,e,n){const r=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){const i=t.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(o);case"ee":return ue(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){const i=t.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(o);case"cc":return ue(o,e.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(i,{width:"narrow",context:"standalone"});case"cccccc":return n.day(i,{width:"short",context:"standalone"});default:return n.day(i,{width:"wide",context:"standalone"})}},i:function(t,e,n){const r=t.getDay(),i=0===r?7:r;switch(e){case"i":return String(i);case"ii":return ue(i,e.length);case"io":return n.ordinalNumber(i,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){const r=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){const r=t.getHours();let i;switch(i=12===r?pi.noon:0===r?pi.midnight:r/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(t,e,n){const r=t.getHours();let i;switch(i=r>=17?pi.evening:r>=12?pi.afternoon:r>=4?pi.morning:pi.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return Br.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):Br.H(t,e)},K:function(t,e,n){const r=t.getHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):ue(r,e.length)},k:function(t,e,n){let r=t.getHours();return 0===r&&(r=24),"ko"===e?n.ordinalNumber(r,{unit:"hour"}):ue(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):Br.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):Br.s(t,e)},S:function(t,e){return Br.S(t,e)},X:function(t,e,n){const r=t.getTimezoneOffset();if(0===r)return"Z";switch(e){case"X":return eh(r);case"XXXX":case"XX":return $r(r);default:return $r(r,":")}},x:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"x":return eh(r);case"xxxx":case"xx":return $r(r);default:return $r(r,":")}},O:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+th(r,":");default:return"GMT"+$r(r,":")}},z:function(t,e,n){const r=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+th(r,":");default:return"GMT"+$r(r,":")}},t:function(t,e,n){return ue(Math.trunc(t.getTime()/1e3),e.length)},T:function(t,e,n){return ue(t.getTime(),e.length)}};function th(t,e=""){const n=t>0?"-":"+",r=Math.abs(t),i=Math.trunc(r/60),o=r%60;return 0===o?n+String(i):n+String(i)+e+ue(o,2)}function eh(t,e){return t%60==0?(t>0?"-":"+")+ue(Math.abs(t)/60,2):$r(t,e)}function $r(t,e=""){const n=t>0?"-":"+",r=Math.abs(t);return n+ue(Math.trunc(r/60),2)+e+ue(r%60,2)}const nh=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},jg=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},PI=(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],r=n[1],i=n[2];if(!i)return nh(t,e);let o;switch(r){case"P":o=e.dateTime({width:"short"});break;case"PP":o=e.dateTime({width:"medium"});break;case"PPP":o=e.dateTime({width:"long"});break;default:o=e.dateTime({width:"full"})}return o.replace("{{date}}",nh(r,e)).replace("{{time}}",jg(i,e))},MI={p:jg,P:PI},NI=/^D+$/,CI=/^Y+$/,FI=["D","DD","YY","YYYY"];function jI(t){return NI.test(t)}function LI(t){return CI.test(t)}function RI(t,e,n){const r=UI(t,e,n);if(console.warn(r),FI.includes(t))throw new RangeError(r)}function UI(t,e,n){const r="Y"===t[0]?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}function VI(t){return t instanceof Date||"object"==typeof t&&"[object Date]"===Object.prototype.toString.call(t)}function zI(t){if(!VI(t)&&"number"!=typeof t)return!1;const e=xe(t);return!isNaN(Number(e))}const WI=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,HI=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,xI=/^'([^]*?)'?$/,qI=/''/g,YI=/[a-zA-Z]/;function rh(t,e,n){var r,i,o,s;const a=ms(),l=a.locale??Mg,c=a.firstWeekContainsDate??(null==(i=null==(r=a.locale)?void 0:r.options)?void 0:i.firstWeekContainsDate)??1,u=a.weekStartsOn??(null==(s=null==(o=a.locale)?void 0:o.options)?void 0:s.weekStartsOn)??0,d=xe(t);if(!zI(d))throw new RangeError("Invalid time value");let h=e.match(HI).map((t=>{const e=t[0];return"p"===e||"P"===e?(0,MI[e])(t,l.formatLong):t})).join("").match(WI).map((t=>{if("''"===t)return{isToken:!1,value:"'"};const e=t[0];if("'"===e)return{isToken:!1,value:KI(t)};if($d[e])return{isToken:!0,value:t};if(e.match(YI))throw new RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}}));l.localize.preprocessor&&(h=l.localize.preprocessor(d,h));const f={firstWeekContainsDate:c,weekStartsOn:u,locale:l};return h.map((n=>{if(!n.isToken)return n.value;const r=n.value;return(LI(r)||jI(r))&&RI(r,e,String(t)),(0,$d[r[0]])(d,r,l.localize,f)})).join("")}function KI(t){const e=t.match(xI);return e?e[1].replace(qI,"'"):t}function ih(t,e,n){const r=t.slice();return r[6]=e[n],r}function oh(t){let e,n,r,i,o,s,a,l,c,u,d=t[4](t[6].type)+"",h=t[6].content+"";return{c(){e=Y("div"),n=Y("span"),r=wt(d),o=rt(),s=Y("div"),a=Y("pre"),l=wt(h),u=rt(),this.h()},l(t){e=q(t,"DIV",{class:!0});var i=J(e);n=q(i,"SPAN",{class:!0});var c=J(n);r=vt(c,d),c.forEach(w),o=nt(i),s=q(i,"DIV",{class:!0});var f=J(s);a=q(f,"PRE",{class:!0});var p=J(a);l=vt(p,h),p.forEach(w),f.forEach(w),u=nt(i),i.forEach(w),this.h()},h(){C(n,"class",i=t[3][t[6].type]+" px-1 select-none"),C(a,"class","whitespace-pre-wrap"),C(s,"class",c=t[3][t[6].type]+" selection:bg-black/15"),C(e,"class","group contents")},m(t,i){L(t,e,i),W(e,n),W(n,r),W(e,o),W(e,s),W(s,a),W(a,l),W(e,u)},p(t,e){2&e&&d!==(d=t[4](t[6].type)+"")&&Re(r,d),2&e&&i!==(i=t[3][t[6].type]+" px-1 select-none")&&C(n,"class",i),2&e&&h!==(h=t[6].content+"")&&Re(l,h),2&e&&c!==(c=t[3][t[6].type]+" selection:bg-black/15")&&C(s,"class",c)},d(t){t&&w(e)}}}function JI(t){let e,n,r,i,o,s,a,l,c,u=rh(t[0].asof,"HH:mm:ss")+"",d=he(t[1]),h=[];for(let e=0;e<d.length;e+=1)h[e]=oh(ih(t,d,e));return{c(){e=Y("section"),n=Y("span"),r=wt("About "),i=wt(t[2]),o=wt(" ("),s=wt(u),a=wt(")"),l=rt(),c=Y("div");for(let t=0;t<h.length;t+=1)h[t].c();this.h()},l(d){e=q(d,"SECTION",{});var f=J(e);n=q(f,"SPAN",{});var p=J(n);r=vt(p,"About "),i=vt(p,t[2]),o=vt(p," ("),s=vt(p,u),a=vt(p,")"),p.forEach(w),l=nt(f),c=q(f,"DIV",{class:!0});var m=J(c);for(let t=0;t<h.length;t+=1)h[t].l(m);m.forEach(w),f.forEach(w),this.h()},h(){C(c,"class","font-mono text-xs grid grid-cols-[auto,1fr] text-[0.7rem] bg-base-200 p-2 select-text")},m(t,u){L(t,e,u),W(e,n),W(n,r),W(n,i),W(n,o),W(n,s),W(n,a),W(e,l),W(e,c);for(let t=0;t<h.length;t+=1)h[t]&&h[t].m(c,null)},p(t,[e]){if(4&e&&Re(i,t[2]),1&e&&u!==(u=rh(t[0].asof,"HH:mm:ss")+"")&&Re(s,u),26&e){let n;for(d=he(t[1]),n=0;n<d.length;n+=1){const r=ih(t,d,n);h[n]?h[n].p(r,e):(h[n]=oh(r),h[n].c(),h[n].m(c,null))}for(;n<h.length;n+=1)h[n].d(1);h.length=d.length}},i:Xt,o:Xt,d(t){t&&w(e),An(h,t)}}}function QI(t,e,n){let r,i,{diffData:o={added:{},deleted:{},updated:{},before:{},after:{},asof:new Date}}=e;const s=O_(null,(t=>{t(Xd(o.asof,{addSuffix:!0,includeSeconds:!0}));const e=setInterval((()=>{t(Xd(o.asof,{addSuffix:!0,includeSeconds:!0}))}),5e3);return()=>{clearInterval(e)}}));return Te(t,s,(t=>n(2,i=t))),t.$$set=t=>{"diffData"in t&&n(0,o=t.diffData)},t.$$.update=()=>{1&t.$$.dirty&&n(1,r=US(o))},[o,r,i,{added:"bg-positive/25",deleted:"bg-negative/25",updated:"bg-warning/25",unchanged:""},t=>{switch(t){case"added":return"+";case"deleted":return"-";case"updated":return"~";case"unchanged":return" ";default:return"?"}},s]}class GI extends re{constructor(t){super(),ie(this,t,QI,JI,Zt,{diffData:0})}}function sh(t,e,n){const r=t.slice();return r[2]=e[n],r}function ah(t){let e,n,r,i;return n=new GI({props:{diffData:t[2]}}),{c(){e=Y("div"),lt(n.$$.fragment),r=rt(),this.h()},l(t){e=q(t,"DIV",{class:!0});var i=J(e);at(n.$$.fragment,i),r=nt(i),i.forEach(w),this.h()},h(){C(e,"class","my-4")},m(t,o){L(t,e,o),st(n,e,null),W(e,r),i=!0},p(t,e){const r={};2&e&&(r.diffData=t[2]),n.$set(r)},i(t){i||(I(n.$$.fragment,t),i=!0)},o(t){E(n.$$.fragment,t),i=!1},d(t){t&&w(e),ot(n)}}}function XI(t){let e,n,r=he(t[1].reverse()),i=[];for(let e=0;e<r.length;e+=1)i[e]=ah(sh(t,r,e));const o=t=>E(i[t],1,1,(()=>{i[t]=null}));return{c(){for(let t=0;t<i.length;t+=1)i[t].c();e=ut()},l(t){for(let e=0;e<i.length;e+=1)i[e].l(t);e=ut()},m(t,r){for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,r);L(t,e,r),n=!0},p(t,[n]){if(2&n){let s;for(r=he(t[1].reverse()),s=0;s<r.length;s+=1){const o=sh(t,r,s);i[s]?(i[s].p(o,n),I(i[s],1)):(i[s]=ah(o),i[s].c(),I(i[s],1),i[s].m(e.parentNode,e))}for(jt(),s=r.length;s<i.length;s+=1)o(s);Lt()}},i(t){if(!n){for(let t=0;t<r.length;t+=1)I(i[t]);n=!0}},o(t){i=i.filter(Boolean);for(let t=0;t<i.length;t+=1)E(i[t]);n=!1},d(t){t&&w(e),An(i,t)}}}function ZI(t,e,n){let r,i=Xt,o=()=>(i(),i=Zg(s,(t=>n(1,r=t))),s);t.$$.on_destroy.push((()=>i()));let{history:s}=e;return o(),t.$$set=t=>{"history"in t&&o(n(0,s=t.history))},[s,r]}class $I extends re{constructor(t){super(),ie(this,t,ZI,XI,Zt,{history:0})}}function lh(t,e,n){const r=t.slice();return r[12]=e[n][0],r[13]=e[n][1],r}function ch(t){let e,n,r,i,o,s,a,l,c,u,d,h="Evidence Dev Tools";return r=new Pn({props:{src:t[0]?Xs:Il,class:"w-4 h-4"}}),a=new F_({props:{$$slots:{default:[rO]},$$scope:{ctx:t}}}),{c(){e=Y("div"),n=Y("button"),lt(r.$$.fragment),i=rt(),o=Y("header"),o.textContent=h,s=rt(),lt(a.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var l=J(e);n=q(l,"BUTTON",{class:!0});var c=J(n);at(r.$$.fragment,c),c.forEach(w),i=nt(l),o=q(l,"HEADER",{class:!0,"data-svelte-h":!0}),"svelte-ekyf9x"!==vr(o)&&(o.textContent=h),s=nt(l),at(a.$$.fragment,l),l.forEach(w),this.h()},h(){C(n,"class","absolute right-4 top-4 rounded-full bg-info text-info-content w-8 h-8 flex items-center justify-center hover:brightness-110 z-30"),C(o,"class","text-xl font-bold mb-4"),C(e,"class","h-[calc(100vh-3rem)] w-96 bg-base-100 fixed overflow-auto right-0 top-12 px-4 py-4 z-10")},m(l,h){L(l,e,h),W(e,n),st(r,n,null),W(e,i),W(e,o),W(e,s),st(a,e,null),c=!0,u||(d=Pt(n,"click",t[7]),u=!0)},p(t,e){const n={};1&e&&(n.src=t[0]?Xs:Il),r.$set(n);const i={};2054&e&&(i.$$scope={dirty:e,ctx:t}),a.$set(i)},i(t){c||(I(r.$$.fragment,t),I(a.$$.fragment,t),t&&En((()=>{c&&(l||(l=Dn(e,ii,{x:384,duration:250,delay:0},!0)),l.run(1))})),c=!0)},o(t){E(r.$$.fragment,t),E(a.$$.fragment,t),t&&(l||(l=Dn(e,ii,{x:384,duration:250,delay:0},!1)),l.run(0)),c=!1},d(t){t&&w(e),ot(r),ot(a),t&&l&&l.end(),u=!1,d()}}}function uh(t,e){let n,r,i,o,s,a,l,c,u,d=e[13].id+"",h=e[13].hash+"";function f(){return e[8](e[13])}return{key:t,first:null,c(){n=Y("button"),r=Y("p"),i=wt(d),o=rt(),s=Y("p"),a=wt(h),l=rt(),this.h()},l(t){n=q(t,"BUTTON",{class:!0});var e=J(n);r=q(e,"P",{class:!0});var c=J(r);i=vt(c,d),c.forEach(w),o=nt(e),s=q(e,"P",{class:!0});var u=J(s);a=vt(u,h),u.forEach(w),l=nt(e),e.forEach(w),this.h()},h(){var t;C(r,"class","w-full text-left truncate"),C(s,"class","w-full text-right"),C(n,"class","flex justify-between w-full odd:bg-base-200/40 hover:bg-base-200"),Fr(n,"bg-negative",e[13].error),Fr(n,"bg-warning",null==(t=e[13].opts)?void 0:t.noResolve),this.first=n},m(t,e){L(t,n,e),W(n,r),W(r,i),W(n,o),W(n,s),W(s,a),W(n,l),c||(u=Pt(n,"click",f),c=!0)},p(t,r){var o;e=t,4&r&&d!==(d=e[13].id+"")&&Re(i,d),4&r&&h!==(h=e[13].hash+"")&&Re(a,h),4&r&&Fr(n,"bg-negative",e[13].error),4&r&&Fr(n,"bg-warning",null==(o=e[13].opts)?void 0:o.noResolve)},d(t){t&&w(n),c=!1,u()}}}function fh(t){let e,n;return e=new I_({props:{query:t[1]}}),e.$on("close",t[9]),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};2&n&&(r.query=t[1]),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function tO(t){let e,n,r,i,o=[],s=new Map,a=Yc.isQuery(t[1]),l=he(t[2].entries());const c=t=>t[12];for(let e=0;e<l.length;e+=1){let n=lh(t,l,e),r=c(n);s.set(r,o[e]=uh(r,n))}let u=a&&fh(t);return{c(){e=Y("section");for(let t=0;t<o.length;t+=1)o[t].c();n=rt(),u&&u.c(),r=ut(),this.h()},l(t){e=q(t,"SECTION",{class:!0});var i=J(e);for(let t=0;t<o.length;t+=1)o[t].l(i);i.forEach(w),n=nt(t),u&&u.l(t),r=ut(),this.h()},h(){C(e,"class","")},m(t,s){L(t,e,s);for(let t=0;t<o.length;t+=1)o[t]&&o[t].m(e,null);L(t,n,s),u&&u.m(t,s),L(t,r,s),i=!0},p(t,n){6&n&&(l=he(t[2].entries()),o=Eh(o,n,c,1,t,l,s,e,S_,uh,null,lh)),2&n&&(a=Yc.isQuery(t[1])),a?u?(u.p(t,n),2&n&&I(u,1)):(u=fh(t),u.c(),I(u,1),u.m(r.parentNode,r)):u&&(jt(),E(u,1,1,(()=>{u=null})),Lt())},i(t){i||(I(u),i=!0)},o(t){E(u),i=!1},d(t){t&&(w(e),w(n),w(r));for(let t=0;t<o.length;t+=1)o[t].d();u&&u.d(t)}}}function eO(t){let e,n;return e=new RS({props:{history:t[4]}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p:Xt,i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function nO(t){let e,n;return e=new $I({props:{history:t[4]}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p:Xt,i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function rO(t){let e,n,r,i,o,s;return e=new Xa({props:{title:"Inspect Queries",compact:!0,$$slots:{default:[tO]},$$scope:{ctx:t}}}),r=new Xa({props:{title:"Inspect Inputs",compact:!0,$$slots:{default:[eO]},$$scope:{ctx:t}}}),o=new Xa({props:{title:"View Input History",compact:!0,$$slots:{default:[nO]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment),n=rt(),lt(r.$$.fragment),i=rt(),lt(o.$$.fragment)},l(t){at(e.$$.fragment,t),n=nt(t),at(r.$$.fragment,t),i=nt(t),at(o.$$.fragment,t)},m(t,a){st(e,t,a),L(t,n,a),st(r,t,a),L(t,i,a),st(o,t,a),s=!0},p(t,n){const i={};2054&n&&(i.$$scope={dirty:n,ctx:t}),e.$set(i);const s={};2048&n&&(s.$$scope={dirty:n,ctx:t}),r.$set(s);const a={};2048&n&&(a.$$scope={dirty:n,ctx:t}),o.$set(a)},i(t){s||(I(e.$$.fragment,t),I(r.$$.fragment,t),I(o.$$.fragment,t),s=!0)},o(t){E(e.$$.fragment,t),E(r.$$.fragment,t),E(o.$$.fragment,t),s=!1},d(t){t&&(w(n),w(i)),ot(e,t),ot(r,t),ot(o,t)}}}function iO(t){let e,n,r,i,o,s;return n=new Pn({props:{src:Il,class:"w-4 h-4"}}),{c(){e=Y("button"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"BUTTON",{class:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"class","fixed right-4 top-16 rounded-full bg-info text-info-content w-8 h-8 flex items-center justify-center hover:brightness-110 z-0")},m(r,a){L(r,e,a),st(n,e,null),i=!0,o||(s=Pt(e,"click",t[10]),o=!0)},p:Xt,i(t){i||(I(n.$$.fragment,t),t&&En((()=>{i&&(r||(r=Dn(e,Sl,{axis:"x"},!0)),r.run(1))})),i=!0)},o(t){E(n.$$.fragment,t),t&&(r||(r=Dn(e,Sl,{axis:"x"},!1)),r.run(0)),i=!1},d(t){t&&w(e),ot(n),t&&r&&r.end(),o=!1,s()}}}function oO(t){let e,n,r,i=qg(),o=t[0]&&ch(t),s=i&&iO(t);const a=t[6].default,l=ge(a,t,t[11],null);return{c(){o&&o.c(),e=rt(),s&&s.c(),n=rt(),l&&l.c()},l(t){o&&o.l(t),e=nt(t),s&&s.l(t),n=nt(t),l&&l.l(t)},m(t,i){o&&o.m(t,i),L(t,e,i),s&&s.m(t,i),L(t,n,i),l&&l.m(t,i),r=!0},p(t,[n]){t[0]?o?(o.p(t,n),1&n&&I(o,1)):(o=ch(t),o.c(),I(o,1),o.m(e.parentNode,e)):o&&(jt(),E(o,1,1,(()=>{o=null})),Lt()),i&&s.p(t,n),l&&l.p&&(!r||2048&n)&&_e(l,a,t,t[11],r?ye(a,t[11],n,null):be(t[11]),null)},i(t){r||(I(o),I(s),I(l,t),r=!0)},o(t){E(o),E(s),E(l,t),r=!1},d(t){t&&(w(e),w(n)),o&&o.d(t),s&&s.d(t),l&&l.d(t)}}}function sO(t,e,n){let r,i;Te(t,w_,(t=>n(2,i=t)));let{$$slots:o={},$$scope:s}=e;Yg(Ke({}));let a,l=!1;k_((()=>{})),Cr((()=>{const t=t=>{"Escape"===t.key&&(n(0,l=!1),t.stopPropagation()),"e"===t.key.toLowerCase()&&t.shiftKey&&(t.ctrlKey||t.metaKey)&&(n(0,l=!0),t.stopPropagation())};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)}));const c=Sh();Te(t,c,(t=>n(5,r=t)));const u=new A0;return t.$$set=t=>{"$$scope"in t&&n(11,s=t.$$scope)},t.$$.update=()=>{32&t.$$.dirty&&u.push(r)},[l,a,i,c,u,r,o,()=>n(0,l=!l),t=>n(1,a=t),()=>n(1,a=null),()=>n(0,l=!l),s]}class aO extends re{constructor(t){super(),ie(this,t,sO,oO,Zt,{})}}function lO(t){let e;const n=t[0].default,r=ge(n,t,t[1],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||2&i)&&_e(r,n,t,t[1],e?ye(n,t[1],i,null):be(t[1]),null)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function cO(t){let e,n;return e=new aO({props:{$$slots:{default:[uO]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,n){const r={};2&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function uO(t){let e;const n=t[0].default,r=ge(n,t,t[1],null);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||2&i)&&_e(r,n,t,t[1],e?ye(n,t[1],i,null):be(t[1]),null)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function fO(t){let e,n,r,i;const o=[cO,lO],s=[];return e=1,n=s[1]=o[1](t),{c(){n.c(),r=ut()},l(t){n.l(t),r=ut()},m(t,e){s[1].m(t,e),L(t,r,e),i=!0},p(t,[e]){n.p(t,e)},i(t){i||(I(n),i=!0)},o(t){E(n),i=!1},d(t){t&&w(r),s[1].d(t)}}}function dO(t,e,n){let{$$slots:r={},$$scope:i}=e;return t.$$set=t=>{"$$scope"in t&&n(1,i=t.$$scope)},[r,i]}class hO extends re{constructor(t){super(),ie(this,t,dO,fO,Zt,{})}}const mO=t=>({}),dh=t=>({});function hh(t){let e,n,r;function i(e){t[41](e)}let o={title:t[0],logo:t[1],lightLogo:t[2],darkLogo:t[3],neverShowQueries:t[4],fullWidth:t[21],maxWidth:t[20],hideSidebar:t[5],githubRepo:t[8],slackCommunity:t[11],xProfile:t[9],blueskyProfile:t[10],algolia:t[7],sidebarFrontMatter:t[14]};return void 0!==t[17]&&(o.mobileSidebarOpen=t[17]),e=new S2({props:o}),kn.push((()=>kh(e,"mobileSidebarOpen",i))),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,n){st(e,t,n),r=!0},p(t,r){const i={};1&r[0]&&(i.title=t[0]),2&r[0]&&(i.logo=t[1]),4&r[0]&&(i.lightLogo=t[2]),8&r[0]&&(i.darkLogo=t[3]),16&r[0]&&(i.neverShowQueries=t[4]),2097152&r[0]&&(i.fullWidth=t[21]),1048576&r[0]&&(i.maxWidth=t[20]),32&r[0]&&(i.hideSidebar=t[5]),256&r[0]&&(i.githubRepo=t[8]),2048&r[0]&&(i.slackCommunity=t[11]),512&r[0]&&(i.xProfile=t[9]),1024&r[0]&&(i.blueskyProfile=t[10]),128&r[0]&&(i.algolia=t[7]),16384&r[0]&&(i.sidebarFrontMatter=t[14]),!n&&131072&r[0]&&(n=!0,i.mobileSidebarOpen=t[17],Oh((()=>n=!1))),e.$set(i)},i(t){r||(I(e.$$.fragment,t),r=!0)},o(t){E(e.$$.fragment,t),r=!1},d(t){ot(e,t)}}}function mh(t){let e,n,r,i;function o(e){t[42](e)}let s={fileTree:t[24],title:t[0],logo:t[1],homePageName:t[12],builtWithEvidence:t[6],hideHeader:t[19],sidebarFrontMatter:t[14],sidebarDepth:t[13]};return void 0!==t[17]&&(s.mobileSidebarOpen=t[17]),n=new _S({props:s}),kn.push((()=>kh(n,"mobileSidebarOpen",o))),{c(){e=Y("div"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"class","print:hidden")},m(t,r){L(t,e,r),st(n,e,null),i=!0},p(t,e){const i={};1&e[0]&&(i.title=t[0]),2&e[0]&&(i.logo=t[1]),4096&e[0]&&(i.homePageName=t[12]),64&e[0]&&(i.builtWithEvidence=t[6]),524288&e[0]&&(i.hideHeader=t[19]),16384&e[0]&&(i.sidebarFrontMatter=t[14]),8192&e[0]&&(i.sidebarDepth=t[13]),!r&&131072&e[0]&&(r=!0,i.mobileSidebarOpen=t[17],Oh((()=>r=!1))),n.$set(i)},i(t){i||(I(n.$$.fragment,t),i=!0)},o(t){E(n.$$.fragment,t),i=!1},d(t){t&&w(e),ot(n)}}}function ph(t){let e,n,r="/settings"!==t[15].route.id&&gh(t);return{c(){e=Y("div"),r&&r.c(),this.h()},l(t){e=q(t,"DIV",{class:!0});var n=J(e);r&&r.l(n),n.forEach(w),this.h()},h(){C(e,"class","print:hidden")},m(t,i){L(t,e,i),r&&r.m(e,null),n=!0},p(t,n){"/settings"!==t[15].route.id?r?(r.p(t,n),32768&n[0]&&I(r,1)):(r=gh(t),r.c(),I(r,1),r.m(e,null)):r&&(jt(),E(r,1,1,(()=>{r=null})),Lt())},i(t){n||(I(r),n=!0)},o(t){E(r),n=!1},d(t){t&&w(e),r&&r.d()}}}function gh(t){let e,n;return e=new MS({props:{fileTree:t[24]}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p:Xt,i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function pO(t){let e,n;return e=new D2({}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p:Xt,i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function gO(t){let e,n;const r=t[40].content,i=ge(r,t,t[43],dh);return{c(){e=Y("article"),i&&i.c(),this.h()},l(t){e=q(t,"ARTICLE",{id:!0,class:!0});var n=J(e);i&&i.l(n),n.forEach(w),this.h()},h(){C(e,"id","evidence-main-article"),C(e,"class","select-text markdown pb-10")},m(t,r){L(t,e,r),i&&i.m(e,null),n=!0},p(t,e){i&&i.p&&(!n||4096&e[1])&&_e(i,r,t,t[43],n?ye(r,t[43],e,mO):be(t[43]),dh)},i(t){n||(I(i,t),n=!0)},o(t){E(i,t),n=!1},d(t){t&&w(e),i&&i.d(t)}}}function _h(t){let e,n,r;return n=new IS({props:{hideHeader:t[19]}}),{c(){e=Y("div"),lt(n.$$.fragment),this.h()},l(t){e=q(t,"DIV",{class:!0});var r=J(e);at(n.$$.fragment,r),r.forEach(w),this.h()},h(){C(e,"class","print:hidden")},m(t,i){L(t,e,i),st(n,e,null),r=!0},p(t,e){const r={};524288&e[0]&&(r.hideHeader=t[19]),n.$set(r)},i(t){r||(I(n.$$.fragment,t),r=!0)},o(t){E(n.$$.fragment,t),r=!1},d(t){t&&w(e),ot(n)}}}function bh(t){let e,n;return e=new O2({}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function _O(t){let e,n,r,i,o,s,a,l,c,u,d,h,f,p,m,b,y=!t[16]&&Ol;n=new FS({});let g=!t[19]&&hh(t),v=!t[5]&&"never"!==t[14]&&"/settings"!==t[15].route.id&&mh(t),_=!t[22]&&"/settings"!==t[15].route.id&&ph(t);const S=[gO,pO],O=[];function $(t,e){return t[16]?1:0}c=$(t),u=O[c]=S[c](t);let x=!t[18]&&"/settings"!==t[15].route.id&&_h(t),k=y&&bh();return{c(){e=Y("div"),lt(n.$$.fragment),r=rt(),g&&g.c(),i=rt(),o=Y("div"),v&&v.c(),s=rt(),a=Y("main"),_&&_.c(),l=rt(),u.c(),h=rt(),x&&x.c(),p=rt(),k&&k.c(),m=ut(),this.h()},l(t){e=q(t,"DIV",{"data-sveltekit-preload-data":!0,class:!0});var c=J(e);at(n.$$.fragment,c),r=nt(c),g&&g.l(c),i=nt(c),o=q(c,"DIV",{class:!0,style:!0});var d=J(o);v&&v.l(d),s=nt(d),a=q(d,"MAIN",{class:!0});var f=J(a);_&&_.l(f),l=nt(f),u.l(f),f.forEach(w),h=nt(d),x&&x.l(d),d.forEach(w),c.forEach(w),p=nt(t),k&&k.l(t),m=ut(),this.h()},h(){C(a,"class",d=("/settings"===t[15].route.id?"w-full mt-16 sm:mt-20 ":(t[5]||["hide","never"].includes(t[14])?"":"md:pl-8 ")+(t[18]?"":"md:pr-8 ")+(t[19]?t[22]?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":t[22]?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8"),C(o,"class",f=(t[21]?"max-w-full ":t[20]?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start"),Gs(o,"max-width",t[20]+"px"),C(e,"data-sveltekit-preload-data",t[23]),C(e,"class","antialiased")},m(t,u){L(t,e,u),st(n,e,null),W(e,r),g&&g.m(e,null),W(e,i),W(e,o),v&&v.m(o,null),W(o,s),W(o,a),_&&_.m(a,null),W(a,l),O[c].m(a,null),W(o,h),x&&x.m(o,null),L(t,p,u),k&&k.m(t,u),L(t,m,u),b=!0},p(t,n){t[19]?g&&(jt(),E(g,1,1,(()=>{g=null})),Lt()):g?(g.p(t,n),524288&n[0]&&I(g,1)):(g=hh(t),g.c(),I(g,1),g.m(e,i)),t[5]||"never"===t[14]||"/settings"===t[15].route.id?v&&(jt(),E(v,1,1,(()=>{v=null})),Lt()):v?(v.p(t,n),49184&n[0]&&I(v,1)):(v=mh(t),v.c(),I(v,1),v.m(o,s)),t[22]||"/settings"===t[15].route.id?_&&(jt(),E(_,1,1,(()=>{_=null})),Lt()):_?(_.p(t,n),4227072&n[0]&&I(_,1)):(_=ph(t),_.c(),I(_,1),_.m(a,l));let r=c;c=$(t),c===r?O[c].p(t,n):(jt(),E(O[r],1,1,(()=>{O[r]=null})),Lt(),u=O[c],u?u.p(t,n):(u=O[c]=S[c](t),u.c()),I(u,1),u.m(a,null)),(!b||5029920&n[0]&&d!==(d=("/settings"===t[15].route.id?"w-full mt-16 sm:mt-20 ":(t[5]||["hide","never"].includes(t[14])?"":"md:pl-8 ")+(t[18]?"":"md:pr-8 ")+(t[19]?t[22]?" mt-4 sm:mt-[26px] ":" mt-4 sm:mt-8 ":t[22]?" mt-16 sm:mt-[74px] ":" mt-16 sm:mt-20 "))+"flex-grow overflow-x-hidden print:px-0 print:mt-8"))&&C(a,"class",d),t[18]||"/settings"===t[15].route.id?x&&(jt(),E(x,1,1,(()=>{x=null})),Lt()):x?(x.p(t,n),294912&n[0]&&I(x,1)):(x=_h(t),x.c(),I(x,1),x.m(o,null)),(!b||3145728&n[0]&&f!==(f=(t[21]?"max-w-full ":t[20]?"":" max-w-7xl ")+"print:w-[650px] print:md:w-[841px] mx-auto print:md:px-0 print:px-0 px-6 sm:px-8 md:px-12 flex justify-start"))&&C(o,"class",f),(!b||1048576&n[0])&&Gs(o,"max-width",t[20]+"px"),98304&n[0]&&(y=!t[16]&&Ol),y?k?98304&n[0]&&I(k,1):(k=bh(),k.c(),I(k,1),k.m(m.parentNode,m)):k&&(jt(),E(k,1,1,(()=>{k=null})),Lt())},i(t){b||(I(n.$$.fragment,t),I(g),I(v),I(_),I(u),I(x),I(k),b=!0)},o(t){E(n.$$.fragment,t),E(g),E(v),E(_),E(u),E(x),E(k),b=!1},d(t){t&&(w(e),w(p),w(m)),ot(n),g&&g.d(),v&&v.d(),_&&_.d(),O[c].d(),x&&x.d(),k&&k.d(t)}}}function bO(t){let e,n,r,i,o;const s=t[40].default,a=ge(s,t,t[43],null);return n=new V0({}),i=new hO({props:{$$slots:{default:[_O]},$$scope:{ctx:t}}}),{c(){a&&a.c(),e=rt(),lt(n.$$.fragment),r=rt(),lt(i.$$.fragment)},l(t){a&&a.l(t),e=nt(t),at(n.$$.fragment,t),r=nt(t),at(i.$$.fragment,t)},m(t,s){a&&a.m(t,s),L(t,e,s),st(n,t,s),L(t,r,s),st(i,t,s),o=!0},p(t,e){a&&a.p&&(!o||4096&e[1])&&_e(a,s,t,t[43],o?ye(s,t[43],e,null):be(t[43]),null);const n={};8388607&e[0]|4096&e[1]&&(n.$$scope={dirty:e,ctx:t}),i.$set(n)},i(t){o||(I(a,t),I(n.$$.fragment,t),I(i.$$.fragment,t),o=!0)},o(t){E(a,t),E(n.$$.fragment,t),E(i.$$.fragment,t),o=!1},d(t){t&&(w(e),w(r)),a&&a.d(t),ot(n,t),ot(i,t)}}}function yO(t){const e=new Map;return function t(n,r=""){const i=n.href||r;n.isPage&&e.set(decodeURI(i),n),n.children&&Object.entries(n.children).forEach((([e,n])=>{t(n,`${i}/${e}`)}))}(t),e}function vO(t,e,n){let r,i,o,s,a,l,c,u,d,h,f,p,m,b,y,g,v;Te(t,Na,(t=>n(15,g=t))),Te(t,Th,(t=>n(16,v=t)));let{$$slots:_={},$$scope:w}=e;{const t=document.getElementById("__evidence_project_splash");null==t||t.remove()}let{data:S}=e,{title:I}=e,{logo:O}=e,{lightLogo:$}=e,{darkLogo:x}=e,{neverShowQueries:E=!1}=e,{fullWidth:k=!1}=e,{hideSidebar:A=!1}=e,{builtWithEvidence:D=!0}=e,{algolia:T}=e,{githubRepo:C}=e,{xProfile:L}=e,{blueskyProfile:M}=e,{slackCommunity:P}=e,{maxWidth:B}=e,{homePageName:N="Home"}=e,{hideBreadcrumbs:j=!1}=e,{hideHeader:F=!1}=e,{hideTOC:U=!1}=e,{sidebarDepth:R=3}=e,V=!1,W=null==S?void 0:S.pagesManifest;Cr((async()=>{if(!("serviceWorker"in navigator))return;const t=await navigator.serviceWorker.register(Wt("/fix-tprotocol-service-worker.js"),{scope:Wt("/"),type:"classic"});console.debug("[fix-tprotocol-service-worker] Service Worker registered",{registration:t})}));const{syncThemeAttribute:z,cycleAppearance:q,selectedAppearance:Y,setAppearance:H,activeAppearance:J}=Ah();return Te(t,Y,(t=>n(44,b=t))),Te(t,J,(t=>n(45,y=t))),Cr((()=>{const t=t=>{"l"===t.key.toLowerCase()&&t.shiftKey&&(t.ctrlKey||t.metaKey)&&q()};return window.addEventListener("keydown",t),()=>window.removeEventListener("keydown",t)})),Cr((()=>z(document.querySelector("html")))),Cr((()=>{let t;const e=()=>{t=y,"dark"===b&&H("light")},n=()=>{"dark"===t&&H("dark")};return window.addEventListener("beforeprint",e),window.addEventListener("afterprint",n),()=>{window.removeEventListener("beforeprint",e),window.removeEventListener("afterprint",n)}})),t.$$set=t=>{"data"in t&&n(27,S=t.data),"title"in t&&n(0,I=t.title),"logo"in t&&n(1,O=t.logo),"lightLogo"in t&&n(2,$=t.lightLogo),"darkLogo"in t&&n(3,x=t.darkLogo),"neverShowQueries"in t&&n(4,E=t.neverShowQueries),"fullWidth"in t&&n(28,k=t.fullWidth),"hideSidebar"in t&&n(5,A=t.hideSidebar),"builtWithEvidence"in t&&n(6,D=t.builtWithEvidence),"algolia"in t&&n(7,T=t.algolia),"githubRepo"in t&&n(8,C=t.githubRepo),"xProfile"in t&&n(9,L=t.xProfile),"blueskyProfile"in t&&n(10,M=t.blueskyProfile),"slackCommunity"in t&&n(11,P=t.slackCommunity),"maxWidth"in t&&n(29,B=t.maxWidth),"homePageName"in t&&n(12,N=t.homePageName),"hideBreadcrumbs"in t&&n(30,j=t.hideBreadcrumbs),"hideHeader"in t&&n(31,F=t.hideHeader),"hideTOC"in t&&n(32,U=t.hideTOC),"sidebarDepth"in t&&n(13,R=t.sidebarDepth),"$$scope"in t&&n(43,w=t.$$scope)},t.$$.update=()=>{var e;65536&t.$$.dirty[0]&&v&&n(17,V=!1),32768&t.$$.dirty[0]|256&t.$$.dirty[1]&&n(34,i=null==(e=r.get(g.route.id))?void 0:e.frontMatter),8&t.$$.dirty[1]&&n(14,o=null==i?void 0:i.sidebar),16384&t.$$.dirty[0]&&(["show","hide","never"].includes(o)||n(14,o=void 0)),8&t.$$.dirty[1]&&n(38,s=null==i?void 0:i.hide_breadcrumbs),1073741824&t.$$.dirty[0]|128&t.$$.dirty[1]&&n(22,a=s??j),8&t.$$.dirty[1]&&n(37,l=null==i?void 0:i.full_width),268435456&t.$$.dirty[0]|64&t.$$.dirty[1]&&n(21,c=l??k),8&t.$$.dirty[1]&&n(36,u=null==i?void 0:i.max_width),536870912&t.$$.dirty[0]|32&t.$$.dirty[1]&&n(20,d=u??B),8&t.$$.dirty[1]&&n(35,h=null==i?void 0:i.hide_header),17&t.$$.dirty[1]&&n(19,f=h??F),8&t.$$.dirty[1]&&n(33,p=null==i?void 0:i.hide_toc),6&t.$$.dirty[1]&&n(18,m=p??U)},n(39,r=yO(W)),[I,O,$,x,E,A,D,T,C,L,M,P,N,R,o,g,v,V,m,f,d,c,a,"hover",W,Y,J,S,k,B,j,F,U,p,i,h,u,l,s,r,_,function(t){V=t,n(17,V),n(16,v)},function(t){V=t,n(17,V),n(16,v)},w]}class wO extends re{constructor(t){super(),ie(this,t,vO,bO,Zt,{data:27,title:0,logo:1,lightLogo:2,darkLogo:3,neverShowQueries:4,fullWidth:28,hideSidebar:5,builtWithEvidence:6,algolia:7,githubRepo:8,xProfile:9,blueskyProfile:10,slackCommunity:11,maxWidth:29,homePageName:12,hideBreadcrumbs:30,hideHeader:31,hideTOC:32,sidebarDepth:13},null,[-1,-1])}}const SO=t=>({}),yh=t=>({slot:"content"});function IO(t){let e;const n=t[1].default,r=ge(n,t,t[2],yh);return{c(){r&&r.c()},l(t){r&&r.l(t)},m(t,n){r&&r.m(t,n),e=!0},p(t,i){r&&r.p&&(!e||4&i)&&_e(r,n,t,t[2],e?ye(n,t[2],i,SO):be(t[2]),yh)},i(t){e||(I(r,t),e=!0)},o(t){E(r,t),e=!1},d(t){r&&r.d(t)}}}function OO(t){let e,n;return e=new wO({props:{data:t[0],$$slots:{content:[IO]},$$scope:{ctx:t}}}),{c(){lt(e.$$.fragment)},l(t){at(e.$$.fragment,t)},m(t,r){st(e,t,r),n=!0},p(t,[n]){const r={};1&n&&(r.data=t[0]),4&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){n||(I(e.$$.fragment,t),n=!0)},o(t){E(e.$$.fragment,t),n=!1},d(t){ot(e,t)}}}function kO(t,e,n){let{$$slots:r={},$$scope:i}=e,{data:o}=e;return t.$$set=t=>{"data"in t&&n(0,o=t.data),"$$scope"in t&&n(2,i=t.$$scope)},[o,r,i]}class VO extends re{constructor(t){super(),ie(this,t,kO,OO,Zt,{data:0})}}export{VO as component,RO as universal};