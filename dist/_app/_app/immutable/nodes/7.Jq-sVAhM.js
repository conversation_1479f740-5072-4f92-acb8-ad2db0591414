import{v as re,W as hl,R as _n,T as pl,E as Mn,a5 as Vn,s as _e,c as Oe,u as Le,g as Pe,a as De,t as qe,d as p,i as j,r as fe,a6 as Se,a7 as ae,a8 as Ye,G as Je,p as ke,a9 as Ie,H as Qe,l as ce,h as k,j as M,m as w,o as Ke,J as Ee,w as ye,x as ie,y as le,k as q,n as B,b as E,K as ln,ab as Ue,z as we,P as tt,e as g,q as oe,ac as ki,ad as jn,ae as at,af as St,ag as wi,ah as Ci,ai as ns,aj as en,ak as tn,al as Ti,am as Ei}from"../chunks/scheduler.D0cbHTIG.js";import{n as gl,l as _l,k as bl,o as vl,S as be,i as ve,t as P,a as T,g as pe,c as ge,f as nt,h as Si,j as Ae,d as J,m as z,b as X,e as Z,p as Ge}from"../chunks/index.YnsWT1Qn.js";import{m as rt,J as yl,X as Ii,Y as kl,x as et,B as ut,w as ss,v as Nn,t as An,q as Ni,y as Ai,r as wl,Z as Cl,A as qt,H as me,_ as is,$ as bn,a0 as Tl,a1 as ls,a2 as El,a3 as Sl,a4 as Il,a5 as Nl,a6 as Al,n as vn,z as Ht,a7 as Ol,a8 as Xt,G as On,F as Ll,a9 as Pl,aa as Dl,ab as Ml,ac as jt,ad as Vl,ae as jl,af as Fl,ag as ql,k as yn,M as Oi,K as Li,L as Pi,ah as Bl,O as rn,N as Ve,R as Ut,Q as ot,I as je,ai as Hl,aj as Rl,ak as Kl,al as Ul,am as Gl,an as Xe,ao as _t,ap as Di,aq as It,ar as Mi,as as Vi,at as Be,au as Et,av as Fn,aw as qn,ax as Bn,ay as Yl,az as bt,aA as Wl,aB as Jl,aC as Zt,aD as on,aE as rs,aF as zl,aG as os,aH as as,aI as nn,aJ as Xl,aK as Zl,aL as Hn,aM as Ql,aN as $l,aO as ft,aP as Rt,aQ as Qt,aR as Nt,aS as cs,aT as xl,aU as er,aV as Rn,aW as an,aX as tr,aY as nr,aZ as ji,a_ as Ct,a$ as Fi,b0 as sr,b1 as ir,b2 as sn,b3 as qi,b4 as lr,b5 as rr,b6 as or,b7 as ar,b8 as cr,b9 as ur,d as dt,e as Ne,ba as fr,bb as vt,bc as Kn,h as Gt,bd as dr,u as Un,o as mr,be as At,bf as Ot,bg as hr,bh as pr,bi as kn}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CjNovnpU.js";import{w as Bt,d as pt,r as gr,i as _r,a as br,p as vr}from"../chunks/entry.sFL81Vq-.js";import{S as yr,a as Ln,c as kr,B as Yt}from"../chunks/inferColumnTypes.Bz8pcAS_.js";import{c as wr,a as Cr,r as Tr,b as Er,s as st}from"../chunks/index.DmPYR7xL.js";import{B as We}from"../chunks/Button.BvUdNbiN.js";import{b as Sr,A as Gn,a as Tt}from"../chunks/AccordionItem.cc1KqoI7.js";function Ir(e,t,n,s){if(!t)return re;const o=e.getBoundingClientRect();if(t.left===o.left&&t.right===o.right&&t.top===o.top&&t.bottom===o.bottom)return re;const{delay:r=0,duration:a=300,easing:i=hl,start:l=gl()+r,end:c=l+a,tick:u=re,css:d}=n(e,{from:t,to:o},s);let f,p=!0,m=!1;function h(){d&&bl(e,f),p=!1}return _l((e=>{if(!m&&e>=l&&(m=!0),m&&e>=c&&(u(1,0),h()),!p)return!1;if(m){const t=0+1*i((e-l)/a);u(t,1-t)}return!0})),d&&(f=vl(e,0,1,a,r,i,d)),r||(m=!0),u(0,1),h}function Nr(e){const t=getComputedStyle(e);if("absolute"!==t.position&&"fixed"!==t.position){const{width:n,height:s}=t,o=e.getBoundingClientRect();e.style.position="absolute",e.style.width=n,e.style.height=s,Ar(e,o)}}function Ar(e,t){const n=e.getBoundingClientRect();if(t.left!==n.left||t.top!==n.top){const s=getComputedStyle(e),o="none"===s.transform?"":s.transform;e.style.transform=`${o} translate(${t.left-n.left}px, ${t.top-n.top}px)`}}function wn(e){return Array.from(e.querySelectorAll('[role="option"]:not([data-disabled])')).filter((e=>rt(e)))}function Or(e){return t=>{const n=t.target,s=yl(e);if(!s||!Ii(n))return!1;const o=s.id;return!!(kl(n)&&o===n.htmlFor||n.closest(`label[for="${o}"]`))}}function Lr(){return{elements:{root:et("label",{action:e=>({destroy:ut(e,"mousedown",(e=>{!e.defaultPrevented&&e.detail>1&&e.preventDefault()}))})})}}}const Pr=[me.ARROW_LEFT,me.ESCAPE,me.ARROW_RIGHT,me.SHIFT,me.CAPS_LOCK,me.CONTROL,me.ALT,me.META,me.ENTER,me.F1,me.F2,me.F3,me.F4,me.F5,me.F6,me.F7,me.F8,me.F9,me.F10,me.F11,me.F12],Dr={positioning:{placement:"bottom",sameWidth:!0},scrollAlignment:"nearest",loop:!0,defaultOpen:!1,closeOnOutsideClick:!0,preventScroll:!0,closeOnEscape:!0,forceVisible:!1,portal:void 0,builder:"listbox",disabled:!1,required:!1,name:void 0,typeahead:!0,highlightOnHover:!0,onOutsideClick:void 0},Mr=["trigger","menu","label"];function Vr(e){const t={...Dr,...e},n=ss(Bt(null)),s=ss(Bt(null)),o=t.selected??Bt(t.defaultSelected),r=Nn(o,null==t?void 0:t.onSelectedChange),a=pt(s,(e=>e?N(e):void 0)),i=t.open??Bt(t.defaultOpen),l=Nn(i,null==t?void 0:t.onOpenChange),c=An({...Ni(t,"open","defaultOpen","builder","ids"),multiple:t.multiple??!1}),{scrollAlignment:u,loop:d,closeOnOutsideClick:f,closeOnEscape:p,preventScroll:m,portal:h,forceVisible:g,positioning:$,multiple:v,arrowSize:b,disabled:y,required:w,typeahead:k,name:E,highlightOnHover:x,onOutsideClick:T}=c,{name:P,selector:I}=Ai(t.builder),S=An({...wl(Mr),...t.ids}),{handleTypeaheadSearch:C}=wr({onMatch:e=>{s.set(e),e.scrollIntoView({block:u.get()})},getCurrentItem:()=>s.get()});function N(e){const t=e.getAttribute("data-value"),n=e.getAttribute("data-label"),s=e.hasAttribute("data-disabled");return{value:t&&JSON.parse(t),label:n??e.textContent??void 0,disabled:!!s}}function A(e){(e=>{r.update((t=>{if(v.get()){const n=Array.isArray(t)?[...t]:[];return Ml(e,n,((e,t)=>jt(e.value,t.value)))}return e}))})(N(e))}async function M(){l.set(!0);const e=document.getElementById(S.trigger.get());if(!e)return;e!==n.get()&&n.set(e),await _n();const t=document.getElementById(S.menu.get());if(!rt(t))return;const o=t.querySelector("[aria-selected=true]");rt(o)&&s.set(o)}function O(){l.set(!1),s.set(null)}const j=Cl({open:l,forceVisible:g,activeTrigger:n}),q=pt([r],(([e])=>t=>Array.isArray(e)?e.some((e=>jt(e.value,t))):Vl(t)?jt(null==e?void 0:e.value,jl(t,void 0)):jt(null==e?void 0:e.value,t))),_=pt([a],(([e])=>t=>jt(null==e?void 0:e.value,t))),B=et(P("trigger"),{stores:[l,s,y,S.menu,S.trigger,S.label],returned:([e,n,s,o,r,a])=>({"aria-activedescendant":null==n?void 0:n.id,"aria-autocomplete":"list","aria-controls":o,"aria-expanded":e,"aria-labelledby":a,id:r,role:"combobox",disabled:Ht(s),type:"select"===t.builder?"button":void 0}),action:e=>{const t=Ol(e),n=qt(ut(e,"click",(()=>{e.focus(),l.get()?O():M()})),ut(e,"keydown",(n=>{if(!l.get()){if(Pr.includes(n.key)||n.key===me.TAB||n.key===me.BACKSPACE&&t&&""===e.value||n.key===me.SPACE&&is(e))return;M(),_n().then((()=>{if(r.get())return;const e=document.getElementById(S.menu.get());if(!rt(e))return;const t=Array.from(e.querySelectorAll(`${I("item")}:not([data-disabled]):not([data-hidden])`)).filter((e=>rt(e)));t.length&&(n.key===me.ARROW_DOWN?(s.set(t[0]),t[0].scrollIntoView({block:u.get()})):n.key===me.ARROW_UP&&(s.set(bn(t)),bn(t).scrollIntoView({block:u.get()})))}))}if(n.key!==me.TAB){if(n.key===me.ENTER&&!n.isComposing||n.key===me.SPACE&&is(e)){n.preventDefault();const e=s.get();e&&A(e),v.get()||O()}if(n.key===me.ARROW_UP&&n.altKey&&O(),Tl.includes(n.key)){n.preventDefault();const e=document.getElementById(S.menu.get());if(!rt(e))return;const t=wn(e);if(!t.length)return;const o=t.filter((e=>!ls(e)&&void 0===e.dataset.hidden)),r=s.get(),a=r?o.indexOf(r):-1,i=d.get(),l=u.get();let c;switch(n.key){case me.ARROW_DOWN:c=Nl(o,a,i);break;case me.ARROW_UP:c=Il(o,a,i);break;case me.PAGE_DOWN:c=Sl(o,a,10,i);break;case me.PAGE_UP:c=El(o,a,10,i);break;case me.HOME:c=o[0];break;case me.END:c=bn(o);break;default:return}s.set(c),null==c||c.scrollIntoView({block:l})}else if(k.get()){const e=document.getElementById(S.menu.get());if(!rt(e))return;C(n.key,wn(e))}}else O()})));let o=vn;const a=Al(e,{handler:O,enabled:pt([l,p],(([e,t])=>e&&t))});return a&&a.destroy&&(o=a.destroy),{destroy(){n(),o()}}}}),L=et(P("menu"),{stores:[j,S.menu],returned:([e,t])=>({hidden:!e||void 0,id:t,role:"listbox",style:On({display:e?void 0:"none"})}),action:e=>{let t=vn;const s=qt(Xt([j,h,f,$,n],(([n,s,o,r,a])=>{t(),n&&a&&_n().then((()=>{t();const i=Or(S.trigger.get());t=Fl(e,{anchorElement:a,open:l,options:{floating:r,focusTrap:null,modal:{closeOnInteractOutside:o,onClose:O,open:n,shouldCloseOnInteractOutside:e=>{var t;if(null==(t=T.get())||t(e),e.defaultPrevented)return!1;const n=e.target;return!(!Ii(n)||n===a||a.contains(n)||i(e))}},escapeKeydown:null,portal:ql(e,s)}}).destroy}))})));return{destroy:()=>{s(),t()}}}}),{elements:{root:D}}=Lr(),{action:V}=pl(D),J=et(P("label"),{stores:[S.label,S.trigger],returned:([e,t])=>({id:e,for:t}),action:V}),F=et(P("option"),{stores:[q],returned:([e])=>t=>{const n=e(t.value);return{"data-value":JSON.stringify(t.value),"data-label":t.label,"data-disabled":Ht(t.disabled),"aria-disabled":!!t.disabled||void 0,"aria-selected":n,"data-selected":n?"":void 0,id:Ll(),role:"option"}},action:e=>({destroy:qt(ut(e,"click",(t=>{ls(e)?t.preventDefault():(A(e),v.get()||O())})),Xt(x,(t=>t?qt(ut(e,"mouseover",(()=>{s.set(e)})),ut(e,"mouseleave",(()=>{s.set(null)}))):void 0)))})}),H=et(P("group"),{returned:()=>e=>({role:"group","aria-labelledby":e})}),X=et(P("group-label"),{returned:()=>e=>({id:e})}),z=Pl({value:pt([r],(([e])=>{const t=Array.isArray(e)?e.map((e=>e.value)):null==e?void 0:e.value;return"string"==typeof t?t:JSON.stringify(t)})),name:gr(E),required:w,prefix:t.builder}),K=et(P("arrow"),{stores:b,returned:e=>({"data-arrow":!0,style:On({position:"absolute",width:`var(--arrow-size, ${e}px)`,height:`var(--arrow-size, ${e}px)`})})});return Dl((()=>{if(!yn)return;const e=document.getElementById(S.menu.get()),t=document.getElementById(S.trigger.get());if(t&&n.set(t),!e)return;const s=e.querySelector("[data-selected]");rt(s)})),Xt([s],(([e])=>{if(!yn)return;const t=document.getElementById(S.menu.get());rt(t)&&wn(t).forEach((t=>{t===e?Cr(t):Tr(t)}))})),Xt([l],(([e])=>{if(!yn)return;let t=vn;return m.get()&&e&&(t=Er()),()=>{t()}})),{ids:S,elements:{trigger:B,group:H,option:F,menu:L,groupLabel:X,label:J,hiddenInput:z,arrow:K},states:{open:l,selected:r,highlighted:a,highlightedItem:s},helpers:{isSelected:q,isHighlighted:_,closeMenu:O},options:c}}function jr(e){const t=Vr({...e,builder:"select"}),n=pt(t.states.selected,(e=>Array.isArray(e)?e.map((e=>e.label)).join(", "):(null==e?void 0:e.label)??""));return{...t,elements:{...t.elements},states:{...t.states,selectedLabel:n}}}const Fr={defaultChecked:!1,disabled:!1,required:!1,name:"",value:""},{name:us}=Ai("switch");function qr(e){const t={...Fr,...e},n=An(Ni(t,"checked")),{disabled:s,required:o,name:r,value:a}=n,i=t.checked??Bt(t.defaultChecked),l=Nn(i,null==t?void 0:t.onCheckedChange);function c(){s.get()||l.update((e=>!e))}return{elements:{root:et(us(),{stores:[l,s,o],returned:([e,t,n])=>({"data-disabled":Ht(t),disabled:Ht(t),"data-state":e?"checked":"unchecked",type:"button",role:"switch","aria-checked":e?"true":"false","aria-required":n?"true":void 0}),action:e=>({destroy:qt(ut(e,"click",(()=>{c()})),ut(e,"keydown",(e=>{e.key!==me.ENTER&&e.key!==me.SPACE||(e.preventDefault(),c())})))})}),input:et(us("input"),{stores:[l,r,o,s,a],returned:([e,t,n,s,o])=>({type:"checkbox","aria-hidden":!0,hidden:!0,tabindex:-1,name:t,value:o,checked:e,required:n,disabled:Ht(s),style:On({position:"absolute",opacity:0,"pointer-events":"none",margin:0,transform:"translateX(-100%)"})})})},states:{checked:l},options:n}}function cn(){return{NAME:"select",GROUP_NAME:"select-group",ITEM_NAME:"select-item",PARTS:["arrow","content","group","item","indicator","input","label","trigger","value"]}}function Pt(){const{NAME:e}=cn();return Vn(e)}function Br(e){const{NAME:t,PARTS:n}=cn(),s=Oi(t,n),o={...jr({...Li(e),forceVisible:!0}),getAttrs:s};return Mn(t,o),{...o,updateOption:Pi(o.options)}}function Hr(e){const{ITEM_NAME:t}=cn(),n=Pt();return Mn(t,e),n}function Rr(){const{ITEM_NAME:e}=cn(),{helpers:{isSelected:t},getAttrs:n}=Pt();return{value:Vn(e),isSelected:t,getAttrs:n}}function Kr(e){const t={side:"bottom",align:"center",sameWidth:!0,...e},{options:{positioning:n}}=Pt();Bl(n)(t)}const Ur=e=>({ids:1&e}),fs=e=>({ids:e[0]});function Gr(e){let t;const n=e[19].default,s=Oe(n,e,e[18],fs);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,[o]){s&&s.p&&(!t||262145&o)&&Le(s,n,e,e[18],t?De(n,e[18],o,Ur):Pe(e[18]),fs)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function Yr(e,t,n){let s,{$$slots:o={},$$scope:r}=t,{required:a}=t,{disabled:i}=t,{preventScroll:l}=t,{loop:c}=t,{closeOnEscape:u}=t,{closeOnOutsideClick:d}=t,{portal:f}=t,{name:p}=t,{multiple:m=!1}=t,{selected:h}=t,{onSelectedChange:g}=t,{open:$}=t,{onOpenChange:v}=t,{items:b=[]}=t,{onOutsideClick:y}=t,{typeahead:w}=t;const{states:{open:k,selected:E},updateOption:x,ids:T}=Br({required:a,disabled:i,preventScroll:l,loop:c,closeOnEscape:u,closeOnOutsideClick:d,portal:f,name:p,onOutsideClick:y,multiple:m,forceVisible:!0,defaultSelected:Array.isArray(h)?[...h]:h,defaultOpen:$,onSelectedChange:({next:e})=>Array.isArray(e)?((!Array.isArray(h)||!Sr(h,e))&&(null==g||g(e),n(2,h=e)),e):(h!==e&&(null==g||g(e),n(2,h=e)),e),onOpenChange:({next:e})=>($!==e&&(null==v||v(e),n(3,$=e)),e),items:b,typeahead:w}),P=pt([T.menu,T.trigger,T.label],(([e,t,n])=>({menu:e,trigger:t,label:n})));return qe(e,P,(e=>n(0,s=e))),e.$$set=e=>{"required"in e&&n(4,a=e.required),"disabled"in e&&n(5,i=e.disabled),"preventScroll"in e&&n(6,l=e.preventScroll),"loop"in e&&n(7,c=e.loop),"closeOnEscape"in e&&n(8,u=e.closeOnEscape),"closeOnOutsideClick"in e&&n(9,d=e.closeOnOutsideClick),"portal"in e&&n(10,f=e.portal),"name"in e&&n(11,p=e.name),"multiple"in e&&n(12,m=e.multiple),"selected"in e&&n(2,h=e.selected),"onSelectedChange"in e&&n(13,g=e.onSelectedChange),"open"in e&&n(3,$=e.open),"onOpenChange"in e&&n(14,v=e.onOpenChange),"items"in e&&n(15,b=e.items),"onOutsideClick"in e&&n(16,y=e.onOutsideClick),"typeahead"in e&&n(17,w=e.typeahead),"$$scope"in e&&n(18,r=e.$$scope)},e.$$.update=()=>{8&e.$$.dirty&&void 0!==$&&k.set($),4&e.$$.dirty&&void 0!==h&&E.set(Array.isArray(h)?[...h]:h),16&e.$$.dirty&&x("required",a),32&e.$$.dirty&&x("disabled",i),64&e.$$.dirty&&x("preventScroll",l),128&e.$$.dirty&&x("loop",c),256&e.$$.dirty&&x("closeOnEscape",u),512&e.$$.dirty&&x("closeOnOutsideClick",d),1024&e.$$.dirty&&x("portal",f),2048&e.$$.dirty&&x("name",p),4096&e.$$.dirty&&x("multiple",m),65536&e.$$.dirty&&x("onOutsideClick",y),131072&e.$$.dirty&&x("typeahead",w)},[s,P,h,$,a,i,l,c,u,d,f,p,m,g,v,b,y,w,r,o]}class Wr extends be{constructor(e){super(),ve(this,e,Yr,Gr,_e,{required:4,disabled:5,preventScroll:6,loop:7,closeOnEscape:8,closeOnOutsideClick:9,portal:10,name:11,multiple:12,selected:2,onSelectedChange:13,open:3,onOpenChange:14,items:15,onOutsideClick:16,typeahead:17})}}const Jr=e=>({builder:256&e[0]}),ds=e=>({builder:e[8]}),zr=e=>({builder:256&e[0]}),ms=e=>({builder:e[8]}),Xr=e=>({builder:256&e[0]}),hs=e=>({builder:e[8]}),Zr=e=>({builder:256&e[0]}),ps=e=>({builder:e[8]}),Qr=e=>({builder:256&e[0]}),gs=e=>({builder:e[8]}),$r=e=>({builder:256&e[0]}),_s=e=>({builder:e[8]});function xr(e){let t,n,s,o;const r=e[28].default,a=Oe(r,e,e[27],ds);let i=[e[8],e[13]],l={};for(let e=0;e<i.length;e+=1)l=ae(l,i[e]);return{c(){t=w("div"),a&&a.c(),this.h()},l(e){t=k(e,"DIV",{});var n=M(t);a&&a.l(n),n.forEach(p),this.h()},h(){Ie(t,l)},m(r,i){j(r,t,i),a&&a.m(t,null),e[38](t),n=!0,s||(o=[Qe(e[8].action(t)),ce(t,"m-pointerleave",e[12]),ce(t,"keydown",e[33])],s=!0)},p(e,s){a&&a.p&&(!n||134217984&s[0])&&Le(a,r,e,e[27],n?De(r,e[27],s,Jr):Pe(e[27]),ds),Ie(t,l=Ve(i,[256&s[0]&&e[8],8192&s[0]&&e[13]]))},i(e){n||(T(a,e),n=!0)},o(e){P(a,e),n=!1},d(n){n&&p(t),a&&a.d(n),e[38](null),s=!1,Je(o)}}}function eo(e){let t,n,s,o,r;const a=e[28].default,i=Oe(a,e,e[27],ms);let l=[e[8],e[13]],c={};for(let e=0;e<l.length;e+=1)c=ae(c,l[e]);return{c(){t=w("div"),i&&i.c(),this.h()},l(e){t=k(e,"DIV",{});var n=M(t);i&&i.l(n),n.forEach(p),this.h()},h(){Ie(t,c)},m(n,a){j(n,t,a),i&&i.m(t,null),e[37](t),s=!0,o||(r=[Qe(e[8].action(t)),ce(t,"m-pointerleave",e[12]),ce(t,"keydown",e[32])],o=!0)},p(n,o){e=n,i&&i.p&&(!s||134217984&o[0])&&Le(i,a,e,e[27],s?De(a,e[27],o,zr):Pe(e[27]),ms),Ie(t,c=Ve(l,[256&o[0]&&e[8],8192&o[0]&&e[13]]))},i(e){s||(T(i,e),n&&n.end(1),s=!0)},o(o){P(i,o),o&&(n=Si(t,e[5],e[6])),s=!1},d(s){s&&p(t),i&&i.d(s),e[37](null),s&&n&&n.end(),o=!1,Je(r)}}}function to(e){let t,n,s,o,r;const a=e[28].default,i=Oe(a,e,e[27],hs);let l=[e[8],e[13]],c={};for(let e=0;e<l.length;e+=1)c=ae(c,l[e]);return{c(){t=w("div"),i&&i.c(),this.h()},l(e){t=k(e,"DIV",{});var n=M(t);i&&i.l(n),n.forEach(p),this.h()},h(){Ie(t,c)},m(n,a){j(n,t,a),i&&i.m(t,null),e[36](t),s=!0,o||(r=[Qe(e[8].action(t)),ce(t,"m-pointerleave",e[12]),ce(t,"keydown",e[31])],o=!0)},p(n,o){e=n,i&&i.p&&(!s||134217984&o[0])&&Le(i,a,e,e[27],s?De(a,e[27],o,Xr):Pe(e[27]),hs),Ie(t,c=Ve(l,[256&o[0]&&e[8],8192&o[0]&&e[13]]))},i(o){s||(T(i,o),o&&(n||ke((()=>{n=Ae(t,e[3],e[4]),n.start()}))),s=!0)},o(e){P(i,e),s=!1},d(n){n&&p(t),i&&i.d(n),e[36](null),o=!1,Je(r)}}}function no(e){let t,n,s,o,r,a;const i=e[28].default,l=Oe(i,e,e[27],ps);let c=[e[8],e[13]],u={};for(let e=0;e<c.length;e+=1)u=ae(u,c[e]);return{c(){t=w("div"),l&&l.c(),this.h()},l(e){t=k(e,"DIV",{});var n=M(t);l&&l.l(n),n.forEach(p),this.h()},h(){Ie(t,u)},m(n,s){j(n,t,s),l&&l.m(t,null),e[35](t),o=!0,r||(a=[Qe(e[8].action(t)),ce(t,"m-pointerleave",e[12]),ce(t,"keydown",e[30])],r=!0)},p(n,s){e=n,l&&l.p&&(!o||134217984&s[0])&&Le(l,i,e,e[27],o?De(i,e[27],s,Zr):Pe(e[27]),ps),Ie(t,u=Ve(c,[256&s[0]&&e[8],8192&s[0]&&e[13]]))},i(r){o||(T(l,r),r&&ke((()=>{o&&(s&&s.end(1),n=Ae(t,e[3],e[4]),n.start())})),o=!0)},o(r){P(l,r),n&&n.invalidate(),r&&(s=Si(t,e[5],e[6])),o=!1},d(n){n&&p(t),l&&l.d(n),e[35](null),n&&s&&s.end(),r=!1,Je(a)}}}function so(e){let t,n,s,o,r;const a=e[28].default,i=Oe(a,e,e[27],gs);let l=[e[8],e[13]],c={};for(let e=0;e<l.length;e+=1)c=ae(c,l[e]);return{c(){t=w("div"),i&&i.c(),this.h()},l(e){t=k(e,"DIV",{});var n=M(t);i&&i.l(n),n.forEach(p),this.h()},h(){Ie(t,c)},m(n,a){j(n,t,a),i&&i.m(t,null),e[34](t),s=!0,o||(r=[Qe(e[8].action(t)),ce(t,"m-pointerleave",e[12]),ce(t,"keydown",e[29])],o=!0)},p(n,o){e=n,i&&i.p&&(!s||134217984&o[0])&&Le(i,a,e,e[27],s?De(a,e[27],o,Qr):Pe(e[27]),gs),Ie(t,c=Ve(l,[256&o[0]&&e[8],8192&o[0]&&e[13]]))},i(o){s||(T(i,o),o&&ke((()=>{s&&(n||(n=nt(t,e[1],e[2],!0)),n.run(1))})),s=!0)},o(o){P(i,o),o&&(n||(n=nt(t,e[1],e[2],!1)),n.run(0)),s=!1},d(s){s&&p(t),i&&i.d(s),e[34](null),s&&n&&n.end(),o=!1,Je(r)}}}function io(e){let t;const n=e[28].default,s=Oe(n,e,e[27],_s);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,o){s&&s.p&&(!t||134217984&o[0])&&Le(s,n,e,e[27],t?De(n,e[27],o,$r):Pe(e[27]),_s)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function lo(e){let t,n,s,o;const r=[io,so,no,to,eo,xr],a=[];function i(e,t){return e[7]&&e[9]?0:e[1]&&e[9]?1:e[3]&&e[5]&&e[9]?2:e[3]&&e[9]?3:e[5]&&e[9]?4:e[9]?5:-1}return~(t=i(e))&&(n=a[t]=r[t](e)),{c(){n&&n.c(),s=fe()},l(e){n&&n.l(e),s=fe()},m(e,n){~t&&a[t].m(e,n),j(e,s,n),o=!0},p(e,o){let l=t;t=i(e),t===l?~t&&a[t].p(e,o):(n&&(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge()),~t?(n=a[t],n?n.p(e,o):(n=a[t]=r[t](e),n.c()),T(n,1),n.m(s.parentNode,s)):n=null)},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),~t&&a[t].d(e)}}}function ro(e,t,n){let s;const o=["transition","transitionConfig","inTransition","inTransitionConfig","outTransition","outTransitionConfig","asChild","id","side","align","sideOffset","alignOffset","collisionPadding","avoidCollisions","collisionBoundary","sameWidth","fitViewport","strategy","overlap","el"];let r,a,i=Se(t,o),{$$slots:l={},$$scope:c}=t,{transition:u}=t,{transitionConfig:d}=t,{inTransition:f}=t,{inTransitionConfig:p}=t,{outTransition:m}=t,{outTransitionConfig:h}=t,{asChild:g=!1}=t,{id:$}=t,{side:v="bottom"}=t,{align:b="center"}=t,{sideOffset:y=0}=t,{alignOffset:w=0}=t,{collisionPadding:k=8}=t,{avoidCollisions:E=!0}=t,{collisionBoundary:x}=t,{sameWidth:T=!0}=t,{fitViewport:P=!1}=t,{strategy:I="absolute"}=t,{overlap:S=!1}=t,{el:C}=t;const{elements:{menu:N},states:{open:A},ids:M,getAttrs:O}=Pt();qe(e,N,(e=>n(26,a=e))),qe(e,A,(e=>n(9,r=e)));const j=rn(),q=O("content");return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(13,i=Se(t,o)),"transition"in e&&n(1,u=e.transition),"transitionConfig"in e&&n(2,d=e.transitionConfig),"inTransition"in e&&n(3,f=e.inTransition),"inTransitionConfig"in e&&n(4,p=e.inTransitionConfig),"outTransition"in e&&n(5,m=e.outTransition),"outTransitionConfig"in e&&n(6,h=e.outTransitionConfig),"asChild"in e&&n(7,g=e.asChild),"id"in e&&n(14,$=e.id),"side"in e&&n(15,v=e.side),"align"in e&&n(16,b=e.align),"sideOffset"in e&&n(17,y=e.sideOffset),"alignOffset"in e&&n(18,w=e.alignOffset),"collisionPadding"in e&&n(19,k=e.collisionPadding),"avoidCollisions"in e&&n(20,E=e.avoidCollisions),"collisionBoundary"in e&&n(21,x=e.collisionBoundary),"sameWidth"in e&&n(22,T=e.sameWidth),"fitViewport"in e&&n(23,P=e.fitViewport),"strategy"in e&&n(24,I=e.strategy),"overlap"in e&&n(25,S=e.overlap),"el"in e&&n(0,C=e.el),"$$scope"in e&&n(27,c=e.$$scope)},e.$$.update=()=>{16384&e.$$.dirty[0]&&$&&M.menu.set($),67108864&e.$$.dirty[0]&&n(8,s=a),256&e.$$.dirty[0]&&Object.assign(s,q),67076608&e.$$.dirty[0]&&r&&Kr({side:v,align:b,sideOffset:y,alignOffset:w,collisionPadding:k,avoidCollisions:E,collisionBoundary:x,sameWidth:T,fitViewport:P,strategy:I,overlap:S})},[C,u,d,f,p,m,h,g,s,r,N,A,j,i,$,v,b,y,w,k,E,x,T,P,I,S,a,c,l,function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},function(e){Ee[e?"unshift":"push"]((()=>{C=e,n(0,C)}))},function(e){Ee[e?"unshift":"push"]((()=>{C=e,n(0,C)}))},function(e){Ee[e?"unshift":"push"]((()=>{C=e,n(0,C)}))},function(e){Ee[e?"unshift":"push"]((()=>{C=e,n(0,C)}))},function(e){Ee[e?"unshift":"push"]((()=>{C=e,n(0,C)}))}]}let oo=class extends be{constructor(e){super(),ve(this,e,ro,lo,_e,{transition:1,transitionConfig:2,inTransition:3,inTransitionConfig:4,outTransition:5,outTransitionConfig:6,asChild:7,id:14,side:15,align:16,sideOffset:17,alignOffset:18,collisionPadding:19,avoidCollisions:20,collisionBoundary:21,sameWidth:22,fitViewport:23,strategy:24,overlap:25,el:0},null,[-1,-1])}};const ao=e=>({builder:4&e}),bs=e=>({builder:e[2]});function co(e){let t,n,s,o=[e[2],e[5]],r={};for(let e=0;e<o.length;e+=1)r=ae(r,o[e]);return{c(){t=w("input"),this.h()},l(e){t=k(e,"INPUT",{}),this.h()},h(){Ie(t,r)},m(o,r){j(o,t,r),t.autofocus&&t.focus(),e[11](t),n||(s=Qe(e[2].action(t)),n=!0)},p(e,n){Ie(t,r=Ve(o,[4&n&&e[2],32&n&&e[5]]))},i:re,o:re,d(o){o&&p(t),e[11](null),n=!1,s()}}}function uo(e){let t;const n=e[10].default,s=Oe(n,e,e[9],bs);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,o){s&&s.p&&(!t||516&o)&&Le(s,n,e,e[9],t?De(n,e[9],o,ao):Pe(e[9]),bs)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function fo(e){let t,n,s,o;const r=[uo,co],a=[];function i(e,t){return e[1]?0:1}return t=i(e),n=a[t]=r[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){a[t].m(e,n),j(e,s,n),o=!0},p(e,[o]){let l=t;t=i(e),t===l?a[t].p(e,o):(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge(),n=a[t],n?n.p(e,o):(n=a[t]=r[t](e),n.c()),T(n,1),n.m(s.parentNode,s))},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),a[t].d(e)}}}function mo(e,t,n){let s,o;const r=["asChild","el"];let a,i,l=Se(t,r),{$$slots:c={},$$scope:u}=t,{asChild:d=!1}=t,{el:f}=t;const{elements:{hiddenInput:p},options:{disabled:m},getAttrs:h}=Pt();return qe(e,p,(e=>n(7,a=e))),qe(e,m,(e=>n(8,i=e))),e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(5,l=Se(t,r)),"asChild"in e&&n(1,d=e.asChild),"el"in e&&n(0,f=e.el),"$$scope"in e&&n(9,u=e.$$scope)},e.$$.update=()=>{256&e.$$.dirty&&n(6,s={...h("input"),disabled:!!i||void 0}),128&e.$$.dirty&&n(2,o=a),68&e.$$.dirty&&Object.assign(o,s)},[f,d,o,p,m,l,s,a,i,u,c,function(e){Ee[e?"unshift":"push"]((()=>{f=e,n(0,f)}))}]}class ho extends be{constructor(e){super(),ve(this,e,mo,fo,_e,{asChild:1,el:0})}}const po=e=>({builder:16&e,isSelected:32&e}),vs=e=>({builder:e[4],isSelected:e[5]}),go=e=>({builder:16&e,isSelected:32&e}),ys=e=>({builder:e[4],isSelected:e[5]});function _o(e){let t,n,s,o;const r=e[14].default,a=Oe(r,e,e[13],vs),i=a||vo(e);let l=[e[4],e[9]],c={};for(let e=0;e<l.length;e+=1)c=ae(c,l[e]);return{c(){t=w("div"),i&&i.c(),this.h()},l(e){t=k(e,"DIV",{});var n=M(t);i&&i.l(n),n.forEach(p),this.h()},h(){Ie(t,c)},m(r,a){j(r,t,a),i&&i.m(t,null),e[19](t),n=!0,s||(o=[Qe(e[4].action(t)),ce(t,"m-click",e[8]),ce(t,"m-pointermove",e[8]),ce(t,"focusin",e[15]),ce(t,"keydown",e[16]),ce(t,"focusout",e[17]),ce(t,"pointerleave",e[18])],s=!0)},p(e,s){a?a.p&&(!n||8240&s)&&Le(a,r,e,e[13],n?De(r,e[13],s,po):Pe(e[13]),vs):i&&i.p&&(!n||6&s)&&i.p(e,n?s:-1),Ie(t,c=Ve(l,[16&s&&e[4],512&s&&e[9]]))},i(e){n||(T(i,e),n=!0)},o(e){P(i,e),n=!1},d(n){n&&p(t),i&&i.d(n),e[19](null),s=!1,Je(o)}}}function bo(e){let t;const n=e[14].default,s=Oe(n,e,e[13],ys);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,o){s&&s.p&&(!t||8240&o)&&Le(s,n,e,e[13],t?De(n,e[13],o,go):Pe(e[13]),ys)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function vo(e){let t,n=(e[2]||e[1])+"";return{c(){t=le(n)},l(e){t=ie(e,n)},m(e,n){j(e,t,n)},p(e,s){6&s&&n!==(n=(e[2]||e[1])+"")&&ye(t,n)},d(e){e&&p(t)}}}function yo(e){let t,n,s,o;const r=[bo,_o],a=[];function i(e,t){return e[3]?0:1}return t=i(e),n=a[t]=r[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){a[t].m(e,n),j(e,s,n),o=!0},p(e,[o]){let l=t;t=i(e),t===l?a[t].p(e,o):(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge(),n=a[t],n?n.p(e,o):(n=a[t]=r[t](e),n.c()),T(n,1),n.m(s.parentNode,s))},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),a[t].d(e)}}}function ko(e,t,n){let s,o;const r=["value","disabled","label","asChild","el"];let a,i,l=Se(t,r),{$$slots:c={},$$scope:u}=t,{value:d}=t,{disabled:f}=t,{label:p}=t,{asChild:m=!1}=t,{el:h}=t;const{elements:{option:g},helpers:{isSelected:$},getAttrs:v}=Hr(d);qe(e,g,(e=>n(12,i=e))),qe(e,$,(e=>n(11,a=e)));const b=rn(),y=v("item");return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(9,l=Se(t,r)),"value"in e&&n(1,d=e.value),"disabled"in e&&n(10,f=e.disabled),"label"in e&&n(2,p=e.label),"asChild"in e&&n(3,m=e.asChild),"el"in e&&n(0,h=e.el),"$$scope"in e&&n(13,u=e.$$scope)},e.$$.update=()=>{5126&e.$$.dirty&&n(4,s=i({value:d,disabled:f,label:p})),16&e.$$.dirty&&Object.assign(s,y),2050&e.$$.dirty&&n(5,o=a(d))},[h,d,p,m,s,o,g,$,b,l,f,a,i,u,c,function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},function(e){Ee[e?"unshift":"push"]((()=>{h=e,n(0,h)}))}]}let wo=class extends be{constructor(e){super(),ve(this,e,ko,yo,_e,{value:1,disabled:10,label:2,asChild:3,el:0})}};const Co=e=>({isSelected:4&e}),ks=e=>({attrs:e[5],isSelected:e[2](e[4])}),To=e=>({isSelected:4&e}),ws=e=>({attrs:e[5],isSelected:e[2](e[4])});function Eo(e){let t,n,s=e[2](e[4]),o=s&&Cs(e),r=[e[6],e[5]],a={};for(let e=0;e<r.length;e+=1)a=ae(a,r[e]);return{c(){t=w("div"),o&&o.c(),this.h()},l(e){t=k(e,"DIV",{});var n=M(t);o&&o.l(n),n.forEach(p),this.h()},h(){Ie(t,a)},m(s,r){j(s,t,r),o&&o.m(t,null),e[9](t),n=!0},p(e,n){4&n&&(s=e[2](e[4])),s?o?(o.p(e,n),4&n&&T(o,1)):(o=Cs(e),o.c(),T(o,1),o.m(t,null)):o&&(pe(),P(o,1,1,(()=>{o=null})),ge()),Ie(t,a=Ve(r,[64&n&&e[6],e[5]]))},i(e){n||(T(o),n=!0)},o(e){P(o),n=!1},d(n){n&&p(t),o&&o.d(),e[9](null)}}}function So(e){let t;const n=e[8].default,s=Oe(n,e,e[7],ws);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,o){s&&s.p&&(!t||132&o)&&Le(s,n,e,e[7],t?De(n,e[7],o,To):Pe(e[7]),ws)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function Cs(e){let t;const n=e[8].default,s=Oe(n,e,e[7],ks);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,o){s&&s.p&&(!t||132&o)&&Le(s,n,e,e[7],t?De(n,e[7],o,Co):Pe(e[7]),ks)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function Io(e){let t,n,s,o;const r=[So,Eo],a=[];function i(e,t){return e[1]?0:1}return t=i(e),n=a[t]=r[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){a[t].m(e,n),j(e,s,n),o=!0},p(e,[o]){let l=t;t=i(e),t===l?a[t].p(e,o):(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge(),n=a[t],n?n.p(e,o):(n=a[t]=r[t](e),n.c()),T(n,1),n.m(s.parentNode,s))},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),a[t].d(e)}}}function No(e,t,n){const s=["asChild","el"];let o,r=Se(t,s),{$$slots:a={},$$scope:i}=t,{asChild:l=!1}=t,{el:c}=t;const{isSelected:u,value:d,getAttrs:f}=Rr();qe(e,u,(e=>n(2,o=e)));const p=f("indicator");return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(6,r=Se(t,s)),"asChild"in e&&n(1,l=e.asChild),"el"in e&&n(0,c=e.el),"$$scope"in e&&n(7,i=e.$$scope)},[c,l,o,u,d,p,r,i,a,function(e){Ee[e?"unshift":"push"]((()=>{c=e,n(0,c)}))}]}class Ao extends be{constructor(e){super(),ve(this,e,No,Io,_e,{asChild:1,el:0})}}const Oo=e=>({builder:4&e}),Ts=e=>({builder:e[2]}),Lo=e=>({builder:4&e}),Es=e=>({builder:e[2]});function Po(e){let t,n,s,o;const r=e[9].default,a=Oe(r,e,e[8],Ts);let i=[e[2],{type:"button"},e[5]],l={};for(let e=0;e<i.length;e+=1)l=ae(l,i[e]);return{c(){t=w("button"),a&&a.c(),this.h()},l(e){t=k(e,"BUTTON",{type:!0});var n=M(t);a&&a.l(n),n.forEach(p),this.h()},h(){Ie(t,l)},m(r,i){j(r,t,i),a&&a.m(t,null),t.autofocus&&t.focus(),e[10](t),n=!0,s||(o=[Qe(e[2].action(t)),ce(t,"m-click",e[4]),ce(t,"m-keydown",e[4])],s=!0)},p(e,s){a&&a.p&&(!n||260&s)&&Le(a,r,e,e[8],n?De(r,e[8],s,Oo):Pe(e[8]),Ts),Ie(t,l=Ve(i,[4&s&&e[2],{type:"button"},32&s&&e[5]]))},i(e){n||(T(a,e),n=!0)},o(e){P(a,e),n=!1},d(n){n&&p(t),a&&a.d(n),e[10](null),s=!1,Je(o)}}}function Do(e){let t;const n=e[9].default,s=Oe(n,e,e[8],Es);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,o){s&&s.p&&(!t||260&o)&&Le(s,n,e,e[8],t?De(n,e[8],o,Lo):Pe(e[8]),Es)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function Mo(e){let t,n,s,o;const r=[Do,Po],a=[];function i(e,t){return e[1]?0:1}return t=i(e),n=a[t]=r[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){a[t].m(e,n),j(e,s,n),o=!0},p(e,[o]){let l=t;t=i(e),t===l?a[t].p(e,o):(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge(),n=a[t],n?n.p(e,o):(n=a[t]=r[t](e),n.c()),T(n,1),n.m(s.parentNode,s))},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),a[t].d(e)}}}function Vo(e,t,n){let s;const o=["asChild","id","el"];let r,a=Se(t,o),{$$slots:i={},$$scope:l}=t,{asChild:c=!1}=t,{id:u}=t,{el:d}=t;const{elements:{trigger:f},ids:p,getAttrs:m}=Pt();qe(e,f,(e=>n(7,r=e)));const h=rn(),g=m("trigger");return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(5,a=Se(t,o)),"asChild"in e&&n(1,c=e.asChild),"id"in e&&n(6,u=e.id),"el"in e&&n(0,d=e.el),"$$scope"in e&&n(8,l=e.$$scope)},e.$$.update=()=>{64&e.$$.dirty&&u&&p.trigger.set(u),128&e.$$.dirty&&n(2,s=r),4&e.$$.dirty&&Object.assign(s,g)},[d,c,s,f,h,a,u,r,l,i,function(e){Ee[e?"unshift":"push"]((()=>{d=e,n(0,d)}))}]}let jo=class extends be{constructor(e){super(),ve(this,e,Vo,Mo,_e,{asChild:1,id:6,el:0})}};function Bi(){return{NAME:"switch",PARTS:["root","input","thumb"]}}function Fo(e){const{NAME:t,PARTS:n}=Bi(),s=Oi(t,n),o={...qr(Li(e)),getAttrs:s};return Mn(t,o),{...o,updateOption:Pi(o.options)}}function Hi(){const{NAME:e}=Bi();return Vn(e)}function qo(e){let t,n,s,o=[e[2],{name:e[3]},{disabled:e[4]},{required:e[5]},{value:e[1]},e[11]],r={};for(let e=0;e<o.length;e+=1)r=ae(r,o[e]);return{c(){t=w("input"),this.h()},l(e){t=k(e,"INPUT",{name:!0}),this.h()},h(){Ie(t,r)},m(o,a){j(o,t,a),"value"in r&&(t.value=r.value),t.autofocus&&t.focus(),e[13](t),n||(s=Qe(e[2].action(t)),n=!0)},p(e,[n]){Ie(t,r=Ve(o,[4&n&&e[2],8&n&&{name:e[3]},16&n&&{disabled:e[4]},32&n&&{required:e[5]},2&n&&t.value!==e[1]&&{value:e[1]},2048&n&&e[11]])),"value"in r&&(t.value=r.value)},i:re,o:re,d(o){o&&p(t),e[13](null),n=!1,s()}}}function Bo(e,t,n){let s;const o=["el"];let r,a,i,l,c,u=Se(t,o),{el:d}=t;const{elements:{input:f},options:{value:p,name:m,disabled:h,required:g}}=Hi();return qe(e,f,(e=>n(2,a=e))),qe(e,p,(e=>n(12,r=e))),qe(e,m,(e=>n(3,i=e))),qe(e,h,(e=>n(4,l=e))),qe(e,g,(e=>n(5,c=e))),e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(11,u=Se(t,o)),"el"in e&&n(0,d=e.el)},e.$$.update=()=>{4096&e.$$.dirty&&n(1,s=void 0===r||""===r?"on":r)},[d,s,a,i,l,c,f,p,m,h,g,u,r,function(e){Ee[e?"unshift":"push"]((()=>{d=e,n(0,d)}))}]}class Ho extends be{constructor(e){super(),ve(this,e,Bo,qo,_e,{el:0})}}const Ro=e=>({builder:16&e}),Ss=e=>({builder:e[4]}),Ko=e=>({builder:16&e}),Is=e=>({builder:e[4]});function Uo(e){let t,n,s,o;const r=e[17].default,a=Oe(r,e,e[16],Ss);let i=[e[4],{type:"button"},e[7]],l={};for(let e=0;e<i.length;e+=1)l=ae(l,i[e]);return{c(){t=w("button"),a&&a.c(),this.h()},l(e){t=k(e,"BUTTON",{type:!0});var n=M(t);a&&a.l(n),n.forEach(p),this.h()},h(){Ie(t,l)},m(r,i){j(r,t,i),a&&a.m(t,null),t.autofocus&&t.focus(),e[18](t),n=!0,s||(o=[Qe(e[4].action(t)),ce(t,"m-click",e[6]),ce(t,"m-keydown",e[6])],s=!0)},p(e,s){a&&a.p&&(!n||65552&s)&&Le(a,r,e,e[16],n?De(r,e[16],s,Ro):Pe(e[16]),Ss),Ie(t,l=Ve(i,[16&s&&e[4],{type:"button"},128&s&&e[7]]))},i(e){n||(T(a,e),n=!0)},o(e){P(a,e),n=!1},d(n){n&&p(t),a&&a.d(n),e[18](null),s=!1,Je(o)}}}function Go(e){let t;const n=e[17].default,s=Oe(n,e,e[16],Is);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,o){s&&s.p&&(!t||65552&o)&&Le(s,n,e,e[16],t?De(n,e[16],o,Ko):Pe(e[16]),Is)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function Ns(e){let t,n;const s=[e[3]];let o={};for(let e=0;e<s.length;e+=1)o=ae(o,s[e]);return t=new Ho({props:o}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const o=8&n?Ve(s,[Ut(e[3])]):{};t.$set(o)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Yo(e){let t,n,s,o,r;const a=[Go,Uo],i=[];function l(e,t){return e[2]?0:1}t=l(e),n=i[t]=a[t](e);let c=e[1]&&Ns(e);return{c(){n.c(),s=B(),c&&c.c(),o=fe()},l(e){n.l(e),s=q(e),c&&c.l(e),o=fe()},m(e,n){i[t].m(e,n),j(e,s,n),c&&c.m(e,n),j(e,o,n),r=!0},p(e,[r]){let u=t;t=l(e),t===u?i[t].p(e,r):(pe(),P(i[u],1,1,(()=>{i[u]=null})),ge(),n=i[t],n?n.p(e,r):(n=i[t]=a[t](e),n.c()),T(n,1),n.m(s.parentNode,s)),e[1]?c?(c.p(e,r),2&r&&T(c,1)):(c=Ns(e),c.c(),T(c,1),c.m(o.parentNode,o)):c&&(pe(),P(c,1,1,(()=>{c=null})),ge())},i(e){r||(T(n),T(c),r=!0)},o(e){P(n),P(c),r=!1},d(e){e&&(p(s),p(o)),i[t].d(e),c&&c.d(e)}}}function Wo(e,t,n){let s,o;const r=["checked","onCheckedChange","disabled","name","value","includeInput","required","asChild","inputAttrs","el"];let a,i=Se(t,r),{$$slots:l={},$$scope:c}=t,{checked:u}=t,{onCheckedChange:d}=t,{disabled:f}=t,{name:p}=t,{value:m}=t,{includeInput:h=!0}=t,{required:g}=t,{asChild:$=!1}=t,{inputAttrs:v}=t,{el:b}=t;const{elements:{root:y},states:{checked:w},updateOption:k,getAttrs:E}=Fo({disabled:f,name:p,value:m,required:g,defaultChecked:u,onCheckedChange:({next:e})=>(u!==e&&(null==d||d(e),n(8,u=e)),e)});qe(e,y,(e=>n(15,a=e)));const x=rn();return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(7,i=Se(t,r)),"checked"in e&&n(8,u=e.checked),"onCheckedChange"in e&&n(9,d=e.onCheckedChange),"disabled"in e&&n(10,f=e.disabled),"name"in e&&n(11,p=e.name),"value"in e&&n(12,m=e.value),"includeInput"in e&&n(1,h=e.includeInput),"required"in e&&n(13,g=e.required),"asChild"in e&&n(2,$=e.asChild),"inputAttrs"in e&&n(3,v=e.inputAttrs),"el"in e&&n(0,b=e.el),"$$scope"in e&&n(16,c=e.$$scope)},e.$$.update=()=>{256&e.$$.dirty&&void 0!==u&&w.set(u),1024&e.$$.dirty&&k("disabled",f),2048&e.$$.dirty&&k("name",p),4096&e.$$.dirty&&k("value",m),8192&e.$$.dirty&&k("required",g),32768&e.$$.dirty&&n(4,s=a),256&e.$$.dirty&&n(14,o={...E("root"),"data-checked":u?"":void 0}),16400&e.$$.dirty&&Object.assign(s,o)},[b,h,$,v,s,y,x,i,u,d,f,p,m,g,o,a,c,l,function(e){Ee[e?"unshift":"push"]((()=>{b=e,n(0,b)}))}]}class Jo extends be{constructor(e){super(),ve(this,e,Wo,Yo,_e,{checked:8,onCheckedChange:9,disabled:10,name:11,value:12,includeInput:1,required:13,asChild:2,inputAttrs:3,el:0})}}const zo=e=>({attrs:8&e,checked:4&e}),As=e=>({attrs:e[3],checked:e[2]});function Xo(e){let t,n=[e[5],e[3]],s={};for(let e=0;e<n.length;e+=1)s=ae(s,n[e]);return{c(){t=w("span"),this.h()},l(e){t=k(e,"SPAN",{}),M(t).forEach(p),this.h()},h(){Ie(t,s)},m(n,s){j(n,t,s),e[8](t)},p(e,o){Ie(t,s=Ve(n,[32&o&&e[5],8&o&&e[3]]))},i:re,o:re,d(n){n&&p(t),e[8](null)}}}function Zo(e){let t;const n=e[7].default,s=Oe(n,e,e[6],As);return{c(){s&&s.c()},l(e){s&&s.l(e)},m(e,n){s&&s.m(e,n),t=!0},p(e,o){s&&s.p&&(!t||76&o)&&Le(s,n,e,e[6],t?De(n,e[6],o,zo):Pe(e[6]),As)},i(e){t||(T(s,e),t=!0)},o(e){P(s,e),t=!1},d(e){s&&s.d(e)}}}function Qo(e){let t,n,s,o;const r=[Zo,Xo],a=[];function i(e,t){return e[1]?0:1}return t=i(e),n=a[t]=r[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){a[t].m(e,n),j(e,s,n),o=!0},p(e,[o]){let l=t;t=i(e),t===l?a[t].p(e,o):(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge(),n=a[t],n?n.p(e,o):(n=a[t]=r[t](e),n.c()),T(n,1),n.m(s.parentNode,s))},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),a[t].d(e)}}}function $o(e,t,n){let s;const o=["asChild","el"];let r,a=Se(t,o),{$$slots:i={},$$scope:l}=t,{asChild:c=!1}=t,{el:u}=t;const{states:{checked:d},getAttrs:f}=Hi();return qe(e,d,(e=>n(2,r=e))),e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(5,a=Se(t,o)),"asChild"in e&&n(1,c=e.asChild),"el"in e&&n(0,u=e.el),"$$scope"in e&&n(6,l=e.$$scope)},e.$$.update=()=>{4&e.$$.dirty&&n(3,s={...f("thumb"),"data-state":r?"checked":"unchecked","data-checked":r?"":void 0})},[u,c,r,s,d,a,l,i,function(e){Ee[e?"unshift":"push"]((()=>{u=e,n(0,u)}))}]}class xo extends be{constructor(e){super(),ve(this,e,$o,Qo,_e,{asChild:1,el:0})}}function ea(e){let t,n;return t=new je({props:{src:Hl,class:"h-4 w-4"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ta(e){let t,n,s,o;n=new Ao({props:{$$slots:{default:[ea]},$$scope:{ctx:e}}});const r=e[5].default,a=Oe(r,e,e[9],null);return{c(){t=w("span"),Z(n.$$.fragment),s=B(),a&&a.c(),this.h()},l(e){t=k(e,"SPAN",{class:!0});var o=M(t);X(n.$$.fragment,o),o.forEach(p),s=q(e),a&&a.l(e),this.h()},h(){E(t,"class","absolute right-2 flex h-3.5 w-3.5 items-center justify-center")},m(e,r){j(e,t,r),z(n,t,null),j(e,s,r),a&&a.m(e,r),o=!0},p(e,t){const s={};512&t&&(s.$$scope={dirty:t,ctx:e}),n.$set(s),a&&a.p&&(!o||512&t)&&Le(a,r,e,e[9],o?De(r,e[9],t,null):Pe(e[9]),null)},i(e){o||(T(n.$$.fragment,e),T(a,e),o=!0)},o(e){P(n.$$.fragment,e),P(a,e),o=!1},d(e){e&&(p(t),p(s)),J(n),a&&a.d(e)}}}function na(e){let t,n;const s=[{value:e[1]},{disabled:e[3]},{label:e[2]},{class:ot("relative flex w-full cursor-default select-none items-center rounded-xs py-1.5 pl-2 pr-8 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",e[0])},e[4]];let o={$$slots:{default:[ta]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)o=ae(o,s[e]);return t=new wo({props:o}),t.$on("click",e[6]),t.$on("pointermove",e[7]),t.$on("focusin",e[8]),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,[n]){const o=31&n?Ve(s,[2&n&&{value:e[1]},8&n&&{disabled:e[3]},4&n&&{label:e[2]},1&n&&{class:ot("relative flex w-full cursor-default select-none items-center rounded-xs py-1.5 pl-2 pr-8 text-sm outline-none data-[disabled]:pointer-events-none data-[highlighted]:bg-base-200  data-[disabled]:opacity-50",e[0])},16&n&&Ut(e[4])]):{};512&n&&(o.$$scope={dirty:n,ctx:e}),t.$set(o)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function sa(e,t,n){const s=["class","value","label","disabled"];let o=Se(t,s),{$$slots:r={},$$scope:a}=t,{class:i}=t,{value:l}=t,{label:c}=t,{disabled:u}=t;return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(4,o=Se(t,s)),"class"in e&&n(0,i=e.class),"value"in e&&n(1,l=e.value),"label"in e&&n(2,c=e.label),"disabled"in e&&n(3,u=e.disabled),"$$scope"in e&&n(9,a=e.$$scope)},[i,l,c,u,o,r,function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)},a]}class Ri extends be{constructor(e){super(),ve(this,e,sa,na,_e,{class:0,value:1,label:2,disabled:3})}}function ia(e){let t,n;const s=e[7].default,o=Oe(s,e,e[8],null);return{c(){t=w("div"),o&&o.c(),this.h()},l(e){t=k(e,"DIV",{class:!0});var n=M(t);o&&o.l(n),n.forEach(p),this.h()},h(){E(t,"class","w-full p-1")},m(e,s){j(e,t,s),o&&o.m(t,null),n=!0},p(e,t){o&&o.p&&(!n||256&t)&&Le(o,s,e,e[8],n?De(s,e[8],t,null):Pe(e[8]),null)},i(e){n||(T(o,e),n=!0)},o(e){P(o,e),n=!1},d(e){e&&p(t),o&&o.d(e)}}}function la(e){let t,n;const s=[{inTransition:e[2]},{inTransitionConfig:e[3]},{outTransition:e[4]},{outTransitionConfig:e[5]},{sideOffset:e[1]},{class:ot("relative z-50 min-w-[8rem] overflow-hidden rounded-md border border-base-300 bg-base-100 shadow-md focus:outline-none",e[0])},e[6]];let o={$$slots:{default:[ia]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)o=ae(o,s[e]);return t=new oo({props:o}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,[n]){const o=127&n?Ve(s,[4&n&&{inTransition:e[2]},8&n&&{inTransitionConfig:e[3]},16&n&&{outTransition:e[4]},32&n&&{outTransitionConfig:e[5]},2&n&&{sideOffset:e[1]},1&n&&{class:ot("relative z-50 min-w-[8rem] overflow-hidden rounded-md border border-base-300 bg-base-100 shadow-md focus:outline-none",e[0])},64&n&&Ut(e[6])]):{};256&n&&(o.$$scope={dirty:n,ctx:e}),t.$set(o)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ra(e,t,n){const s=["class","sideOffset","inTransition","inTransitionConfig","outTransition","outTransitionConfig"];let o=Se(t,s),{$$slots:r={},$$scope:a}=t,{class:i}=t,{sideOffset:l=4}=t,{inTransition:c=Rl}=t,{inTransitionConfig:u}=t,{outTransition:d=Kl}=t,{outTransitionConfig:f={start:.95,opacity:0,duration:50}}=t;return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(6,o=Se(t,s)),"class"in e&&n(0,i=e.class),"sideOffset"in e&&n(1,l=e.sideOffset),"inTransition"in e&&n(2,c=e.inTransition),"inTransitionConfig"in e&&n(3,u=e.inTransitionConfig),"outTransition"in e&&n(4,d=e.outTransition),"outTransitionConfig"in e&&n(5,f=e.outTransitionConfig),"$$scope"in e&&n(8,a=e.$$scope)},[i,l,c,u,d,f,o,r,a]}class oa extends be{constructor(e){super(),ve(this,e,ra,la,_e,{class:0,sideOffset:1,inTransition:2,inTransitionConfig:3,outTransition:4,outTransitionConfig:5})}}function aa(e){let t,n,s,o;const r=e[2].default,a=Oe(r,e,e[3],null);return s=new je({props:{src:Ul,class:"h-4 w-4"}}),{c(){a&&a.c(),t=B(),n=w("div"),Z(s.$$.fragment)},l(e){a&&a.l(e),t=q(e),n=k(e,"DIV",{});var o=M(n);X(s.$$.fragment,o),o.forEach(p)},m(e,r){a&&a.m(e,r),j(e,t,r),j(e,n,r),z(s,n,null),o=!0},p(e,t){a&&a.p&&(!o||8&t)&&Le(a,r,e,e[3],o?De(r,e[3],t,null):Pe(e[3]),null)},i(e){o||(T(a,e),T(s.$$.fragment,e),o=!0)},o(e){P(a,e),P(s.$$.fragment,e),o=!1},d(e){e&&(p(t),p(n)),a&&a.d(e),J(s)}}}function ca(e){let t,n;const s=[{class:ot("border border-base-300 flex h-9 w-full items-center justify-between rounded-md bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-base-content-muted focus:outline-none focus:ring-1 focus:ring-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",e[0])},e[1]];let o={$$slots:{default:[aa]},$$scope:{ctx:e}};for(let e=0;e<s.length;e+=1)o=ae(o,s[e]);return t=new jo({props:o}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,[n]){const o=3&n?Ve(s,[1&n&&{class:ot("border border-base-300 flex h-9 w-full items-center justify-between rounded-md bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-base-content-muted focus:outline-none focus:ring-1 focus:ring-base-content-muted disabled:cursor-not-allowed disabled:opacity-50",e[0])},2&n&&Ut(e[1])]):{};8&n&&(o.$$scope={dirty:n,ctx:e}),t.$set(o)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ua(e,t,n){const s=["class"];let o=Se(t,s),{$$slots:r={},$$scope:a}=t,{class:i}=t;return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(1,o=Se(t,s)),"class"in e&&n(0,i=e.class),"$$scope"in e&&n(3,a=e.$$scope)},[i,o,r,a]}class fa extends be{constructor(e){super(),ve(this,e,ua,ca,_e,{class:0})}}const da=Wr,ma=ho;function ha(e,{from:t,to:n},s={}){const o=getComputedStyle(e),r="none"===o.transform?"":o.transform,[a,i]=o.transformOrigin.split(" ").map(parseFloat),l=t.left+t.width*a/n.width-(n.left+a),c=t.top+t.height*i/n.height-(n.top+i),{delay:u=0,duration:d=e=>120*Math.sqrt(e),easing:f=Gl}=s;return{delay:u,duration:ln(d)?d(Math.sqrt(l*l+c*c)):d,easing:f,css:(e,s)=>{const o=s*l,a=s*c,i=e+s*t.width/n.width,u=e+s*t.height/n.height;return`transform: ${r} translate(${o}px, ${a}px) scale(${i}, ${u});`}}}var pa={default:{a:{viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},path:[{stroke:"none",d:"M0 0h24v24H0z",fill:"none"},{d:"M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"},{d:"M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"},{d:"M9 17v-4"},{d:"M12 17v-1"},{d:"M15 17v-2"},{d:"M12 17v-1"}]}},ga={default:{a:{viewBox:"0 0 200 200",style:"enable-background:new 0 0 791.9 221.6;"},path:[{fill:"currentColor",d:"M 13.53 126.114 C 13.53 126.114 64.492 162.546 67.591 164.363 C 70.582 166.179 74.963 166.606 78.808 165.111 C 82.655 163.615 85.54 160.196 86.395 156.777 C 87.356 153.358 99.002 91.819 99.002 91.819 C 99.108 90.964 99.428 88.507 98.147 86.156 C 96.331 82.951 92.271 81.242 88.638 82.31 C 85.754 83.165 84.258 85.302 83.83 86.049 C 83.19 87.224 82.014 89.148 79.877 90.857 C 78.275 92.139 76.672 92.887 75.924 93.208 C 69.407 95.879 62.142 93.635 57.975 88.293 C 55.411 84.981 50.817 83.699 46.757 85.302 C 42.697 86.904 40.347 91.071 40.667 95.238 C 41.415 101.862 37.676 108.486 31.052 111.156 C 27.526 112.546 23.787 112.546 20.475 111.477 C 19.727 111.263 17.056 110.623 14.385 112.118 C 11.073 113.828 9.363 117.887 10.218 121.52 C 10.859 123.978 12.783 125.58 13.53 126.114 Z",style:"transform-origin: 122.948px 117.753px;"},{fill:"currentColor",d:"M 98.467 46.305 C 98.467 46.305 116.63 106.349 117.912 109.554 C 119.194 112.866 122.399 115.858 126.352 117.032 C 130.306 118.101 134.686 117.247 137.463 115.11 C 140.242 112.973 187.143 71.412 187.143 71.412 C 187.785 70.879 189.494 69.062 189.922 66.391 C 190.455 62.759 188.212 58.912 184.793 57.523 C 182.015 56.455 179.451 57.203 178.703 57.523 C 177.528 58.057 175.391 58.805 172.72 58.912 C 170.583 59.019 168.981 58.592 168.126 58.378 C 161.395 56.455 156.908 50.259 156.908 43.528 C 156.802 39.361 154.023 35.515 149.857 34.339 C 145.583 33.164 141.203 34.98 138.959 38.506 C 135.434 44.275 128.489 47.16 121.651 45.237 C 118.019 44.168 115.027 41.925 113.104 39.04 C 112.57 38.399 110.86 36.263 107.869 35.728 C 104.236 35.087 100.283 37.224 98.788 40.536 C 97.612 43.1 98.254 45.451 98.467 46.305 Z",style:"transform-origin: 122.948px 117.753px;"}]}},_a={default:{a:{role:"img",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round"},title:[{}],path:[{stroke:"none",d:"M0 0h24v24H0z",fill:"none"},{d:"M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4"},{d:"M13.5 6.5l4 4"}]}};const it=Object.freeze(Object.defineProperty({__proto__:null,Clipboard:pa,Motherduck:ga,Pencil:_a},Symbol.toStringTag,{value:"Module"}));function ba(e){const t=JSON.parse(e);return t.data&&(t.data=vr(t.data)),t}function Cn(e){return HTMLElement.prototype.cloneNode.call(e)}function va(e,t=()=>{}){const n=async({action:t,result:n,reset:s=!0,invalidateAll:o=!0})=>{"success"===n.type&&(s&&HTMLFormElement.prototype.reset.call(e),o&&await _r()),(location.origin+location.pathname===t.origin+t.pathname||"redirect"===n.type||"error"===n.type)&&br(n)};async function s(s){var o,r,a,i,l;if("post"!==(null!=(o=s.submitter)&&o.hasAttribute("formmethod")?s.submitter.formMethod:Cn(e).method))return;s.preventDefault();const c=new URL(null!=(r=s.submitter)&&r.hasAttribute("formaction")?s.submitter.formAction:Cn(e).action),u=null!=(a=s.submitter)&&a.hasAttribute("formenctype")?s.submitter.formEnctype:Cn(e).enctype,d=new FormData(e),f=null==(i=s.submitter)?void 0:i.getAttribute("name");f&&d.append(f,(null==(l=s.submitter)?void 0:l.getAttribute("value"))??"");const p=new AbortController;let m=!1;const h=await t({action:c,cancel:()=>m=!0,controller:p,formData:d,formElement:e,submitter:s.submitter})??n;if(m)return;let g;try{const e=new Headers({accept:"application/json","x-sveltekit-action":"true"});"multipart/form-data"!==u&&e.set("Content-Type",/^(:?application\/x-www-form-urlencoded|text\/plain)$/.test(u)?u:"application/x-www-form-urlencoded");const t="multipart/form-data"===u?d:new URLSearchParams(d),n=await fetch(c,{method:"POST",headers:e,cache:"no-store",body:t,signal:p.signal});g=ba(await n.text()),"error"===g.type&&(g.status=n.status)}catch(e){if("AbortError"===(null==e?void 0:e.name))return;g={type:"error",error:e}}h({action:c,formData:d,formElement:e,update:e=>n({action:c,result:g,reset:null==e?void 0:e.reset,invalidateAll:null==e?void 0:e.invalidateAll}),result:g})}return HTMLFormElement.prototype.addEventListener.call(e,"submit",s),{destroy(){HTMLFormElement.prototype.removeEventListener.call(e,"submit",s)}}}function ya(e){let t,n;return t=new xo({props:{class:ot("bg-base-100 pointer-events-none block h-4 w-4 rounded-full shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ka(e){let t,n,s;const o=[{class:ot("focus-visible:ring-base-300 focus-visible:ring-offset-base-100 data-[state=checked]:bg-base-content data-[state=unchecked]:bg-base-300 peer inline-flex h-[20px] w-[36px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e[1])},e[2]];function r(t){e[3](t)}let a={$$slots:{default:[ya]},$$scope:{ctx:e}};for(let e=0;e<o.length;e+=1)a=ae(a,o[e]);return void 0!==e[0]&&(a.checked=e[0]),t=new Jo({props:a}),Ee.push((()=>Ge(t,"checked",r))),t.$on("click",e[4]),t.$on("keydown",e[5]),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,n){z(t,e,n),s=!0},p(e,[s]){const r=6&s?Ve(o,[2&s&&{class:ot("focus-visible:ring-base-300 focus-visible:ring-offset-base-100 data-[state=checked]:bg-base-content data-[state=unchecked]:bg-base-300 peer inline-flex h-[20px] w-[36px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e[1])},4&s&&Ut(e[2])]):{};64&s&&(r.$$scope={dirty:s,ctx:e}),!n&&1&s&&(n=!0,r.checked=e[0],Ue((()=>n=!1))),t.$set(r)},i(e){s||(T(t.$$.fragment,e),s=!0)},o(e){P(t.$$.fragment,e),s=!1},d(e){J(t,e)}}}function wa(e,t,n){const s=["class","checked"];let o=Se(t,s),{class:r}=t,{checked:a}=t;return e.$$set=e=>{t=ae(ae({},t),Ye(e)),n(2,o=Se(t,s)),"class"in e&&n(1,r=e.class),"checked"in e&&n(0,a=e.checked)},[a,r,o,function(e){a=e,n(0,a)},function(t){Ke.call(this,e,t)},function(t){Ke.call(this,e,t)}]}class Yn extends be{constructor(e){super(),ve(this,e,wa,ka,_e,{class:1,checked:0})}}function Os(e){let t,n='<span class="text-base-content-muted text-sm select-none font-mono">sources/</span>';return{c(){t=w("div"),t.innerHTML=n,this.h()},l(e){t=k(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1vp1rbd"!==oe(t)&&(t.innerHTML=n),this.h()},h(){E(t,"class","flex items-center border border-r-0 border-base-300 rounded-l-md px-3 bg-base-100 cursor-text")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function Ls(e){let t,n,s;return{c(){t=w("span"),n=le(e[1]),this.h()},l(s){t=k(s,"SPAN",{class:!0});var o=M(t);n=ie(o,e[1]),o.forEach(p),this.h()},h(){E(t,"class","text-negative text-xs break-words max-w-md")},m(e,s){j(e,t,s),g(t,n)},p(e,t){2&t&&ye(n,e[1])},i(e){e&&(s||ke((()=>{s=Ae(t,Xe,{}),s.start()})))},o:re,d(e){e&&p(t)}}}function Ca(e){let t,n='Tables from this source can be queried using <code class="select-all">`&lt;source name&gt;.<wbr/>&lt;tablename&gt;`</code>. Changing the name will change how you reference the source in your queries, but it will not\n\t\t\tchange the source directory.';return{c(){t=w("p"),t.innerHTML=n,this.h()},l(e){t=k(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-17lv2uo"!==oe(t)&&(t.innerHTML=n),this.h()},h(){E(t,"class","text-xs text-base-content-muted")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function Ta(e){let t,n='Name of the new directory that will be created for this source, under <code class="select-all">`/sources`</code>.';return{c(){t=w("p"),t.innerHTML=n,this.h()},l(e){t=k(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1alkzla"!==oe(t)&&(t.innerHTML=n),this.h()},h(){E(t,"class","text-xs text-base-content-muted")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function Ea(e){let t,n,s,o,r,a,i,l,c,u,d,f,m=e[2]?"Directory Name":"Name",h=e[2]&&Os(),$=e[1]&&Ls(e);function v(e,t){return e[2]?Ta:Ca}let b=v(e),y=b(e);return{c(){t=w("div"),n=w("label"),s=w("span"),o=le(m),r=B(),a=w("div"),h&&h.c(),i=B(),l=w("input"),c=B(),$&&$.c(),u=B(),y.c(),this.h()},l(e){t=k(e,"DIV",{class:!0});var d=M(t);n=k(d,"LABEL",{for:!0,class:!0});var f=M(n);s=k(f,"SPAN",{class:!0});var g=M(s);o=ie(g,m),g.forEach(p),r=q(f),a=k(f,"DIV",{class:!0});var v=M(a);h&&h.l(v),i=q(v),l=k(v,"INPUT",{name:!0,class:!0}),v.forEach(p),f.forEach(p),c=q(d),$&&$.l(d),u=q(d),y.l(d),d.forEach(p),this.h()},h(){E(s,"class","text-sm font-medium"),l.required=!0,E(l,"name","sourceName"),E(l,"class","flex-1 border border-base-300 bg-base-100 shadow-sm text-sm h-9 bg-transparent px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none"),tt(l,"rounded-md",!e[2]),tt(l,"rounded-l-none",e[2]),tt(l,"rounded-r-md",e[2]),E(a,"class","flex w-full group focus-within:ring-1 focus-within:ring-base-300 rounded-md"),E(n,"for","sourceName"),E(n,"class","flex flex-col gap-2"),E(t,"class","flex flex-col gap-2")},m(p,m){j(p,t,m),g(t,n),g(n,s),g(s,o),g(n,r),g(n,a),h&&h.m(a,null),g(a,i),g(a,l),we(l,e[0]),g(t,c),$&&$.m(t,null),g(t,u),y.m(t,null),d||(f=[ce(l,"input",e[3]),ce(l,"change",e[4])],d=!0)},p(e,[n]){4&n&&m!==(m=e[2]?"Directory Name":"Name")&&ye(o,m),e[2]?h||(h=Os(),h.c(),h.m(a,i)):h&&(h.d(1),h=null),1&n&&l.value!==e[0]&&we(l,e[0]),4&n&&tt(l,"rounded-md",!e[2]),4&n&&tt(l,"rounded-l-none",e[2]),4&n&&tt(l,"rounded-r-md",e[2]),e[1]?$?($.p(e,n),2&n&&T($,1)):($=Ls(e),$.c(),T($,1),$.m(t,u)):$&&($.d(1),$=null),b!==(b=v(e))&&(y.d(1),y=b(e),y&&(y.c(),y.m(t,null)))},i(e){T($)},o:re,d(e){e&&p(t),h&&h.d(),$&&$.d(),y.d(),d=!1,Je(f)}}}const Sa=/^[\w_]+$/,Ki=(e,t)=>e.length<1?"Source name must be set.":Sa.test(e)?e&&t.some((t=>t.name===e))?`A source named ${e} already exists.`:"":"Source names can only contain letters, numbers, and underscores.";function Ia(e,t,n){let{sourceName:s}=t,{nameError:o}=t,{showPrefix:r=!1}=t;return e.$$set=e=>{"sourceName"in e&&n(0,s=e.sourceName),"nameError"in e&&n(1,o=e.nameError),"showPrefix"in e&&n(2,r=e.showPrefix)},[s,o,r,function(){s=this.value,n(0,s)},()=>n(1,o="")]}class Ui extends be{constructor(e){super(),ve(this,e,Ia,Ea,_e,{sourceName:0,nameError:1,showPrefix:2})}}const Dt={collection:"map",default:!0,nodeClass:_t,tag:"tag:yaml.org,2002:map",resolve:(e,t)=>(Di(e)||t("Expected a mapping for this tag"),e),createNode:(e,t,n)=>_t.from(e,t,n)},Mt={collection:"seq",default:!0,nodeClass:It,tag:"tag:yaml.org,2002:seq",resolve:(e,t)=>(Mi(e)||t("Expected a sequence for this tag"),e),createNode:(e,t,n)=>It.from(e,t,n)},un={identify:e=>"string"==typeof e,default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:(e,t,n,s)=>(t=Object.assign({actualString:!0},t),Vi(e,t,n,s))},fn={identify:e=>null==e,createNode:()=>new Be(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new Be(null),stringify:({source:e},t)=>"string"==typeof e&&fn.test.test(e)?e:t.options.nullStr},Wn={identify:e=>"boolean"==typeof e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:e=>new Be("t"===e[0]||"T"===e[0]),stringify:({source:e,value:t},n)=>e&&Wn.test.test(e)&&t===("t"===e[0]||"T"===e[0])?e:t?n.options.trueStr:n.options.falseStr};function $e({format:e,minFractionDigits:t,tag:n,value:s}){if("bigint"==typeof s)return String(s);const o="number"==typeof s?s:Number(s);if(!isFinite(o))return isNaN(o)?".nan":o<0?"-.inf":".inf";let r=JSON.stringify(s);if(!e&&t&&(!n||"tag:yaml.org,2002:float"===n)&&/^\d/.test(r)){let e=r.indexOf(".");e<0&&(e=r.length,r+=".");let n=t-(r.length-e-1);for(;n-- >0;)r+="0"}return r}const Gi={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>"nan"===e.slice(-3).toLowerCase()?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:$e},Yi={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e),stringify(e){const t=Number(e.value);return isFinite(t)?t.toExponential():$e(e)}},Wi={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(e){const t=new Be(parseFloat(e)),n=e.indexOf(".");return-1!==n&&"0"===e[e.length-1]&&(t.minFractionDigits=e.length-n-1),t},stringify:$e},dn=e=>"bigint"==typeof e||Number.isInteger(e),Jn=(e,t,n,{intAsBigInt:s})=>s?BigInt(e):parseInt(e.substring(t),n);function Ji(e,t,n){const{value:s}=e;return dn(s)&&s>=0?n+s.toString(t):$e(e)}const zi={identify:e=>dn(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(e,t,n)=>Jn(e,2,8,n),stringify:e=>Ji(e,8,"0o")},Xi={identify:dn,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(e,t,n)=>Jn(e,0,10,n),stringify:$e},Zi={identify:e=>dn(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(e,t,n)=>Jn(e,2,16,n),stringify:e=>Ji(e,16,"0x")},Na=[Dt,Mt,un,fn,Wn,zi,Xi,Zi,Gi,Yi,Wi];function Ps(e){return"bigint"==typeof e||Number.isInteger(e)}const $t=({value:e})=>JSON.stringify(e),Aa=[{identify:e=>"string"==typeof e,default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:$t},{identify:e=>null==e,createNode:()=>new Be(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:$t},{identify:e=>"boolean"==typeof e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^true$|^false$/,resolve:e=>"true"===e,stringify:$t},{identify:Ps,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(e,t,{intAsBigInt:n})=>n?BigInt(e):parseInt(e,10),stringify:({value:e})=>Ps(e)?e.toString():JSON.stringify(e)},{identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:e=>parseFloat(e),stringify:$t}],Oa={default:!0,tag:"",test:/^/,resolve:(e,t)=>(t(`Unresolved plain scalar ${JSON.stringify(e)}`),e)},La=[Dt,Mt].concat(Aa,Oa),zn={identify:e=>e instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(e,t){if("function"==typeof atob){const t=atob(e.replace(/[\n\r]/g,"")),n=new Uint8Array(t.length);for(let e=0;e<t.length;++e)n[e]=t.charCodeAt(e);return n}return t("This environment does not support reading binary tags; either Buffer or atob is required"),e},stringify({comment:e,type:t,value:n},s,o,r){const a=n;let i;if("function"!=typeof btoa)throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");{let e="";for(let t=0;t<a.length;++t)e+=String.fromCharCode(a[t]);i=btoa(e)}if(t||(t=Be.BLOCK_LITERAL),t!==Be.QUOTE_DOUBLE){const e=Math.max(s.options.lineWidth-s.indent.length,s.options.minContentWidth),n=Math.ceil(i.length/e),o=new Array(n);for(let t=0,s=0;t<n;++t,s+=e)o[t]=i.substr(s,e);i=o.join(t===Be.BLOCK_LITERAL?"\n":" ")}return Vi({comment:e,type:t,value:i},s,o,r)}};function Qi({value:e,source:t},n){return t&&(e?$i:xi).test.test(t)?t:e?n.options.trueStr:n.options.falseStr}const $i={identify:e=>!0===e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new Be(!0),stringify:Qi},xi={identify:e=>!1===e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new Be(!1),stringify:Qi},Pa={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>"nan"===e.slice(-3).toLowerCase()?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:$e},Da={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e.replace(/_/g,"")),stringify(e){const t=Number(e.value);return isFinite(t)?t.toExponential():$e(e)}},Ma={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(e){const t=new Be(parseFloat(e.replace(/_/g,""))),n=e.indexOf(".");if(-1!==n){const s=e.substring(n+1).replace(/_/g,"");"0"===s[s.length-1]&&(t.minFractionDigits=s.length)}return t},stringify:$e},Wt=e=>"bigint"==typeof e||Number.isInteger(e);function mn(e,t,n,{intAsBigInt:s}){const o=e[0];if(("-"===o||"+"===o)&&(t+=1),e=e.substring(t).replace(/_/g,""),s){switch(n){case 2:e=`0b${e}`;break;case 8:e=`0o${e}`;break;case 16:e=`0x${e}`}const t=BigInt(e);return"-"===o?BigInt(-1)*t:t}const r=parseInt(e,n);return"-"===o?-1*r:r}function Xn(e,t,n){const{value:s}=e;if(Wt(s)){const e=s.toString(t);return s<0?"-"+n+e.substr(1):n+e}return $e(e)}const Va={identify:Wt,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(e,t,n)=>mn(e,2,2,n),stringify:e=>Xn(e,2,"0b")},ja={identify:Wt,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(e,t,n)=>mn(e,1,8,n),stringify:e=>Xn(e,8,"0")},Fa={identify:Wt,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(e,t,n)=>mn(e,0,10,n),stringify:$e},qa={identify:Wt,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(e,t,n)=>mn(e,2,16,n),stringify:e=>Xn(e,16,"0x")};function Zn(e,t){const n=e[0],s="-"===n||"+"===n?e.substring(1):e,o=e=>t?BigInt(e):Number(e),r=s.replace(/_/g,"").split(":").reduce(((e,t)=>e*o(60)+o(t)),o(0));return"-"===n?o(-1)*r:r}function el(e){let{value:t}=e,n=e=>e;if("bigint"==typeof t)n=e=>BigInt(e);else if(isNaN(t)||!isFinite(t))return $e(e);let s="";t<0&&(s="-",t*=n(-1));const o=n(60),r=[t%o];return t<60?r.unshift(0):(t=(t-r[0])/o,r.unshift(t%o),t>=60&&(t=(t-r[0])/o,r.unshift(t))),s+r.map((e=>String(e).padStart(2,"0"))).join(":").replace(/000000\d*$/,"")}const tl={identify:e=>"bigint"==typeof e||Number.isInteger(e),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(e,t,{intAsBigInt:n})=>Zn(e,n),stringify:el},nl={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:e=>Zn(e,!1),stringify:el},hn={identify:e=>e instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(e){const t=e.match(hn.test);if(!t)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");const[,n,s,o,r,a,i]=t.map(Number),l=t[7]?Number((t[7]+"00").substr(1,3)):0;let c=Date.UTC(n,s-1,o,r||0,a||0,i||0,l);const u=t[8];if(u&&"Z"!==u){let e=Zn(u,!1);Math.abs(e)<30&&(e*=60),c-=6e4*e}return new Date(c)},stringify:({value:e})=>e.toISOString().replace(/(T00:00:00)?\.000Z$/,"")},Ds=[Dt,Mt,un,fn,$i,xi,Va,ja,Fa,qa,Pa,Da,Ma,zn,Et,Fn,qn,Bn,tl,nl,hn],Ms=new Map([["core",Na],["failsafe",[Dt,Mt,un]],["json",La],["yaml11",Ds],["yaml-1.1",Ds]]),Vs={binary:zn,bool:Wn,float:Wi,floatExp:Yi,floatNaN:Gi,floatTime:nl,int:Xi,intHex:Zi,intOct:zi,intTime:tl,map:Dt,merge:Et,null:fn,omap:Fn,pairs:qn,seq:Mt,set:Bn,timestamp:hn},Ba={"tag:yaml.org,2002:binary":zn,"tag:yaml.org,2002:merge":Et,"tag:yaml.org,2002:omap":Fn,"tag:yaml.org,2002:pairs":qn,"tag:yaml.org,2002:set":Bn,"tag:yaml.org,2002:timestamp":hn};function Tn(e,t,n){const s=Ms.get(t);if(s&&!e)return n&&!s.includes(Et)?s.concat(Et):s.slice();let o=s;if(!o){if(!Array.isArray(e)){const e=Array.from(Ms.keys()).filter((e=>"yaml11"!==e)).map((e=>JSON.stringify(e))).join(", ");throw new Error(`Unknown schema "${t}"; use one of ${e} or define customTags array`)}o=[]}if(Array.isArray(e))for(const t of e)o=o.concat(t);else"function"==typeof e&&(o=e(o.slice()));return n&&(o=o.concat(Et)),o.reduce(((e,t)=>{const n="string"==typeof t?Vs[t]:t;if(!n){const e=JSON.stringify(t),n=Object.keys(Vs).map((e=>JSON.stringify(e))).join(", ");throw new Error(`Unknown custom tag ${e}; use one of ${n}`)}return e.includes(n)||e.push(n),e}),[])}const Ha=(e,t)=>e.key<t.key?-1:e.key>t.key?1:0;class pn{constructor({compat:e,customTags:t,merge:n,resolveKnownTags:s,schema:o,sortMapEntries:r,toStringDefaults:a}){this.compat=Array.isArray(e)?Tn(e,"compat"):e?Tn(null,e):null,this.name="string"==typeof o&&o||"core",this.knownTags=s?Ba:{},this.tags=Tn(t,this.name,n),this.toStringOptions=a??null,Object.defineProperty(this,Yl,{value:Dt}),Object.defineProperty(this,bt,{value:un}),Object.defineProperty(this,Wl,{value:Mt}),this.sortMapEntries="function"==typeof r?r:!0===r?Ha:null}clone(){const e=Object.create(pn.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}}function Ra(e,t){var n;const s=[];let o=!0===t.directives;if(!1!==t.directives&&e.directives){const t=e.directives.toString(e);t?(s.push(t),o=!0):e.directives.docStart&&(o=!0)}o&&s.push("---");const r=Jl(e,t),{commentString:a}=r.options;if(e.commentBefore){1!==s.length&&s.unshift("");const t=a(e.commentBefore);s.unshift(Zt(t,""))}let i=!1,l=null;if(e.contents){if(on(e.contents)){if(e.contents.spaceBefore&&o&&s.push(""),e.contents.commentBefore){const t=a(e.contents.commentBefore);s.push(Zt(t,""))}r.forceBlockIndent=!!e.comment,l=e.contents.comment}const t=l?void 0:()=>i=!0;let n=rs(e.contents,r,(()=>l=null),t);l&&(n+=zl(n,"",a(l))),"|"!==n[0]&&">"!==n[0]||"---"!==s[s.length-1]?s.push(n):s[s.length-1]=`--- ${n}`}else s.push(rs(e.contents,r));if(null!=(n=e.directives)&&n.docEnd)if(e.comment){const t=a(e.comment);t.includes("\n")?(s.push("..."),s.push(Zt(t,""))):s.push(`... ${t}`)}else s.push("...");else{let t=e.comment;t&&i&&(t=t.replace(/^\n+/,"")),t&&((!i||l)&&""!==s[s.length-1]&&s.push(""),s.push(Zt(a(t),"")))}return s.join("\n")+"\n"}class Vt{constructor(e,t,n){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,os,{value:as});let s=null;"function"==typeof t||Array.isArray(t)?s=t:void 0===n&&t&&(n=t,t=void 0);const o=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},n);this.options=o;let{version:r}=o;null!=n&&n._directives?(this.directives=n._directives.atDocument(),this.directives.yaml.explicit&&(r=this.directives.yaml.version)):this.directives=new nn({version:r}),this.setSchema(r,n),this.contents=void 0===e?null:this.createNode(e,s,n)}clone(){const e=Object.create(Vt.prototype,{[os]:{value:as}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=on(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){kt(this.contents)&&this.contents.add(e)}addIn(e,t){kt(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){const n=Xl(this);e.anchor=!t||n.has(t)?Zl(t||"a",n):t}return new Hn(e.anchor)}createNode(e,t,n){let s;if("function"==typeof t)e=t.call({"":e},"",e),s=t;else if(Array.isArray(t)){const e=e=>"number"==typeof e||e instanceof String||e instanceof Number,n=t.filter(e).map(String);n.length>0&&(t=t.concat(n)),s=t}else void 0===n&&t&&(n=t,t=void 0);const{aliasDuplicateObjects:o,anchorPrefix:r,flow:a,keepUndefined:i,onTagObj:l,tag:c}=n??{},{onAnchor:u,setAnchors:d,sourceObjects:f}=$l(this,r||"a"),p={aliasDuplicateObjects:o??!0,keepUndefined:i??!1,onAnchor:u,onTagObj:l,replacer:s,schema:this.schema,sourceObjects:f},m=Ql(e,c,p);return a&&ft(m)&&(m.flow=!0),d(),m}createPair(e,t,n={}){const s=this.createNode(e,null,n),o=this.createNode(t,null,n);return new Rt(s,o)}delete(e){return!!kt(this.contents)&&this.contents.delete(e)}deleteIn(e){return Qt(e)?null!=this.contents&&(this.contents=null,!0):!!kt(this.contents)&&this.contents.deleteIn(e)}get(e,t){return ft(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return Qt(e)?!t&&Nt(this.contents)?this.contents.value:this.contents:ft(this.contents)?this.contents.getIn(e,t):void 0}has(e){return!!ft(this.contents)&&this.contents.has(e)}hasIn(e){return Qt(e)?void 0!==this.contents:!!ft(this.contents)&&this.contents.hasIn(e)}set(e,t){null==this.contents?this.contents=cs(this.schema,[e],t):kt(this.contents)&&this.contents.set(e,t)}setIn(e,t){Qt(e)?this.contents=t:null==this.contents?this.contents=cs(this.schema,Array.from(e),t):kt(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){let n;switch("number"==typeof e&&(e=String(e)),e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new nn({version:"1.1"}),n={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new nn({version:e}),n={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,n=null;break;default:{const t=JSON.stringify(e);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${t}`)}}if(t.schema instanceof Object)this.schema=t.schema;else{if(!n)throw new Error("With a null YAML version, the { schema: Schema } option is required");this.schema=new pn(Object.assign(n,t))}}toJS({json:e,jsonArg:t,mapAsMap:n,maxAliasCount:s,onAnchor:o,reviver:r}={}){const a={anchors:new Map,doc:this,keep:!e,mapAsMap:!0===n,mapKeyWarned:!1,maxAliasCount:"number"==typeof s?s:100},i=xl(this.contents,t??"",a);if("function"==typeof o)for(const{count:e,res:t}of a.anchors.values())o(t,e);return"function"==typeof r?er(r,{"":i},"",i):i}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||Number(e.indent)<=0)){const t=JSON.stringify(e.indent);throw new Error(`"indent" option must be a positive integer, not ${t}`)}return Ra(this,e)}}function kt(e){if(ft(e))return!0;throw new Error("Expected a YAML collection as document contents")}function Lt(e,{flow:t,indicator:n,next:s,offset:o,onError:r,parentIndent:a,startOnNewline:i}){let l=!1,c=i,u=i,d="",f="",p=!1,m=!1,h=null,g=null,$=null,v=null,b=null,y=null,w=null;for(const o of e)switch(m&&("space"!==o.type&&"newline"!==o.type&&"comma"!==o.type&&r(o.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),m=!1),h&&(c&&"comment"!==o.type&&"newline"!==o.type&&r(h,"TAB_AS_INDENT","Tabs are not allowed as indentation"),h=null),o.type){case"space":!t&&("doc-start"!==n||"flow-collection"!==(null==s?void 0:s.type))&&o.source.includes("\t")&&(h=o),u=!0;break;case"comment":{u||r(o,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const e=o.source.substring(1)||" ";d?d+=f+e:d=e,f="",c=!1;break}case"newline":c?d?d+=o.source:(!y||"seq-item-ind"!==n)&&(l=!0):f+=o.source,c=!0,p=!0,(g||$)&&(v=o),u=!0;break;case"anchor":g&&r(o,"MULTIPLE_ANCHORS","A node can have at most one anchor"),o.source.endsWith(":")&&r(o.offset+o.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),g=o,null===w&&(w=o.offset),c=!1,u=!1,m=!0;break;case"tag":$&&r(o,"MULTIPLE_TAGS","A node can have at most one tag"),$=o,null===w&&(w=o.offset),c=!1,u=!1,m=!0;break;case n:(g||$)&&r(o,"BAD_PROP_ORDER",`Anchors and tags must be after the ${o.source} indicator`),y&&r(o,"UNEXPECTED_TOKEN",`Unexpected ${o.source} in ${t??"collection"}`),y=o,c="seq-item-ind"===n||"explicit-key-ind"===n,u=!1;break;case"comma":if(t){b&&r(o,"UNEXPECTED_TOKEN",`Unexpected , in ${t}`),b=o,c=!1,u=!1;break}default:r(o,"UNEXPECTED_TOKEN",`Unexpected ${o.type} token`),c=!1,u=!1}const k=e[e.length-1],E=k?k.offset+k.source.length:o;return m&&s&&"space"!==s.type&&"newline"!==s.type&&"comma"!==s.type&&("scalar"!==s.type||""!==s.source)&&r(s.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),h&&(c&&h.indent<=a||"block-map"===(null==s?void 0:s.type)||"block-seq"===(null==s?void 0:s.type))&&r(h,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:b,found:y,spaceBefore:l,comment:d,hasNewline:p,anchor:g,tag:$,newlineAfterProp:v,end:E,start:w??E}}function Kt(e){if(!e)return null;switch(e.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(e.source.includes("\n"))return!0;if(e.end)for(const t of e.end)if("newline"===t.type)return!0;return!1;case"flow-collection":for(const t of e.items){for(const e of t.start)if("newline"===e.type)return!0;if(t.sep)for(const e of t.sep)if("newline"===e.type)return!0;if(Kt(t.key)||Kt(t.value))return!0}return!1;default:return!0}}function Pn(e,t,n){if("flow-collection"===(null==t?void 0:t.type)){const s=t.end[0];s.indent===e&&("]"===s.source||"}"===s.source)&&Kt(t)&&n(s,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}function sl(e,t,n){const{uniqueKeys:s}=e.options;if(!1===s)return!1;const o="function"==typeof s?s:(e,t)=>e===t||Nt(e)&&Nt(t)&&e.value===t.value;return t.some((e=>o(e.key,n)))}const js="All mapping items must start at the same column";function Ka({composeNode:e,composeEmptyNode:t},n,s,o,r){var a;const i=new((null==r?void 0:r.nodeClass)??_t)(n.schema);n.atRoot&&(n.atRoot=!1);let l=s.offset,c=null;for(const r of s.items){const{start:u,key:d,sep:f,value:p}=r,m=Lt(u,{indicator:"explicit-key-ind",next:d??(null==f?void 0:f[0]),offset:l,onError:o,parentIndent:s.indent,startOnNewline:!0}),h=!m.found;if(h){if(d&&("block-seq"===d.type?o(l,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in d&&d.indent!==s.indent&&o(l,"BAD_INDENT",js)),!m.anchor&&!m.tag&&!f){c=m.end,m.comment&&(i.comment?i.comment+="\n"+m.comment:i.comment=m.comment);continue}(m.newlineAfterProp||Kt(d))&&o(d??u[u.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else(null==(a=m.found)?void 0:a.indent)!==s.indent&&o(l,"BAD_INDENT",js);n.atKey=!0;const g=m.end,$=d?e(n,d,m,o):t(n,g,u,null,m,o);n.schema.compat&&Pn(s.indent,d,o),n.atKey=!1,sl(n,i.items,$)&&o(g,"DUPLICATE_KEY","Map keys must be unique");const v=Lt(f??[],{indicator:"map-value-ind",next:p,offset:$.range[2],onError:o,parentIndent:s.indent,startOnNewline:!d||"block-scalar"===d.type});if(l=v.end,v.found){h&&("block-map"===(null==p?void 0:p.type)&&!v.hasNewline&&o(l,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),n.options.strict&&m.start<v.found.offset-1024&&o($.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));const a=p?e(n,p,v,o):t(n,l,f,null,v,o);n.schema.compat&&Pn(s.indent,p,o),l=a.range[2];const c=new Rt($,a);n.options.keepSourceTokens&&(c.srcToken=r),i.items.push(c)}else{h&&o($.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),v.comment&&($.comment?$.comment+="\n"+v.comment:$.comment=v.comment);const e=new Rt($);n.options.keepSourceTokens&&(e.srcToken=r),i.items.push(e)}}return c&&c<l&&o(c,"IMPOSSIBLE","Map comment with trailing content"),i.range=[s.offset,l,c??l],i}function Ua({composeNode:e,composeEmptyNode:t},n,s,o,r){const a=new((null==r?void 0:r.nodeClass)??It)(n.schema);n.atRoot&&(n.atRoot=!1),n.atKey&&(n.atKey=!1);let i=s.offset,l=null;for(const{start:r,value:c}of s.items){const u=Lt(r,{indicator:"seq-item-ind",next:c,offset:i,onError:o,parentIndent:s.indent,startOnNewline:!0});if(!u.found){if(!(u.anchor||u.tag||c)){l=u.end,u.comment&&(a.comment=u.comment);continue}c&&"block-seq"===c.type?o(u.end,"BAD_INDENT","All sequence items must start at the same column"):o(i,"MISSING_CHAR","Sequence item without - indicator")}const d=c?e(n,c,u,o):t(n,u.end,r,null,u,o);n.schema.compat&&Pn(s.indent,c,o),i=d.range[2],a.items.push(d)}return a.range=[s.offset,i,l??i],a}const En="Block collections are not allowed within flow collections",Sn=e=>e&&("block-map"===e.type||"block-seq"===e.type);function Ga({composeNode:e,composeEmptyNode:t},n,s,o,r){const a="{"===s.start.source,i=a?"flow map":"flow sequence",l=new((null==r?void 0:r.nodeClass)??(a?_t:It))(n.schema);l.flow=!0;const c=n.atRoot;c&&(n.atRoot=!1),n.atKey&&(n.atKey=!1);let u=s.offset+s.start.source.length;for(let r=0;r<s.items.length;++r){const c=s.items[r],{start:d,key:f,sep:p,value:m}=c,h=Lt(d,{flow:i,indicator:"explicit-key-ind",next:f??(null==p?void 0:p[0]),offset:u,onError:o,parentIndent:s.indent,startOnNewline:!1});if(!h.found){if(!(h.anchor||h.tag||p||m)){0===r&&h.comma?o(h.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${i}`):r<s.items.length-1&&o(h.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${i}`),h.comment&&(l.comment?l.comment+="\n"+h.comment:l.comment=h.comment),u=h.end;continue}!a&&n.options.strict&&Kt(f)&&o(f,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(0===r)h.comma&&o(h.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${i}`);else if(h.comma||o(h.start,"MISSING_CHAR",`Missing , between ${i} items`),h.comment){let e="";e:for(const t of d)switch(t.type){case"comma":case"space":break;case"comment":e=t.source.substring(1);break e;default:break e}if(e){let t=l.items[l.items.length-1];Rn(t)&&(t=t.value??t.key),t.comment?t.comment+="\n"+e:t.comment=e,h.comment=h.comment.substring(e.length+1)}}if(a||p||h.found){n.atKey=!0;const r=h.end,g=f?e(n,f,h,o):t(n,r,d,null,h,o);Sn(f)&&o(g.range,"BLOCK_IN_FLOW",En),n.atKey=!1;const $=Lt(p??[],{flow:i,indicator:"map-value-ind",next:m,offset:g.range[2],onError:o,parentIndent:s.indent,startOnNewline:!1});if($.found){if(!a&&!h.found&&n.options.strict){if(p)for(const e of p){if(e===$.found)break;if("newline"===e.type){o(e,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}h.start<$.found.offset-1024&&o($.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else m&&("source"in m&&m.source&&":"===m.source[0]?o(m,"MISSING_CHAR",`Missing space after : in ${i}`):o($.start,"MISSING_CHAR",`Missing , or : between ${i} items`));const v=m?e(n,m,$,o):$.found?t(n,$.end,p,null,$,o):null;v?Sn(m)&&o(v.range,"BLOCK_IN_FLOW",En):$.comment&&(g.comment?g.comment+="\n"+$.comment:g.comment=$.comment);const b=new Rt(g,v);if(n.options.keepSourceTokens&&(b.srcToken=c),a){const e=l;sl(n,e.items,g)&&o(r,"DUPLICATE_KEY","Map keys must be unique"),e.items.push(b)}else{const e=new _t(n.schema);e.flow=!0,e.items.push(b);const t=(v??g).range;e.range=[g.range[0],t[1],t[2]],l.items.push(e)}u=v?v.range[2]:$.end}else{const s=m?e(n,m,h,o):t(n,h.end,p,null,h,o);l.items.push(s),u=s.range[2],Sn(m)&&o(s.range,"BLOCK_IN_FLOW",En)}}const d=a?"}":"]",[f,...p]=s.end;let m=u;if(f&&f.source===d)m=f.offset+f.source.length;else{const e=i[0].toUpperCase()+i.substring(1);o(u,c?"MISSING_CHAR":"BAD_INDENT",c?`${e} must end with a ${d}`:`${e} in block collection must be sufficiently indented and end with a ${d}`),f&&1!==f.source.length&&p.unshift(f)}if(p.length>0){const e=an(p,m,n.options.strict,o);e.comment&&(l.comment?l.comment+="\n"+e.comment:l.comment=e.comment),l.range=[s.offset,m,e.offset]}else l.range=[s.offset,m,m];return l}function In(e,t,n,s,o,r){const a="block-map"===n.type?Ka(e,t,n,s,r):"block-seq"===n.type?Ua(e,t,n,s,r):Ga(e,t,n,s,r),i=a.constructor;return"!"===o||o===i.tagName?(a.tag=i.tagName,a):(o&&(a.tag=o),a)}function Ya(e,t,n,s,o){var r;const a=s.tag,i=a?t.directives.tagName(a.source,(e=>o(a,"TAG_RESOLVE_FAILED",e))):null;if("block-seq"===n.type){const{anchor:e,newlineAfterProp:t}=s,n=e&&a?e.offset>a.offset?e:a:e??a;n&&(!t||t.offset<n.offset)&&o(n,"MISSING_CHAR","Missing newline after block sequence props")}const l="block-map"===n.type?"map":"block-seq"===n.type?"seq":"{"===n.start.source?"map":"seq";if(!a||!i||"!"===i||i===_t.tagName&&"map"===l||i===It.tagName&&"seq"===l)return In(e,t,n,o,i);let c=t.schema.tags.find((e=>e.tag===i&&e.collection===l));if(!c){const s=t.schema.knownTags[i];if(!s||s.collection!==l)return null!=s&&s.collection?o(a,"BAD_COLLECTION_TYPE",`${s.tag} used for ${l} collection, but expects ${s.collection}`,!0):o(a,"TAG_RESOLVE_FAILED",`Unresolved tag: ${i}`,!0),In(e,t,n,o,i);t.schema.tags.push(Object.assign({},s,{default:!1})),c=s}const u=In(e,t,n,o,i,c),d=(null==(r=c.resolve)?void 0:r.call(c,u,(e=>o(a,"TAG_RESOLVE_FAILED",e)),t.options))??u,f=on(d)?d:new Be(d);return f.range=u.range,f.tag=i,null!=c&&c.format&&(f.format=c.format),f}function il(e,t,n,s){const{value:o,type:r,comment:a,range:i}="block-scalar"===t.type?tr(e,t,s):nr(t,e.options.strict,s),l=n?e.directives.tagName(n.source,(e=>s(n,"TAG_RESOLVE_FAILED",e))):null;let c,u;c=e.options.stringKeys&&e.atKey?e.schema[bt]:l?Wa(e.schema,o,l,n,s):"scalar"===t.type?Ja(e,o,t,s):e.schema[bt];try{const r=c.resolve(o,(e=>s(n??t,"TAG_RESOLVE_FAILED",e)),e.options);u=Nt(r)?r:new Be(r)}catch(e){const r=e instanceof Error?e.message:String(e);s(n??t,"TAG_RESOLVE_FAILED",r),u=new Be(o)}return u.range=i,u.source=o,r&&(u.type=r),l&&(u.tag=l),c.format&&(u.format=c.format),a&&(u.comment=a),u}function Wa(e,t,n,s,o){var r;if("!"===n)return e[bt];const a=[];for(const t of e.tags)if(!t.collection&&t.tag===n){if(!t.default||!t.test)return t;a.push(t)}for(const e of a)if(null!=(r=e.test)&&r.test(t))return e;const i=e.knownTags[n];return i&&!i.collection?(e.tags.push(Object.assign({},i,{default:!1,test:void 0})),i):(o(s,"TAG_RESOLVE_FAILED",`Unresolved tag: ${n}`,"tag:yaml.org,2002:str"!==n),e[bt])}function Ja({atKey:e,directives:t,schema:n},s,o,r){const a=n.tags.find((t=>{var n;return(!0===t.default||e&&"key"===t.default)&&(null==(n=t.test)?void 0:n.test(s))}))||n[bt];if(n.compat){const e=n.compat.find((e=>{var t;return e.default&&(null==(t=e.test)?void 0:t.test(s))}))??n[bt];a.tag!==e.tag&&r(o,"TAG_RESOLVE_FAILED",`Value may be parsed as either ${t.tagString(a.tag)} or ${t.tagString(e.tag)}`,!0)}return a}function za(e,t,n){if(t){null===n&&(n=t.length);for(let s=n-1;s>=0;--s){let n=t[s];switch(n.type){case"space":case"comment":case"newline":e-=n.source.length;continue}for(n=t[++s];"space"===(null==n?void 0:n.type);)e+=n.source.length,n=t[++s];break}}return e}const Xa={composeNode:ll,composeEmptyNode:Qn};function ll(e,t,n,s){const o=e.atKey,{spaceBefore:r,comment:a,anchor:i,tag:l}=n;let c,u=!0;switch(t.type){case"alias":c=Za(e,t,s),(i||l)&&s(t,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":c=il(e,t,l,s),i&&(c.anchor=i.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":c=Ya(Xa,e,t,n,s),i&&(c.anchor=i.source.substring(1));break;default:s(t,"UNEXPECTED_TOKEN","error"===t.type?t.message:`Unsupported token (type: ${t.type})`),c=Qn(e,t.offset,void 0,null,n,s),u=!1}return i&&""===c.anchor&&s(i,"BAD_ALIAS","Anchor cannot be an empty string"),o&&e.options.stringKeys&&(!Nt(c)||"string"!=typeof c.value||c.tag&&"tag:yaml.org,2002:str"!==c.tag)&&s(l??t,"NON_STRING_KEY","With stringKeys, all keys must be strings"),r&&(c.spaceBefore=!0),a&&("scalar"===t.type&&""===t.source?c.comment=a:c.commentBefore=a),e.options.keepSourceTokens&&u&&(c.srcToken=t),c}function Qn(e,t,n,s,{spaceBefore:o,comment:r,anchor:a,tag:i,end:l},c){const u=il(e,{type:"scalar",offset:za(t,n,s),indent:-1,source:""},i,c);return a&&(u.anchor=a.source.substring(1),""===u.anchor&&c(a,"BAD_ALIAS","Anchor cannot be an empty string")),o&&(u.spaceBefore=!0),r&&(u.comment=r,u.range[2]=l),u}function Za({options:e},{offset:t,source:n,end:s},o){const r=new Hn(n.substring(1));""===r.source&&o(t,"BAD_ALIAS","Alias cannot be an empty string"),r.source.endsWith(":")&&o(t+n.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);const a=t+n.length,i=an(s,a,e.strict,o);return r.range=[t,a,i.offset],i.comment&&(r.comment=i.comment),r}function Qa(e,t,{offset:n,start:s,value:o,end:r},a){const i=Object.assign({_directives:t},e),l=new Vt(void 0,i),c={atKey:!1,atRoot:!0,directives:l.directives,options:l.options,schema:l.schema},u=Lt(s,{indicator:"doc-start",next:o??(null==r?void 0:r[0]),offset:n,onError:a,parentIndent:0,startOnNewline:!0});u.found&&(l.directives.docStart=!0,o&&("block-map"===o.type||"block-seq"===o.type)&&!u.hasNewline&&a(u.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),l.contents=o?ll(c,o,u,a):Qn(c,u.end,s,null,u,a);const d=l.contents.range[2],f=an(r,d,!1,a);return f.comment&&(l.comment=f.comment),l.range=[n,d,f.offset],l}function Ft(e){if("number"==typeof e)return[e,e+1];if(Array.isArray(e))return 2===e.length?e:[e[0],e[1]];const{offset:t,source:n}=e;return[t,t+("string"==typeof n?n.length:1)]}function Fs(e){var t;let n="",s=!1,o=!1;for(let r=0;r<e.length;++r){const a=e[r];switch(a[0]){case"#":n+=(""===n?"":o?"\n\n":"\n")+(a.substring(1)||" "),s=!0,o=!1;break;case"%":"#"!==(null==(t=e[r+1])?void 0:t[0])&&(r+=1),s=!1;break;default:s||(o=!0),s=!1}}return{comment:n,afterEmptyLine:o}}class $n{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(e,t,n,s)=>{const o=Ft(e);s?this.warnings.push(new ji(o,t,n)):this.errors.push(new Ct(o,t,n))},this.directives=new nn({version:e.version||"1.2"}),this.options=e}decorate(e,t){const{comment:n,afterEmptyLine:s}=Fs(this.prelude);if(n){const o=e.contents;if(t)e.comment=e.comment?`${e.comment}\n${n}`:n;else if(s||e.directives.docStart||!o)e.commentBefore=n;else if(ft(o)&&!o.flow&&o.items.length>0){let e=o.items[0];Rn(e)&&(e=e.key);const t=e.commentBefore;e.commentBefore=t?`${n}\n${t}`:n}else{const e=o.commentBefore;o.commentBefore=e?`${n}\n${e}`:n}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:Fs(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,n=-1){for(const t of e)yield*this.next(t);yield*this.end(t,n)}*next(e){switch(e.type){case"directive":this.directives.add(e.source,((t,n,s)=>{const o=Ft(e);o[0]+=t,this.onError(o,"BAD_DIRECTIVE",n,s)})),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{const t=Qa(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{const t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,n=new Ct(Ft(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(n):this.doc.errors.push(n);break}case"doc-end":{if(!this.doc){const t="Unexpected doc-end without preceding document";this.errors.push(new Ct(Ft(e),"UNEXPECTED_TOKEN",t));break}this.doc.directives.docEnd=!0;const t=an(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){const e=this.doc.comment;this.doc.comment=e?`${e}\n${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new Ct(Ft(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){const e=Object.assign({_directives:this.directives},this.options),n=new Vt(void 0,e);this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),n.range=[0,t,t],this.decorate(n,!1),yield n}}}class rl{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let t=0,n=this.lineStarts.length;for(;t<n;){const s=t+n>>1;this.lineStarts[s]<e?t=s+1:n=s}return this.lineStarts[t]===e?{line:t+1,col:1}:0===t?{line:0,col:e}:{line:t,col:e-this.lineStarts[t-1]+1}}}}function gt(e,t){for(let n=0;n<e.length;++n)if(e[n].type===t)return!0;return!1}function qs(e){for(let t=0;t<e.length;++t)switch(e[t].type){case"space":case"comment":case"newline":break;default:return t}return-1}function ol(e){switch(null==e?void 0:e.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function xt(e){switch(e.type){case"document":return e.start;case"block-map":{const t=e.items[e.items.length-1];return t.sep??t.start}case"block-seq":return e.items[e.items.length-1].start;default:return[]}}function wt(e){var t;if(0===e.length)return[];let n=e.length;e:for(;--n>=0;)switch(e[n].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;"space"===(null==(t=e[++n])?void 0:t.type););return e.splice(n,e.length)}function Bs(e){if("flow-seq-start"===e.start.type)for(const t of e.items)t.sep&&!t.value&&!gt(t.start,"explicit-key-ind")&&!gt(t.sep,"map-value-ind")&&(t.key&&(t.value=t.key),delete t.key,ol(t.value)?t.value.end?Array.prototype.push.apply(t.value.end,t.sep):t.value.end=t.sep:Array.prototype.push.apply(t.start,t.sep),delete t.sep)}class xn{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new Fi,this.onNewLine=e}*parse(e,t=!1){this.onNewLine&&0===this.offset&&this.onNewLine(0);for(const n of this.lexer.lex(e,t))yield*this.next(n);t||(yield*this.end())}*next(e){if(this.source=e,this.atScalar)return this.atScalar=!1,yield*this.step(),void(this.offset+=e.length);const t=sr(e);if(t)if("scalar"===t)this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=t,yield*this.step(),t){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&" "===e[0]&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{const t=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:t,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){const e=this.peek(1);if("doc-end"!==this.type||e&&"doc-end"===e.type){if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}else{for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source})}}peek(e){return this.stack[this.stack.length-e]}*pop(e){const t=e??this.stack.pop();if(t)if(0===this.stack.length)yield t;else{const e=this.peek(1);switch("block-scalar"===t.type?t.indent="indent"in e?e.indent:0:"flow-collection"===t.type&&"document"===e.type&&(t.indent=0),"flow-collection"===t.type&&Bs(t),e.type){case"document":e.value=t;break;case"block-scalar":e.props.push(t);break;case"block-map":{const n=e.items[e.items.length-1];if(n.value)return e.items.push({start:[],key:t,sep:[]}),void(this.onKeyLine=!0);if(!n.sep)return Object.assign(n,{key:t,sep:[]}),void(this.onKeyLine=!n.explicitKey);n.value=t;break}case"block-seq":{const n=e.items[e.items.length-1];n.value?e.items.push({start:[],value:t}):n.value=t;break}case"flow-collection":{const n=e.items[e.items.length-1];return void(!n||n.value?e.items.push({start:[],key:t,sep:[]}):n.sep?n.value=t:Object.assign(n,{key:t,sep:[]}))}default:yield*this.pop(),yield*this.pop(t)}if(!("document"!==e.type&&"block-map"!==e.type&&"block-seq"!==e.type||"block-map"!==t.type&&"block-seq"!==t.type)){const n=t.items[t.items.length-1];n&&!n.sep&&!n.value&&n.start.length>0&&-1===qs(n.start)&&(0===t.indent||n.start.every((e=>"comment"!==e.type||e.indent<t.indent)))&&("document"===e.type?e.end=n.start:e.items.push({start:n.start}),t.items.splice(-1,1))}}else yield{type:"error",offset:this.offset,source:"",message:"Tried to pop an empty stack"}}*stream(){switch(this.type){case"directive-line":return void(yield{type:"directive",offset:this.offset,source:this.source});case"byte-order-mark":case"space":case"comment":case"newline":return void(yield this.sourceToken);case"doc-mode":case"doc-start":{const e={type:"document",offset:this.offset,start:[]};return"doc-start"===this.type&&e.start.push(this.sourceToken),void this.stack.push(e)}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":return void(-1!==qs(e.start)?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken));case"anchor":case"tag":case"space":case"comment":case"newline":return void e.start.push(this.sourceToken)}const t=this.startBlockValue(e);t?this.stack.push(t):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if("map-value-ind"===this.type){const t=wt(xt(this.peek(2)));let n;e.end?(n=e.end,n.push(this.sourceToken),delete e.end):n=[this.sourceToken];const s={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:t,key:e,sep:n}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=s}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":return void e.props.push(this.sourceToken);case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let e=this.source.indexOf("\n")+1;for(;0!==e;)this.onNewLine(this.offset+e),e=this.source.indexOf("\n",e)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){var t;const n=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,n.value){const t="end"in n.value?n.value.end:void 0,s=Array.isArray(t)?t[t.length-1]:void 0;"comment"===(null==s?void 0:s.type)?null==t||t.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken);return;case"space":case"comment":if(n.value)e.items.push({start:[this.sourceToken]});else if(n.sep)n.sep.push(this.sourceToken);else{if(this.atIndentedComment(n.start,e.indent)){const s=e.items[e.items.length-2],o=null==(t=null==s?void 0:s.value)?void 0:t.end;if(Array.isArray(o))return Array.prototype.push.apply(o,n.start),o.push(this.sourceToken),void e.items.pop()}n.start.push(this.sourceToken)}return}if(this.indent>=e.indent){const t=!this.onKeyLine&&this.indent===e.indent,s=t&&(n.sep||n.explicitKey)&&"seq-item-ind"!==this.type;let o=[];if(s&&n.sep&&!n.value){const t=[];for(let s=0;s<n.sep.length;++s){const o=n.sep[s];switch(o.type){case"newline":t.push(s);break;case"space":break;case"comment":o.indent>e.indent&&(t.length=0);break;default:t.length=0}}t.length>=2&&(o=n.sep.splice(t[1]))}switch(this.type){case"anchor":case"tag":return void(s||n.value?(o.push(this.sourceToken),e.items.push({start:o}),this.onKeyLine=!0):n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken));case"explicit-key-ind":return n.sep||n.explicitKey?s||n.value?(o.push(this.sourceToken),e.items.push({start:o,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}):(n.start.push(this.sourceToken),n.explicitKey=!0),void(this.onKeyLine=!0);case"map-value-ind":if(n.explicitKey)if(n.sep)if(n.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(gt(n.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:o,key:null,sep:[this.sourceToken]}]});else if(ol(n.key)&&!gt(n.sep,"newline")){const e=wt(n.start),t=n.key,s=n.sep;s.push(this.sourceToken),delete n.key,delete n.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:e,key:t,sep:s}]})}else o.length>0?n.sep=n.sep.concat(o,this.sourceToken):n.sep.push(this.sourceToken);else if(gt(n.start,"newline"))Object.assign(n,{key:null,sep:[this.sourceToken]});else{const e=wt(n.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:e,key:null,sep:[this.sourceToken]}]})}else n.sep?n.value||s?e.items.push({start:o,key:null,sep:[this.sourceToken]}):gt(n.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):n.sep.push(this.sourceToken):Object.assign(n,{key:null,sep:[this.sourceToken]});return void(this.onKeyLine=!0);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const t=this.flowScalar(this.type);return void(s||n.value?(e.items.push({start:o,key:t,sep:[]}),this.onKeyLine=!0):n.sep?this.stack.push(t):(Object.assign(n,{key:t,sep:[]}),this.onKeyLine=!0))}default:{const n=this.startBlockValue(e);if(n)return t&&"block-seq"!==n.type&&e.items.push({start:o}),void this.stack.push(n)}}}yield*this.pop(),yield*this.step()}*blockSequence(e){var t;const n=e.items[e.items.length-1];switch(this.type){case"newline":if(n.value){const t="end"in n.value?n.value.end:void 0,s=Array.isArray(t)?t[t.length-1]:void 0;"comment"===(null==s?void 0:s.type)?null==t||t.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else n.start.push(this.sourceToken);return;case"space":case"comment":if(n.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(n.start,e.indent)){const s=e.items[e.items.length-2],o=null==(t=null==s?void 0:s.value)?void 0:t.end;if(Array.isArray(o))return Array.prototype.push.apply(o,n.start),o.push(this.sourceToken),void e.items.pop()}n.start.push(this.sourceToken)}return;case"anchor":case"tag":if(n.value||this.indent<=e.indent)break;return void n.start.push(this.sourceToken);case"seq-item-ind":if(this.indent!==e.indent)break;return void(n.value||gt(n.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):n.start.push(this.sourceToken))}if(this.indent>e.indent){const t=this.startBlockValue(e);if(t)return void this.stack.push(t)}yield*this.pop(),yield*this.step()}*flowCollection(e){const t=e.items[e.items.length-1];if("flow-error-end"===this.type){let e;do{yield*this.pop(),e=this.peek(1)}while(e&&"flow-collection"===e.type)}else if(0===e.end.length){switch(this.type){case"comma":case"explicit-key-ind":return void(!t||t.sep?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken));case"map-value-ind":return void(!t||t.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]}));case"space":case"comment":case"newline":case"anchor":case"tag":return void(!t||t.value?e.items.push({start:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken));case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const n=this.flowScalar(this.type);return void(!t||t.value?e.items.push({start:[],key:n,sep:[]}):t.sep?this.stack.push(n):Object.assign(t,{key:n,sep:[]}))}case"flow-map-end":case"flow-seq-end":return void e.end.push(this.sourceToken)}const n=this.startBlockValue(e);n?this.stack.push(n):(yield*this.pop(),yield*this.step())}else{const t=this.peek(2);if("block-map"===t.type&&("map-value-ind"===this.type&&t.indent===e.indent||"newline"===this.type&&!t.items[t.items.length-1].sep))yield*this.pop(),yield*this.step();else if("map-value-ind"===this.type&&"flow-collection"!==t.type){const n=wt(xt(t));Bs(e);const s=e.end.splice(1,e.end.length);s.push(this.sourceToken);const o={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:n,key:e,sep:s}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=o}else yield*this.lineEnd(e)}}flowScalar(e){if(this.onNewLine){let e=this.source.indexOf("\n")+1;for(;0!==e;)this.onNewLine(this.offset+e),e=this.source.indexOf("\n",e)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;const t=wt(xt(e));return t.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:t,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;const t=wt(xt(e));return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:t,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,t){return!("comment"!==this.type||this.indent<=t)&&e.every((e=>"newline"===e.type||"space"===e.type))}*documentEnd(e){"doc-mode"!==this.type&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],"newline"===this.type&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],"newline"===this.type&&(yield*this.pop())}}}function al(e){const t=!1!==e.prettyErrors;return{lineCounter:e.lineCounter||t&&new rl||null,prettyErrors:t}}function $a(e,t={}){const{lineCounter:n,prettyErrors:s}=al(t),o=new xn(null==n?void 0:n.addNewLine),r=new $n(t),a=Array.from(r.compose(o.parse(e)));if(s&&n)for(const t of a)t.errors.forEach(sn(e,n)),t.warnings.forEach(sn(e,n));return a.length>0?a:Object.assign([],{empty:!0},r.streamInfo())}function cl(e,t={}){const{lineCounter:n,prettyErrors:s}=al(t),o=new xn(null==n?void 0:n.addNewLine),r=new $n(t);let a=null;for(const t of r.compose(o.parse(e),!0,e.length))if(a){if("silent"!==a.options.logLevel){a.errors.push(new Ct(t.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}}else a=t;return s&&n&&(a.errors.forEach(sn(e,n)),a.warnings.forEach(sn(e,n))),a}function xa(e,t,n){let s;"function"==typeof t?s=t:void 0===n&&t&&"object"==typeof t&&(n=t);const o=cl(e,n);if(!o)return null;if(o.warnings.forEach((e=>ir(o.options.logLevel,e))),o.errors.length>0){if("silent"!==o.options.logLevel)throw o.errors[0];o.errors=[]}return o.toJS(Object.assign({reviver:s},n))}function ec(e,t,n){let s=null;if("function"==typeof t||Array.isArray(t)?s=t:void 0===n&&t&&(n=t),"string"==typeof n&&(n=n.length),"number"==typeof n){const e=Math.round(n);n=e<1?void 0:e>8?{indent:8}:{indent:e}}if(void 0===e){const{keepUndefined:e}=n??t??{};if(!e)return}return qi(e)&&!s?e.toString(n):new Vt(e,s,n).toString(n)}const Hs=Object.freeze(Object.defineProperty({__proto__:null,Alias:Hn,CST:lr,Composer:$n,Document:Vt,Lexer:Fi,LineCounter:rl,Pair:Rt,Parser:xn,Scalar:Be,Schema:pn,YAMLError:rr,YAMLMap:_t,YAMLParseError:Ct,YAMLSeq:It,YAMLWarning:ji,isAlias:or,isCollection:ft,isDocument:qi,isMap:Di,isNode:on,isPair:Rn,isScalar:Nt,isSeq:Mi,parse:xa,parseAllDocuments:$a,parseDocument:cl,stringify:ec,visit:ar,visitAsync:cr},Symbol.toStringTag,{value:"Module"}));function Rs(e,t,n){const s=e.slice();return s[23]=t[n],s}function Ks(e){let t,n,s;function o(t){e[15](t)}let r={disabled:e[6],required:e[0].required};return void 0!==e[4]&&(r.checked=e[4]),t=new Yn({props:r}),Ee.push((()=>Ge(t,"checked",o))),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,n){z(t,e,n),s=!0},p(e,s){const o={};64&s&&(o.disabled=e[6]),1&s&&(o.required=e[0].required),!n&&16&s&&(n=!0,o.checked=e[4],Ue((()=>n=!1))),t.$set(o)},i(e){s||(T(t.$$.fragment,e),s=!0)},o(e){P(t.$$.fragment,e),s=!1},d(e){J(t,e)}}}function tc(e){let t,n,s,o,r,a,i,l,c="Choose file";function u(e,t){return e[4]?oc:rc}let d=u(e),f=d(e);return{c(){t=w("label"),n=w("span"),n.textContent=c,s=B(),o=w("span"),f.c(),r=B(),a=w("input"),this.h()},l(e){t=k(e,"LABEL",{class:!0});var i=M(t);n=k(i,"SPAN",{class:!0,"data-svelte-h":!0}),"svelte-18m35yh"!==oe(n)&&(n.textContent=c),s=q(i),o=k(i,"SPAN",{class:!0});var l=M(o);f.l(l),l.forEach(p),r=q(i),a=k(i,"INPUT",{class:!0,type:!0}),i.forEach(p),this.h()},h(){E(n,"class","text-base-content"),E(o,"class","text-base-content-muted"),E(a,"class","hidden"),a.disabled=e[6],E(a,"type","file"),E(t,"class","flex justify-between items-center px-3 py-2 border border-base-300 rounded-md cursor-pointer hover:bg-base-200 transition-colors")},m(c,u){j(c,t,u),g(t,n),g(t,s),g(t,o),f.m(o,null),g(t,r),g(t,a),i||(l=ce(a,"change",e[8]),i=!0)},p(e,t){d===(d=u(e))&&f?f.p(e,t):(f.d(1),f=d(e),f&&(f.c(),f.m(o,null))),64&t&&(a.disabled=e[6])},d(e){e&&p(t),f.d(),i=!1,l()}}}function nc(e){let t,n,s,o,r,a=Ne(e[0].options),i=[];for(let t=0;t<a.length;t+=1)i[t]=Us(Rs(e,a,t));return{c(){t=w("select"),n=w("option");for(let e=0;e<i.length;e+=1)i[e].c();this.h()},l(e){t=k(e,"SELECT",{class:!0});var s=M(t);n=k(s,"OPTION",{}),M(n).forEach(p);for(let e=0;e<i.length;e+=1)i[e].l(s);s.forEach(p),this.h()},h(){n.disabled=s=e[0].required,n.__value=void 0,we(n,n.__value),t.disabled=e[6],E(t,"class",gn),void 0===e[4]&&ke((()=>e[20].call(t)))},m(s,a){j(s,t,a),g(t,n);for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,null);St(t,e[4],!0),o||(r=ce(t,"change",e[20]),o=!0)},p(e,o){if(1&o&&s!==(s=e[0].required)&&(n.disabled=s),1&o){let n;for(a=Ne(e[0].options),n=0;n<a.length;n+=1){const s=Rs(e,a,n);i[n]?i[n].p(s,o):(i[n]=Us(s),i[n].c(),i[n].m(t,null))}for(;n<i.length;n+=1)i[n].d(1);i.length=a.length}64&o&&(t.disabled=e[6]),17&o&&St(t,e[4])},d(e){e&&p(t),at(i,e),o=!1,r()}}}function sc(e){let t,n,s,o;return{c(){t=w("input"),this.h()},l(e){t=k(e,"INPUT",{class:!0,type:!0}),this.h()},h(){E(t,"class",gn),t.disabled=e[6],t.required=n=e[0].required,E(t,"type","number")},m(n,r){j(n,t,r),we(t,e[4]),s||(o=ce(t,"input",e[19]),s=!0)},p(e,s){64&s&&(t.disabled=e[6]),1&s&&n!==(n=e[0].required)&&(t.required=n),17&s&&ki(t.value)!==e[4]&&we(t,e[4])},d(e){e&&p(t),s=!1,o()}}}function ic(e){let t,n,s,o;return{c(){t=w("textarea"),this.h()},l(e){t=k(e,"TEXTAREA",{rows:!0}),M(t).forEach(p),this.h()},h(){t.disabled=e[6],t.required=n=e[0].required,E(t,"rows","5")},m(n,r){j(n,t,r),we(t,e[4]),s||(o=ce(t,"input",e[18]),s=!0)},p(e,s){64&s&&(t.disabled=e[6]),1&s&&n!==(n=e[0].required)&&(t.required=n),17&s&&we(t,e[4])},d(e){e&&p(t),s=!1,o()}}}function lc(e){let t;function n(e,t){return e[0].secret&&!e[2]&&!0!==e[0].shown?fc:uc}let s=n(e),o=s(e);return{c(){o.c(),t=fe()},l(e){o.l(e),t=fe()},m(e,n){o.m(e,n),j(e,t,n)},p(e,r){s===(s=n(e))&&o?o.p(e,r):(o.d(1),o=s(e),o&&(o.c(),o.m(t.parentNode,t)))},d(e){e&&p(t),o.d(e)}}}function rc(e){let t;return{c(){t=le("No file selected")},l(e){t=ie(e,"No file selected")},m(e,n){j(e,t,n)},p:re,d(e){e&&p(t)}}}function oc(e){let t,n=e[4].name+"";return{c(){t=le(n)},l(e){t=ie(e,n)},m(e,n){j(e,t,n)},p(e,s){16&s&&n!==(n=e[4].name+"")&&ye(t,n)},d(e){e&&p(t)}}}function ac(e){let t,n,s,o=e[23].label+"";return{c(){t=w("option"),n=le(o),this.h()},l(e){t=k(e,"OPTION",{});var s=M(t);n=ie(s,o),s.forEach(p),this.h()},h(){t.__value=s=e[23].value,we(t,t.__value)},m(e,s){j(e,t,s),g(t,n)},p(e,r){1&r&&o!==(o=e[23].label+"")&&ye(n,o),1&r&&s!==(s=e[23].value)&&(t.__value=s,we(t,t.__value))},d(e){e&&p(t)}}}function cc(e){let t,n,s,o=e[23]+"";return{c(){t=w("option"),n=le(o),this.h()},l(e){t=k(e,"OPTION",{});var s=M(t);n=ie(s,o),s.forEach(p),this.h()},h(){t.__value=s=e[23],we(t,t.__value)},m(e,s){j(e,t,s),g(t,n)},p(e,r){1&r&&o!==(o=e[23]+"")&&ye(n,o),1&r&&s!==(s=e[23])&&(t.__value=s,we(t,t.__value))},d(e){e&&p(t)}}}function Us(e){let t;function n(e,t){return"string"==typeof e[23]?cc:ac}let s=n(e),o=s(e);return{c(){o.c(),t=fe()},l(e){o.l(e),t=fe()},m(e,n){o.m(e,n),j(e,t,n)},p(e,r){s===(s=n(e))&&o?o.p(e,r):(o.d(1),o=s(e),o&&(o.c(),o.m(t.parentNode,t)))},d(e){e&&p(t),o.d(e)}}}function uc(e){let t,n,s,o;return{c(){t=w("input"),this.h()},l(e){t=k(e,"INPUT",{class:!0,type:!0}),this.h()},h(){E(t,"class",gn),t.disabled=e[6],t.required=n=e[0].required,E(t,"type","text")},m(n,r){j(n,t,r),we(t,e[4]),s||(o=ce(t,"input",e[17]),s=!0)},p(e,s){64&s&&(t.disabled=e[6]),1&s&&n!==(n=e[0].required)&&(t.required=n),17&s&&t.value!==e[4]&&we(t,e[4])},d(e){e&&p(t),s=!1,o()}}}function fc(e){let t,n,s,o;return{c(){t=w("input"),this.h()},l(e){t=k(e,"INPUT",{class:!0,type:!0}),this.h()},h(){E(t,"class",gn),t.disabled=e[6],t.required=n=e[0].required,E(t,"type","password")},m(n,r){j(n,t,r),we(t,e[4]),s||(o=ce(t,"input",e[16]),s=!0)},p(e,s){64&s&&(t.disabled=e[6]),1&s&&n!==(n=e[0].required)&&(t.required=n),17&s&&t.value!==e[4]&&we(t,e[4])},d(e){e&&p(t),s=!1,o()}}}function Gs(e){let t,n,s=e[0].description+"";return{c(){t=w("p"),n=le(s),this.h()},l(e){t=k(e,"P",{class:!0});var o=M(t);n=ie(o,s),o.forEach(p),this.h()},h(){E(t,"class","text-xs text-base-content-muted break-words max-w-2xl")},m(e,s){j(e,t,s),g(t,n)},p(e,t){1&t&&s!==(s=e[0].description+"")&&ye(n,s)},d(e){e&&p(t)}}}function Ys(e){var t;let n,s,o,r,a;function i(t){e[21](t)}let l={rootOptions:e[1],reveal:e[2],disabled:e[6],optionSpec:null==(t=e[0].children)?void 0:t[e[4]]};return void 0!==e[3]&&(l.options=e[3]),s=new ul({props:l}),Ee.push((()=>Ge(s,"options",i))),{c(){n=w("div"),Z(s.$$.fragment),this.h()},l(e){n=k(e,"DIV",{class:!0});var t=M(n);X(s.$$.fragment,t),t.forEach(p),this.h()},h(){E(n,"class","pl-4 border-l-2 mt-2 border-base-200")},m(e,t){j(e,n,t),z(s,n,null),a=!0},p(e,t){var n;const r={};2&t&&(r.rootOptions=e[1]),4&t&&(r.reveal=e[2]),64&t&&(r.disabled=e[6]),17&t&&(r.optionSpec=null==(n=e[0].children)?void 0:n[e[4]]),!o&&8&t&&(o=!0,r.options=e[3],Ue((()=>o=!1))),s.$set(r)},i(e){a||(T(s.$$.fragment,e),e&&ke((()=>{a&&(r||(r=nt(n,dt,{},!0)),r.run(1))})),a=!0)},o(e){P(s.$$.fragment,e),e&&(r||(r=nt(n,dt,{},!1)),r.run(0)),a=!1},d(e){e&&p(n),J(s),e&&r&&r.end()}}}function dc(e){var t,n;let s,o,r,a,i,l,c,u,d,f,m,h,$,v=Object.keys((null==(n=null==(t=e[0])?void 0:t.children)?void 0:n[e[4]])??{}).length,b="boolean"===e[0].type&&Ks(e);function y(e,t){return 1&t&&(c=null),"string"===e[0].type?lc:"multiline"===e[0].type?ic:"number"===e[0].type?sc:(null==c&&(c=!("select"!==e[0].type||!Array.isArray(e[0].options))),c?nc:"file"===e[0].type?tc:void 0)}let x=y(e,-1),I=x&&x(e),S=e[0].description&&Gs(e),C=v&&Ys(e);return{c(){s=w("div"),o=w("label"),r=w("span"),a=le(e[7]),i=B(),b&&b.c(),l=B(),I&&I.c(),u=B(),d=w("p"),f=le(e[5]),m=B(),S&&S.c(),h=B(),C&&C.c(),this.h()},l(t){s=k(t,"DIV",{class:!0});var n=M(s);o=k(n,"LABEL",{class:!0});var c=M(o);r=k(c,"SPAN",{class:!0});var g=M(r);a=ie(g,e[7]),i=q(g),b&&b.l(g),g.forEach(p),l=q(c),I&&I.l(c),u=q(c),d=k(c,"P",{class:!0});var $=M(d);f=ie($,e[5]),$.forEach(p),c.forEach(p),m=q(n),S&&S.l(n),h=q(n),C&&C.l(n),n.forEach(p),this.h()},h(){E(r,"class","text-sm font-medium flex justify-between items-center"),E(d,"class","text-negative text-xs"),E(o,"class","flex flex-col gap-2"),E(s,"class","w-full mb-2")},m(e,t){j(e,s,t),g(s,o),g(o,r),g(r,a),g(r,i),b&&b.m(r,null),g(o,l),I&&I.m(o,null),g(o,u),g(o,d),g(d,f),g(s,m),S&&S.m(s,null),g(s,h),C&&C.m(s,null),$=!0},p(e,[t]){var n,i;(!$||128&t)&&ye(a,e[7]),"boolean"===e[0].type?b?(b.p(e,t),1&t&&T(b,1)):(b=Ks(e),b.c(),T(b,1),b.m(r,null)):b&&(pe(),P(b,1,1,(()=>{b=null})),ge()),x===(x=y(e,t))&&I?I.p(e,t):(I&&I.d(1),I=x&&x(e),I&&(I.c(),I.m(o,u))),(!$||32&t)&&ye(f,e[5]),e[0].description?S?S.p(e,t):(S=Gs(e),S.c(),S.m(s,h)):S&&(S.d(1),S=null),17&t&&(v=Object.keys((null==(i=null==(n=e[0])?void 0:n.children)?void 0:i[e[4]])??{}).length),v?C?(C.p(e,t),17&t&&T(C,1)):(C=Ys(e),C.c(),T(C,1),C.m(s,null)):C&&(pe(),P(C,1,1,(()=>{C=null})),ge())},i(e){$||(T(b),T(C),$=!0)},o(e){P(b),P(C),$=!1},d(e){e&&p(s),b&&b.d(),I&&I.d(),S&&S.d(),C&&C.d()}}}const gn="rounded-md border border-base-300 bg-base-100 shadow-sm px-2 py-1 text-sm focus-visible:ring-base-300 flex h-9 w-full transition-colors focus-visible:outline-none focus-visible:ring-1 p-1 ml-auto align-middle";function mc(e,t,n){let s,o,r,{spec:a}=t,{key:i}=t,{options:l}=t,{disabled:c}=t,{rootOptions:u}=t,{reveal:d}=t;const f=`_${i}`;let p,m;a.children?a.nest?(p=f,m=l[i]??{}):(p=i,m=l):(p=i,m={});let h=l[p],g={},$="";return e.$$set=e=>{"spec"in e&&n(0,a=e.spec),"key"in e&&n(10,i=e.key),"options"in e&&n(9,l=e.options),"disabled"in e&&n(11,c=e.disabled),"rootOptions"in e&&n(1,u=e.rootOptions),"reveal"in e&&n(2,d=e.reveal)},e.$$.update=()=>{var t,d,$;if(1025&e.$$.dirty&&n(7,s=a.title??i),3&e.$$.dirty&&n(14,o=a.references?ur.query(u,a.references):null),16384&e.$$.dirty&&null!=o&&o.length&&n(4,h=o[0]),13849&e.$$.dirty)if(null!=a&&a.children&&!Object.keys(a.children[h]??{}).length){if(n(9,l[i]=h,l),delete l[f],"object"==typeof m)for(const e of Object.keys(g))delete m[e];else console.warn(`child_value_target was unexpectedly not an object ${m}`,{key:i,options:l,spec:a});n(13,g=(null==(t=null==a?void 0:a.children)?void 0:t[h])??{})}else null!=(d=null==a?void 0:a.children)&&d[h]&&(a.nest?(n(12,p=f),"object"!=typeof l[i]&&n(9,l[i]={},l),n(3,m=l[i])):(n(12,p=i),n(3,m=l)),n(9,l[p]=h,l),n(13,g=(null==($=null==a?void 0:a.children)?void 0:$[h])??{}));4112&e.$$.dirty&&n(9,l[p]=h,l),18433&e.$$.dirty&&n(6,r=c||a.forceReference||a.references&&null!==o)},[a,u,d,m,h,$,r,s,async function(e){if(!e.target)return;const{files:t}=e.target;if(!t)return;const[s]=t;switch(a.fileFormat){case"json":try{n(9,l[p]=await s.text().then((e=>JSON.parse(e))),l)}catch(e){n(5,$="Failed to parse YAML file"),console.warn(e)}break;case"yaml":try{n(9,l[p]=await s.text().then((e=>Hs.parse(e))),l)}catch(e){n(5,$="Failed to parse JSON file"),console.warn(e)}break;default:{const e=await s.text();try{n(9,l[p]=JSON.parse(e),l);break}catch{}try{n(9,l[p]=Hs.parse(e),l);break}catch{}n(9,l[p]=await s.text(),l);break}}},l,i,c,p,g,o,function(e){h=e,n(4,h),n(14,o),n(0,a),n(1,u)},function(){h=this.value,n(4,h),n(14,o),n(0,a),n(1,u),n(0,a)},function(){h=this.value,n(4,h),n(14,o),n(0,a),n(1,u),n(0,a)},function(){h=this.value,n(4,h),n(14,o),n(0,a),n(1,u),n(0,a)},function(){h=ki(this.value),n(4,h),n(14,o),n(0,a),n(1,u),n(0,a)},function(){h=jn(this),n(4,h),n(14,o),n(0,a),n(1,u),n(0,a)},function(e){m=e,n(3,m),n(0,a),n(4,h),n(10,i),n(9,l),n(13,g),n(12,p),n(14,o),n(1,u)}]}class hc extends be{constructor(e){super(),ve(this,e,mc,dc,_e,{spec:0,key:10,options:9,disabled:11,rootOptions:1,reveal:2})}}function Ws(e,t,n){const s=e.slice();return s[6]=t[n][0],s[7]=t[n][1],s}function Js(e){let t,n,s;function o(t){e[5](t)}let r={reveal:e[4],disabled:e[2],key:e[6],spec:e[7],rootOptions:e[3]};return void 0!==e[0]&&(r.options=e[0]),t=new hc({props:r}),Ee.push((()=>Ge(t,"options",o))),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,n){z(t,e,n),s=!0},p(e,s){const o={};16&s&&(o.reveal=e[4]),4&s&&(o.disabled=e[2]),2&s&&(o.key=e[6]),2&s&&(o.spec=e[7]),8&s&&(o.rootOptions=e[3]),!n&&1&s&&(n=!0,o.options=e[0],Ue((()=>n=!1))),t.$set(o)},i(e){s||(T(t.$$.fragment,e),s=!0)},o(e){P(t.$$.fragment,e),s=!1},d(e){J(t,e)}}}function pc(e){let t,n,s=Ne(Object.entries(e[1])),o=[];for(let t=0;t<s.length;t+=1)o[t]=Js(Ws(e,s,t));const r=e=>P(o[e],1,1,(()=>{o[e]=null}));return{c(){for(let e=0;e<o.length;e+=1)o[e].c();t=fe()},l(e){for(let t=0;t<o.length;t+=1)o[t].l(e);t=fe()},m(e,s){for(let t=0;t<o.length;t+=1)o[t]&&o[t].m(e,s);j(e,t,s),n=!0},p(e,[n]){if(31&n){let a;for(s=Ne(Object.entries(e[1])),a=0;a<s.length;a+=1){const r=Ws(e,s,a);o[a]?(o[a].p(r,n),T(o[a],1)):(o[a]=Js(r),o[a].c(),T(o[a],1),o[a].m(t.parentNode,t))}for(pe(),a=s.length;a<o.length;a+=1)r(a);ge()}},i(e){if(!n){for(let e=0;e<s.length;e+=1)T(o[e]);n=!0}},o(e){o=o.filter(Boolean);for(let e=0;e<o.length;e+=1)P(o[e]);n=!1},d(e){e&&p(t),at(o,e)}}}function gc(e,t,n){let{optionSpec:s}=t,{options:o={}}=t,{disabled:r=!1}=t,{rootOptions:a=o}=t,{reveal:i}=t;for(const[e,t]of Object.entries(s))t.default&&!o[e]&&(o[e]=t.default);return e.$$set=e=>{"optionSpec"in e&&n(1,s=e.optionSpec),"options"in e&&n(0,o=e.options),"disabled"in e&&n(2,r=e.disabled),"rootOptions"in e&&n(3,a=e.rootOptions),"reveal"in e&&n(4,i=e.reveal)},[o,s,r,a,i,function(e){o=e,n(0,o)}]}class ul extends be{constructor(e){super(),ve(this,e,gc,pc,_e,{optionSpec:1,options:0,disabled:2,rootOptions:3,reveal:4})}}function zs(e){let t,n,s,o;function r(t){e[14](t)}function a(t){e[15](t)}let i={};return void 0!==e[0].name&&(i.sourceName=e[0].name),void 0!==e[10]&&(i.nameError=e[10]),t=new Ui({props:i}),Ee.push((()=>Ge(t,"sourceName",r))),Ee.push((()=>Ge(t,"nameError",a))),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,n){z(t,e,n),o=!0},p(e,o){const r={};!n&&1&o&&(n=!0,r.sourceName=e[0].name,Ue((()=>n=!1))),!s&&1024&o&&(s=!0,r.nameError=e[10],Ue((()=>s=!1))),t.$set(r)},i(e){o||(T(t.$$.fragment,e),o=!0)},o(e){P(t.$$.fragment,e),o=!1},d(e){J(t,e)}}}function Xs(e){var t;let n,s,o;function r(t){e[16](t)}let a={reveal:e[3],disabled:e[5]||e[9],rootOptions:e[0].options,optionSpec:null==(t=e[1])?void 0:t.options};return void 0!==e[0].options&&(a.options=e[0].options),n=new ul({props:a}),Ee.push((()=>Ge(n,"options",r))),{c(){Z(n.$$.fragment)},l(e){X(n.$$.fragment,e)},m(e,t){z(n,e,t),o=!0},p(e,t){var o;const r={};8&t&&(r.reveal=e[3]),544&t&&(r.disabled=e[5]||e[9]),1&t&&(r.rootOptions=e[0].options),2&t&&(r.optionSpec=null==(o=e[1])?void 0:o.options),!s&&1&t&&(s=!0,r.options=e[0].options,Ue((()=>s=!1))),n.$set(r)},i(e){o||(T(n.$$.fragment,e),o=!0)},o(e){P(n.$$.fragment,e),o=!1},d(e){J(n,e)}}}function Zs(e){var t;let n,s,o,r,a,i;function l(t){e[17](t)}let c={id:"reveal-switch-"+(null==(t=e[0])?void 0:t.name)};return void 0!==e[3]&&(c.checked=e[3]),o=new Yn({props:c}),Ee.push((()=>Ge(o,"checked",l))),{c(){n=w("label"),s=le("Show Hidden Values\n\t\t\t\t"),Z(o.$$.fragment),this.h()},l(e){n=k(e,"LABEL",{for:!0,class:!0});var t=M(n);s=ie(t,"Show Hidden Values\n\t\t\t\t"),X(o.$$.fragment,t),t.forEach(p),this.h()},h(){var t;E(n,"for",a="reveal-switch-"+(null==(t=e[0])?void 0:t.name)),E(n,"class","flex gap-2 items-center pt-4 border-t border-base-200")},m(e,t){j(e,n,t),g(n,s),z(o,n,null),i=!0},p(e,t){var s,l;const c={};1&t&&(c.id="reveal-switch-"+(null==(s=e[0])?void 0:s.name)),!r&&8&t&&(r=!0,c.checked=e[3],Ue((()=>r=!1))),o.$set(c),(!i||1&t&&a!==(a="reveal-switch-"+(null==(l=e[0])?void 0:l.name)))&&E(n,"for",a)},i(e){i||(T(o.$$.fragment,e),i=!0)},o(e){P(o.$$.fragment,e),i=!1},d(e){e&&p(n),J(o)}}}function _c(e){let t,n,s='<div class="h-2 w-2 bg-positive rounded-full inline-flex items-center justify-center animate-pulse"><div class="h-2 w-2 rounded-full bg-positive"></div></div> <p class="text-base-content-muted font-medium text-xs">Connected</p>';return{c(){t=w("div"),t.innerHTML=s,this.h()},l(e){t=k(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-rze512"!==oe(t)&&(t.innerHTML=s),this.h()},h(){E(t,"class","flex gap-2 items-center")},m(e,n){j(e,t,n)},p:re,i(e){e&&(n||ke((()=>{n=Ae(t,Xe,{}),n.start()})))},o:re,d(e){e&&p(t)}}}function bc(e){let t,n,s;return{c(){t=w("p"),n=le(e[8]),this.h()},l(s){t=k(s,"P",{class:!0});var o=M(t);n=ie(o,e[8]),o.forEach(p),this.h()},h(){E(t,"class","text-negative text-xs max-w-md break-words")},m(e,s){j(e,t,s),g(t,n)},p(e,t){256&t&&ye(n,e[8])},i(e){e&&(s||ke((()=>{s=Ae(t,Xe,{}),s.start()})))},o:re,d(e){e&&p(t)}}}function vc(e){let t,n,s;return{c(){t=w("p"),n=le(e[4]),this.h()},l(s){t=k(s,"P",{class:!0});var o=M(t);n=ie(o,e[4]),o.forEach(p),this.h()},h(){E(t,"class","text-negative text-xs max-w-md break-words")},m(e,s){j(e,t,s),g(t,n)},p(e,t){16&t&&ye(n,e[4])},i(e){e&&(s||ke((()=>{s=Ae(t,Xe,{}),s.start()})))},o:re,d(e){e&&p(t)}}}function Qs(e){let t,n;return t=new We({props:{variant:"ghost",type:"button",$$slots:{default:[yc]},$$scope:{ctx:e}}}),t.$on("click",e[11]),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};524288&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function yc(e){let t;return{c(){t=le("Back")},l(e){t=ie(e,"Back")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function kc(e){let t,n,s,o;return n=new We({props:{variant:"primary",disabled:e[5]||e[9],class:e[9]?"animate-pulse":"w-32",type:"submit",$$slots:{default:[Tc]},$$scope:{ctx:e}}}),{c(){t=w("div"),Z(n.$$.fragment)},l(e){t=k(e,"DIV",{});var s=M(t);X(n.$$.fragment,s),s.forEach(p)},m(e,s){j(e,t,s),z(n,t,null),o=!0},p(e,t){const s={};544&t&&(s.disabled=e[5]||e[9]),512&t&&(s.class=e[9]?"animate-pulse":"w-32"),524288&t&&(s.$$scope={dirty:t,ctx:e}),n.$set(s)},i(e){o||(T(n.$$.fragment,e),e&&(s||ke((()=>{s=Ae(t,Xe,{}),s.start()}))),o=!0)},o(e){P(n.$$.fragment,e),o=!1},d(e){e&&p(t),J(n)}}}function wc(e){let t,n;return t=new We({props:{variant:"primary",formaction:"?/testSource",disabled:e[9]||e[5],class:e[9]?"animate-pulse w-32":"w-32",$$slots:{default:[Ec]},$$scope:{ctx:e}}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};544&n&&(s.disabled=e[9]||e[5]),512&n&&(s.class=e[9]?"animate-pulse w-32":"w-32"),524288&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Cc(e){let t,n;return t=new We({props:{variant:"primary",disabled:e[5]||e[9],class:e[9]?"animate-pulse":"w-full",type:"submit",$$slots:{default:[Sc]},$$scope:{ctx:e}}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};544&n&&(s.disabled=e[5]||e[9]),512&n&&(s.class=e[9]?"animate-pulse":"w-full"),524288&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Tc(e){let t;return{c(){t=le("Save")},l(e){t=ie(e,"Save")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function Ec(e){let t;return{c(){t=le("Test Configuration")},l(e){t=ie(e,"Test Configuration")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function Sc(e){let t;return{c(){t=le("Save")},l(e){t=ie(e,"Save")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function Ic(e){var t,n;let s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S,C,N,A,O=Object.keys(null==(t=e[1])?void 0:t.options).length,_=Dn(null==(n=e[1])?void 0:n.options),L=!e[2]&&zs(e),D=O&&Xs(e),V=_&&Zs(e);function J(e,t){return e[4]?vc:e[8]?bc:e[6]?_c:void 0}let F=J(e),H=F&&F(e),X=e[2]&&Qs(e);const z=[Cc,wc,kc],K=[];function U(e,t){var n;return 193&t&&(x=null),e[2]&&"duckdb"===(null==(n=e[0])?void 0:n.type)?0:(null==x&&(x=!e[6]||JSON.stringify(e[0])!==e[7]),x?1:2)}return I=U(e,-1),S=K[I]=z[I](e),{c(){s=w("form"),o=w("section"),L&&L.c(),r=B(),a=w("input"),l=B(),D&&D.c(),c=B(),V&&V.c(),u=B(),d=w("input"),m=B(),h=w("div"),$=w("div"),H&&H.c(),v=B(),b=w("div"),X&&X.c(),y=B(),S.c(),this.h()},l(e){s=k(e,"FORM",{action:!0,method:!0,class:!0});var t=M(s);o=k(t,"SECTION",{class:!0});var n=M(o);L&&L.l(n),r=q(n),a=k(n,"INPUT",{type:!0,class:!0}),l=q(n),D&&D.l(n),c=q(n),V&&V.l(n),n.forEach(p),u=q(t),d=k(t,"INPUT",{type:!0,name:!0}),m=q(t),h=k(t,"DIV",{class:!0});var i=M(h);$=k(i,"DIV",{});var f=M($);H&&H.l(f),f.forEach(p),v=q(i),b=k(i,"DIV",{class:!0});var g=M(b);X&&X.l(g),y=q(g),S.l(g),g.forEach(p),i.forEach(p),t.forEach(p),this.h()},h(){E(a,"type","hidden"),a.disabled=!0,a.value=i=e[0].type,E(a,"class","rounded border border-base-300 p-1 ml-auto w-2/3 bg-base-100 align-middle text-sm"),E(o,"class","flex flex-col gap-4"),E(d,"type","hidden"),d.value=f=JSON.stringify({...e[0],dir:`sources/${e[0].name}`}),E(d,"name","source"),E(b,"class","flex gap-2 justify-end items-center pt-1"),E(h,"class","flex justify-between items-center pt-4"),E(s,"action","?/updateSource"),E(s,"method","POST"),E(s,"class","w-full flex flex-col gap-8 px-1 pt-8 text-sm")},m(t,n){j(t,s,n),g(s,o),L&&L.m(o,null),g(o,r),g(o,a),g(o,l),D&&D.m(o,null),g(o,c),V&&V.m(o,null),g(s,u),g(s,d),g(s,m),g(s,h),g(h,$),H&&H.m($,null),g(h,v),g(h,b),X&&X.m(b,null),g(b,y),K[I].m(b,null),C=!0,N||(A=Qe(va.call(null,s,e[12])),N=!0)},p(e,[t]){var n,s;e[2]?L&&(pe(),P(L,1,1,(()=>{L=null})),ge()):L?(L.p(e,t),4&t&&T(L,1)):(L=zs(e),L.c(),T(L,1),L.m(o,r)),(!C||1&t&&i!==(i=e[0].type))&&(a.value=i),2&t&&(O=Object.keys(null==(n=e[1])?void 0:n.options).length),O?D?(D.p(e,t),2&t&&T(D,1)):(D=Xs(e),D.c(),T(D,1),D.m(o,c)):D&&(pe(),P(D,1,1,(()=>{D=null})),ge()),2&t&&(_=Dn(null==(s=e[1])?void 0:s.options)),_?V?(V.p(e,t),2&t&&T(V,1)):(V=Zs(e),V.c(),T(V,1),V.m(o,null)):V&&(pe(),P(V,1,1,(()=>{V=null})),ge()),(!C||1&t&&f!==(f=JSON.stringify({...e[0],dir:`sources/${e[0].name}`})))&&(d.value=f),F===(F=J(e))&&H?H.p(e,t):(H&&H.d(1),H=F&&F(e),H&&(H.c(),T(H,1),H.m($,null))),e[2]?X?(X.p(e,t),4&t&&T(X,1)):(X=Qs(e),X.c(),T(X,1),X.m(b,y)):X&&(pe(),P(X,1,1,(()=>{X=null})),ge());let l=I;I=U(e,t),I===l?K[I].p(e,t):(pe(),P(K[l],1,1,(()=>{K[l]=null})),ge(),S=K[I],S?S.p(e,t):(S=K[I]=z[I](e),S.c()),T(S,1),S.m(b,null))},i(e){C||(T(L),T(D),T(V),T(H),T(X),T(S),C=!0)},o(e){P(L),P(D),P(V),P(X),P(S),C=!1},d(e){e&&p(s),L&&L.d(),D&&D.d(),V&&V.d(),H&&H.d(),X&&X.d(),K[I].d(),N=!1,A()}}}function Dn(e){return!!e&&Object.values(e).some((e=>!!e.secret||!!e.children&&Object.values(e.children).some((e=>Dn({[e.title]:e})))))}function Nc(e,t,n){let{sourcePlugin:s}=t,{isNewSource:o=!1}=t,{source:r}=t,{sources:a}=t;const i=wi();let l;r.initialName=r.name;let c="",u=!1,d=!1,f="",p="",m=!1,h="";return e.$$set=e=>{"sourcePlugin"in e&&n(1,s=e.sourcePlugin),"isNewSource"in e&&n(2,o=e.isNewSource),"source"in e&&n(0,r=e.source),"sources"in e&&n(13,a=e.sources)},[r,s,o,l,c,u,d,f,p,m,h,function(){i("cancel")},({action:e,cancel:t})=>{if(n(5,u=!1),n(9,m=!1),o||(n(10,h=Ki(r.name,a.filter((e=>e!==r)))),!h)){switch(e.search){case"?/updateSource":n(5,u=!0),n(4,c=""),n(6,d=!1);break;case"?/testSource":n(9,m=!0),n(8,p="")}return({result:e,action:t})=>{var s;if("failure"===e.type){if("string"==typeof e.data)n(4,c=e.data);else if("object"==typeof e.data&&"message"in e.data)switch(t.search){case"?/updateSource":n(4,c=e.data.message);break;case"?/testSource":n(8,p=e.data.message)}else n(4,c="Error saving datasource.");return n(5,u=!1),n(6,d=!1),void n(9,m=!1)}switch(t.search){case"?/updateSource":"success"===e.type&&Object.assign(r,null==(s=e.data)?void 0:s.updatedSource),n(5,u=!1),n(6,d=!0),i("sourceUpdated",r);break;case"?/testSource":"success"===e.type&&(n(8,p=""),n(6,d=!0),n(7,f=JSON.stringify(r))),n(9,m=!1)}}}t()},a,function(t){e.$$.not_equal(r.name,t)&&(r.name=t,n(0,r))},function(e){h=e,n(10,h)},function(t){e.$$.not_equal(r.options,t)&&(r.options=t,n(0,r))},function(e){l=e,n(3,l)}]}class fl extends be{constructor(e){super(),ve(this,e,Nc,Ic,_e,{sourcePlugin:1,isNewSource:2,source:0,sources:13})}}function $s(e,t,n){const s=e.slice();s[24]=t[n][0],s[25]=t[n][1];const o=s[25].package.package.evidence.datasources;return s[26]=o,s}function xs(e,t,n){const s=e.slice();return s[29]=t[n],s}function Ac(e){const t=e.slice(),n=t[25].package.package.evidence.icon;return t[32]=n,t}function Oc(e){const t=e.slice(),n=t[25].package.package.evidence.icon;return t[32]=n,t}function Lc(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S=e[6].type+"",C=e[6].name+"";const N=[jc,Vc,Mc],A=[];function O(e,t){return 1024&t[0]&&(s=null),1024&t[0]&&(o=null),null==s&&(s=!!e[14](e[10])),s?0:(null==o&&(o=!!e[15](e[10])),o?1:2)}return r=O(e,[-1,-1]),a=A[r]=N[r](e),y=new fl({props:{sources:e[3],sourcePlugin:e[0],source:e[6],isNewSource:!0}}),y.$on("sourceUpdated",e[21]),y.$on("cancel",e[22]),{c(){t=w("div"),n=w("div"),a.c(),i=B(),l=w("div"),c=w("div"),u=w("div"),d=w("p"),f=le(S),m=B(),h=w("h4"),$=le(C),v=B(),b=w("div"),Z(y.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0});var s=M(t);n=k(s,"DIV",{class:!0});var o=M(n);a.l(o),o.forEach(p),i=q(s),l=k(s,"DIV",{class:!0});var r=M(l);c=k(r,"DIV",{class:!0});var g=M(c);u=k(g,"DIV",{class:!0});var w=M(u);d=k(w,"P",{class:!0});var E=M(d);f=ie(E,S),E.forEach(p),m=q(w),h=k(w,"H4",{class:!0});var x=M(h);$=ie(x,C),x.forEach(p),w.forEach(p),g.forEach(p),r.forEach(p),s.forEach(p),v=q(e),b=k(e,"DIV",{});var T=M(b);X(y.$$.fragment,T),T.forEach(p),this.h()},h(){E(n,"class","text-base-content h-full"),E(d,"class","text-base-content-muted font-mono text-xs"),E(h,"class","text-base-content font-medium"),E(u,"class","flex flex-col text-sm"),E(c,"class","flex items-center text-base-content gap-4"),E(l,"class","flex w-full justify-between items-center"),E(t,"class","flex items-center gap-4")},m(e,s){j(e,t,s),g(t,n),A[r].m(n,null),g(t,i),g(t,l),g(l,c),g(c,u),g(u,d),g(d,f),g(u,m),g(u,h),g(h,$),j(e,v,s),j(e,b,s),z(y,b,null),I=!0},p(e,t){let s=r;r=O(e,t),r===s?A[r].p(e,t):(pe(),P(A[s],1,1,(()=>{A[s]=null})),ge(),a=A[r],a?a.p(e,t):(a=A[r]=N[r](e),a.c()),T(a,1),a.m(n,null)),(!I||64&t[0])&&S!==(S=e[6].type+"")&&ye(f,S),(!I||64&t[0])&&C!==(C=e[6].name+"")&&ye($,C);const o={};8&t[0]&&(o.sources=e[3]),1&t[0]&&(o.sourcePlugin=e[0]),64&t[0]&&(o.source=e[6]),y.$set(o)},i(e){I||(T(a),T(y.$$.fragment,e),e&&(x||ke((()=>{x=Ae(b,vt,{x:100,duration:300}),x.start()}))),I=!0)},o(e){P(a),P(y.$$.fragment,e),I=!1},d(e){e&&(p(t),p(v),p(b)),A[r].d(),J(y)}}}function Pc(e){var t;let n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S,C,N,A,O,_,L="New Source";function D(t){e[17](t)}s=new je({props:{src:Kn,class:"w-4 h-4"}});let V={required:!0,name:"sourceType",$$slots:{default:[Qc]},$$scope:{ctx:e}};function F(t){e[18](t)}function H(t){e[19](t)}void 0!==e[4]&&(V.selected=e[4]),d=new da({props:V}),Ee.push((()=>Ge(d,"selected",D)));let K={showPrefix:!0};return void 0!==e[5]&&(K.sourceName=e[5]),void 0!==e[9]&&(K.nameError=e[9]),$=new Ui({props:K}),Ee.push((()=>Ge($,"sourceName",F))),Ee.push((()=>Ge($,"nameError",H))),I=new We({props:{variant:"ghost",$$slots:{default:[$c]},$$scope:{ctx:e}}}),I.$on("click",e[20]),C=new We({props:{type:"submit",disabled:!(null!=(t=e[4])&&t.value&&e[5]),$$slots:{default:[xc]},$$scope:{ctx:e}}}),{c(){n=w("h3"),Z(s.$$.fragment),o=B(),r=w("span"),r.textContent=L,a=B(),i=w("div"),l=w("form"),c=w("label"),u=le("Source Type\n\t\t\t\t\t"),Z(d.$$.fragment),m=B(),h=w("div"),Z($.$$.fragment),y=B(),x=w("div"),Z(I.$$.fragment),S=B(),Z(C.$$.fragment),this.h()},l(e){n=k(e,"H3",{class:!0});var t=M(n);X(s.$$.fragment,t),o=q(t),r=k(t,"SPAN",{"data-svelte-h":!0}),"svelte-13328qh"!==oe(r)&&(r.textContent=L),t.forEach(p),a=q(e),i=k(e,"DIV",{});var f=M(i);l=k(f,"FORM",{class:!0});var g=M(l);c=k(g,"LABEL",{for:!0,class:!0});var v=M(c);u=ie(v,"Source Type\n\t\t\t\t\t"),X(d.$$.fragment,v),v.forEach(p),m=q(g),h=k(g,"DIV",{});var b=M(h);X($.$$.fragment,b),b.forEach(p),y=q(g),x=k(g,"DIV",{class:!0});var w=M(x);X(I.$$.fragment,w),S=q(w),X(C.$$.fragment,w),w.forEach(p),g.forEach(p),f.forEach(p),this.h()},h(){E(n,"class","font-semibold text-base-content flex items-center gap-2 mb-4"),E(c,"for","sourceType"),E(c,"class","font-medium text-sm flex flex-col gap-2"),E(x,"class","flex justify-end gap-2"),E(l,"class","flex flex-col w-full gap-4")},m(t,f){j(t,n,f),z(s,n,null),g(n,o),g(n,r),j(t,a,f),j(t,i,f),g(i,l),g(l,c),g(c,u),z(d,c,null),g(l,m),g(l,h),z($,h,null),g(l,y),g(l,x),z(I,x,null),g(x,S),z(C,x,null),A=!0,O||(_=ce(l,"submit",Ci(e[11])),O=!0)},p(e,t){var n;const s={};20&t[0]|4&t[1]&&(s.$$scope={dirty:t,ctx:e}),!f&&16&t[0]&&(f=!0,s.selected=e[4],Ue((()=>f=!1))),d.$set(s);const o={};!v&&32&t[0]&&(v=!0,o.sourceName=e[5],Ue((()=>v=!1))),!b&&512&t[0]&&(b=!0,o.nameError=e[9],Ue((()=>b=!1))),$.$set(o);const r={};4&t[1]&&(r.$$scope={dirty:t,ctx:e}),I.$set(r);const a={};48&t[0]&&(a.disabled=!(null!=(n=e[4])&&n.value&&e[5])),4&t[1]&&(a.$$scope={dirty:t,ctx:e}),C.$set(a)},i(e){A||(T(s.$$.fragment,e),T(d.$$.fragment,e),T($.$$.fragment,e),T(I.$$.fragment,e),T(C.$$.fragment,e),e&&(N||ke((()=>{N=Ae(i,vt,{x:-50,duration:300}),N.start()}))),A=!0)},o(e){P(s.$$.fragment,e),P(d.$$.fragment,e),P($.$$.fragment,e),P(I.$$.fragment,e),P(C.$$.fragment,e),A=!1},d(e){e&&(p(n),p(a),p(i)),J(s),J(d),J($),J(I),J(C),O=!1,_()}}}function Dc(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y="Connected",x=e[6].name+"";return s=new je({props:{src:fr,class:"text-positive/90 h-14 w-14"}}),$=new We({props:{variant:"primary",size:"lg",$$slots:{default:[eu]},$$scope:{ctx:e}}}),$.$on("click",e[13]),{c(){t=w("div"),n=w("div"),Z(s.$$.fragment),o=B(),r=w("div"),a=w("p"),a.textContent=y,i=B(),l=w("p"),c=le("Add files to "),u=w("code"),d=le("sources/"),f=le(x),m=le(" in order to query this source."),h=B(),Z($.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0});var g=M(t);n=k(g,"DIV",{class:!0});var v=M(n);X(s.$$.fragment,v),o=q(v),r=k(v,"DIV",{class:!0});var b=M(r);a=k(b,"P",{class:!0,"data-svelte-h":!0}),"svelte-ygcw3j"!==oe(a)&&(a.textContent=y),i=q(b),l=k(b,"P",{class:!0});var w=M(l);c=ie(w,"Add files to "),u=k(w,"CODE",{class:!0});var E=M(u);d=ie(E,"sources/"),f=ie(E,x),E.forEach(p),m=ie(w," in order to query this source."),w.forEach(p),b.forEach(p),v.forEach(p),h=q(g),X($.$$.fragment,g),g.forEach(p),this.h()},h(){E(a,"class","font-semibold text-base-content mb-4 text-lg"),E(u,"class","text-sm bg-base-200 px-1.5 py-0.5 rounded border font-mono"),E(l,"class","text-base-content"),E(r,"class","flex flex-col items-center text-sm"),E(n,"class","flex flex-col items-center gap-2"),E(t,"class","flex flex-col gap-8 items-center p-2")},m(e,p){j(e,t,p),g(t,n),z(s,n,null),g(n,o),g(n,r),g(r,a),g(r,i),g(r,l),g(l,c),g(l,u),g(u,d),g(u,f),g(l,m),g(t,h),z($,t,null),b=!0},p(e,t){(!b||64&t[0])&&x!==(x=e[6].name+"")&&ye(f,x);const n={};4&t[1]&&(n.$$scope={dirty:t,ctx:e}),$.$set(n)},i(e){b||(T(s.$$.fragment,e),T($.$$.fragment,e),e&&(v||ke((()=>{v=Ae(t,vt,{x:50,duration:300}),v.start()}))),b=!0)},o(e){P(s.$$.fragment,e),P($.$$.fragment,e),b=!1},d(e){e&&p(t),J(s),J($)}}}function Mc(e){let t,n;return t=new je({props:{src:Gt,class:"w-6 h-6"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Vc(e){let t,n;return t=new je({props:{src:it[e[10]],class:"w-6 h-6"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};1024&n[0]&&(s.src=it[e[10]]),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function jc(e){let t,n;return t=new je({props:{src:st[e[10]],class:"w-6 h-6"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};1024&n[0]&&(s.src=st[e[10]]),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Fc(e){var t;let n,s,o,r=((null==(t=e[4])?void 0:t.value)??"")+"";return{c(){n=w("div"),s=w("span"),o=le(r),this.h()},l(e){n=k(e,"DIV",{});var t=M(n);s=k(t,"SPAN",{});var a=M(s);o=ie(a,r),a.forEach(p),t.forEach(p),this.h()},h(){var t;tt(n,"border-negative",!(null!=(t=e[4])&&t.value))},m(e,t){j(e,n,t),g(n,s),g(s,o)},p(e,t){var s,a;16&t[0]&&r!==(r=((null==(s=e[4])?void 0:s.value)??"")+"")&&ye(o,r),16&t[0]&&tt(n,"border-negative",!(null!=(a=e[4])&&a.value))},d(e){e&&p(n)}}}function qc(e){let t,n;return t=new Ri({props:{value:e[29],$$slots:{default:[Uc]},$$scope:{ctx:e}}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};4&n[0]&&(s.value=e[29]),4&n[0]|4&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Bc(e){let t,n,s,o;const r=[Yc,Gc],a=[];function i(e,t){return e[29].length?0:1}function l(e,t){return 0===t?Ac(e):e}return t=i(e),n=a[t]=r[t](l(e,t)),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){a[t].m(e,n),j(e,s,n),o=!0},p(e,o){let c=t;t=i(e),t===c?a[t].p(l(e,t),o):(pe(),P(a[c],1,1,(()=>{a[c]=null})),ge(),n=a[t],n?n.p(l(e,t),o):(n=a[t]=r[t](l(e,t)),n.c()),T(n,1),n.m(s.parentNode,s))},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),a[t].d(e)}}}function Hc(e){let t,n;return t=new je({props:{src:Gt,class:"w-5 h-5"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Rc(e){let t,n;return t=new je({props:{src:it[e[32]],class:"w-5 h-5"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};4&n[0]&&(s.src=it[e[32]]),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Kc(e){let t,n;return t=new je({props:{src:st[e[32]],class:"w-5 h-5"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};4&n[0]&&(s.src=st[e[32]]),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Uc(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v=e[29]+"",b=e[24]+"";const y=[Kc,Rc,Hc],x=[];function I(e,t){return 4&t[0]&&(s=null),4&t[0]&&(o=null),null==s&&(s=!!e[14](e[32])),s?0:(null==o&&(o=!!e[15](e[32])),o?1:2)}return r=I(e,[-1,-1]),a=x[r]=y[r](e),{c(){t=w("div"),n=w("div"),a.c(),i=B(),l=w("div"),c=w("div"),u=le(v),d=B(),f=w("div"),m=le(b),h=B(),this.h()},l(e){t=k(e,"DIV",{class:!0});var s=M(t);n=k(s,"DIV",{class:!0});var o=M(n);a.l(o),o.forEach(p),i=q(s),l=k(s,"DIV",{class:!0});var r=M(l);c=k(r,"DIV",{});var g=M(c);u=ie(g,v),g.forEach(p),d=q(r),f=k(r,"DIV",{class:!0});var $=M(f);m=ie($,b),$.forEach(p),r.forEach(p),s.forEach(p),h=q(e),this.h()},h(){E(n,"class","text-base-content"),E(f,"class","font-light text-base-content-muted/70 text-xs"),E(l,"class","flex flex-col"),E(t,"class","flex items-center gap-4")},m(e,s){j(e,t,s),g(t,n),x[r].m(n,null),g(t,i),g(t,l),g(l,c),g(c,u),g(l,d),g(l,f),g(f,m),j(e,h,s),$=!0},p(e,t){let s=r;r=I(e,t),r===s?x[r].p(e,t):(pe(),P(x[s],1,1,(()=>{x[s]=null})),ge(),a=x[r],a?a.p(e,t):(a=x[r]=y[r](e),a.c()),T(a,1),a.m(n,null)),(!$||4&t[0])&&v!==(v=e[29]+"")&&ye(u,v),(!$||4&t[0])&&b!==(b=e[24]+"")&&ye(m,b)},i(e){$||(T(a),$=!0)},o(e){P(a),$=!1},d(e){e&&(p(t),p(h)),x[r].d()}}}function Gc(e){return{c:re,l:re,m:re,p:re,i:re,o:re,d:re}}function Yc(e){let t,n;return t=new Ri({props:{value:e[29][0],$$slots:{default:[Xc]},$$scope:{ctx:e}}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};4&n[0]&&(s.value=e[29][0]),4&n[0]|4&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Wc(e){let t,n;return t=new je({props:{src:Gt,class:"w-5 h-5"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Jc(e){let t,n;return t=new je({props:{src:it[e[32]],class:"w-5 h-5"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};4&n[0]&&(s.src=it[e[32]]),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function zc(e){let t,n;return t=new je({props:{src:st[e[32]],class:"w-5 h-5"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};4&n[0]&&(s.src=st[e[32]]),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Xc(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v=e[29][0]+"",b=e[24]+"";const y=[zc,Jc,Wc],x=[];function I(e,t){return 4&t[0]&&(s=null),4&t[0]&&(o=null),null==s&&(s=!!e[14](e[32])),s?0:(null==o&&(o=!!e[15](e[32])),o?1:2)}return r=I(e,[-1,-1]),a=x[r]=y[r](e),{c(){t=w("div"),n=w("div"),a.c(),i=B(),l=w("div"),c=w("div"),u=le(v),d=B(),f=w("div"),m=le(b),h=B(),this.h()},l(e){t=k(e,"DIV",{class:!0});var s=M(t);n=k(s,"DIV",{class:!0});var o=M(n);a.l(o),o.forEach(p),i=q(s),l=k(s,"DIV",{class:!0});var r=M(l);c=k(r,"DIV",{});var g=M(c);u=ie(g,v),g.forEach(p),d=q(r),f=k(r,"DIV",{class:!0});var $=M(f);m=ie($,b),$.forEach(p),r.forEach(p),s.forEach(p),h=q(e),this.h()},h(){E(n,"class","text-base-content"),E(f,"class","font-light text-base-content-muted/70 text-xs"),E(l,"class","flex flex-col"),E(t,"class","flex items-center gap-4")},m(e,s){j(e,t,s),g(t,n),x[r].m(n,null),g(t,i),g(t,l),g(l,c),g(c,u),g(l,d),g(l,f),g(f,m),j(e,h,s),$=!0},p(e,t){let s=r;r=I(e,t),r===s?x[r].p(e,t):(pe(),P(x[s],1,1,(()=>{x[s]=null})),ge(),a=x[r],a?a.p(e,t):(a=x[r]=y[r](e),a.c()),T(a,1),a.m(n,null)),(!$||4&t[0])&&v!==(v=e[29][0]+"")&&ye(u,v),(!$||4&t[0])&&b!==(b=e[24]+"")&&ye(m,b)},i(e){$||(T(a),$=!0)},o(e){P(a),$=!1},d(e){e&&(p(t),p(h)),x[r].d()}}}function ei(e){let t,n,s,o,r;const a=[Bc,qc],i=[];function l(e,n){return 4&n[0]&&(t=null),null==t&&(t=!!Array.isArray(e[29])),t?0:1}function c(e,t){return 1===t?Oc(e):e}return n=l(e,[-1,-1]),s=i[n]=a[n](c(e,n)),{c(){s.c(),o=fe()},l(e){s.l(e),o=fe()},m(e,t){i[n].m(e,t),j(e,o,t),r=!0},p(e,t){let r=n;n=l(e,t),n===r?i[n].p(c(e,n),t):(pe(),P(i[r],1,1,(()=>{i[r]=null})),ge(),s=i[n],s?s.p(c(e,n),t):(s=i[n]=a[n](c(e,n)),s.c()),T(s,1),s.m(o.parentNode,o))},i(e){r||(T(s),r=!0)},o(e){P(s),r=!1},d(e){e&&p(o),i[n].d(e)}}}function ti(e){let t,n,s=Ne(e[26]),o=[];for(let t=0;t<s.length;t+=1)o[t]=ei(xs(e,s,t));const r=e=>P(o[e],1,1,(()=>{o[e]=null}));return{c(){for(let e=0;e<o.length;e+=1)o[e].c();t=fe()},l(e){for(let t=0;t<o.length;t+=1)o[t].l(e);t=fe()},m(e,s){for(let t=0;t<o.length;t+=1)o[t]&&o[t].m(e,s);j(e,t,s),n=!0},p(e,n){if(49156&n[0]){let a;for(s=Ne(e[26]),a=0;a<s.length;a+=1){const r=xs(e,s,a);o[a]?(o[a].p(r,n),T(o[a],1)):(o[a]=ei(r),o[a].c(),T(o[a],1),o[a].m(t.parentNode,t))}for(pe(),a=s.length;a<o.length;a+=1)r(a);ge()}},i(e){if(!n){for(let e=0;e<s.length;e+=1)T(o[e]);n=!0}},o(e){o=o.filter(Boolean);for(let e=0;e<o.length;e+=1)P(o[e]);n=!1},d(e){e&&p(t),at(o,e)}}}function Zc(e){let t,n,s=Ne(Object.entries(e[2])),o=[];for(let t=0;t<s.length;t+=1)o[t]=ti($s(e,s,t));const r=e=>P(o[e],1,1,(()=>{o[e]=null}));return{c(){for(let e=0;e<o.length;e+=1)o[e].c();t=fe()},l(e){for(let t=0;t<o.length;t+=1)o[t].l(e);t=fe()},m(e,s){for(let t=0;t<o.length;t+=1)o[t]&&o[t].m(e,s);j(e,t,s),n=!0},p(e,n){if(49156&n[0]){let a;for(s=Ne(Object.entries(e[2])),a=0;a<s.length;a+=1){const r=$s(e,s,a);o[a]?(o[a].p(r,n),T(o[a],1)):(o[a]=ti(r),o[a].c(),T(o[a],1),o[a].m(t.parentNode,t))}for(pe(),a=s.length;a<o.length;a+=1)r(a);ge()}},i(e){if(!n){for(let e=0;e<s.length;e+=1)T(o[e]);n=!0}},o(e){o=o.filter(Boolean);for(let e=0;e<o.length;e+=1)P(o[e]);n=!1},d(e){e&&p(t),at(o,e)}}}function Qc(e){let t,n,s,o,r,a;return t=new fa({props:{$$slots:{default:[Fc]},$$scope:{ctx:e}}}),s=new oa({props:{$$slots:{default:[Zc]},$$scope:{ctx:e}}}),r=new ma({}),{c(){Z(t.$$.fragment),n=B(),Z(s.$$.fragment),o=B(),Z(r.$$.fragment)},l(e){X(t.$$.fragment,e),n=q(e),X(s.$$.fragment,e),o=q(e),X(r.$$.fragment,e)},m(e,i){z(t,e,i),j(e,n,i),z(s,e,i),j(e,o,i),z(r,e,i),a=!0},p(e,n){const o={};16&n[0]|4&n[1]&&(o.$$scope={dirty:n,ctx:e}),t.$set(o);const r={};4&n[0]|4&n[1]&&(r.$$scope={dirty:n,ctx:e}),s.$set(r)},i(e){a||(T(t.$$.fragment,e),T(s.$$.fragment,e),T(r.$$.fragment,e),a=!0)},o(e){P(t.$$.fragment,e),P(s.$$.fragment,e),P(r.$$.fragment,e),a=!1},d(e){e&&(p(n),p(o)),J(t,e),J(s,e),J(r,e)}}}function $c(e){let t;return{c(){t=le("Cancel")},l(e){t=ie(e,"Cancel")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function xc(e){let t;return{c(){t=le("Next →")},l(e){t=ie(e,"Next →")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function eu(e){let t;return{c(){t=le("Done")},l(e){t=ie(e,"Done")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function tu(e){let t,n,s,o;const r=[Dc,Pc,Lc],a=[];function i(e,t){return e[8]?0:e[7]?2:1}return n=i(e),s=a[n]=r[n](e),{c(){t=w("div"),s.c()},l(e){t=k(e,"DIV",{});var n=M(t);s.l(n),n.forEach(p)},m(e,s){j(e,t,s),a[n].m(t,null),o=!0},p(e,o){let l=n;n=i(e),n===l?a[n].p(e,o):(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge(),s=a[n],s?s.p(e,o):(s=a[n]=r[n](e),s.c()),T(s,1),s.m(t,null))},i(e){o||(T(s),o=!0)},o(e){P(s),o=!1},d(e){e&&p(t),a[n].d()}}}function nu(e,t,n){let s,o,{addingSource:r}=t,{availablePackages:a}=t,{sourcePlugin:i}=t,{availableSourcePlugins:l}=t,{sources:c=[]}=t,u={},d="",f=!1,p=!1,m="";const h=wi();function g(e){n(8,p=!0),h("newSource",e.detail)}return e.$$set=e=>{"addingSource"in e&&n(1,r=e.addingSource),"availablePackages"in e&&n(2,a=e.availablePackages),"sourcePlugin"in e&&n(0,i=e.sourcePlugin),"availableSourcePlugins"in e&&n(16,l=e.availableSourcePlugins),"sources"in e&&n(3,c=e.sources)},e.$$.update=()=>{48&e.$$.dirty[0]&&n(6,s={name:d,type:null==u?void 0:u.value,options:{},environmentVariables:{}}),65600&e.$$.dirty[0]&&n(0,i=null==l?void 0:l[null==s?void 0:s.type]),1&e.$$.dirty[0]&&n(10,o=null==i?void 0:i.package.package.evidence.icon)},[i,r,a,c,u,d,s,f,p,m,o,function(){null!=u&&u.value&&(n(9,m=Ki(d,c)),!m&&n(7,f=!0))},g,function(){n(1,r=!1)},e=>typeof e<"u"&&e in st,e=>typeof e<"u"&&e in it,l,function(e){u=e,n(4,u)},function(e){d=e,n(5,d)},function(e){m=e,n(9,m)},()=>n(1,r=!1),e=>g(e),()=>n(7,f=!1)]}class dl extends be{constructor(e){super(),ve(this,e,nu,tu,_e,{addingSource:1,availablePackages:2,sourcePlugin:0,availableSourcePlugins:16,sources:3},null,[-1,-1])}}function su(e){let t,n;return t=new je({props:{src:Gt,class:"w-6 h-6"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function iu(e){let t,n;return t=new je({props:{src:dr,class:"w-6 h-6 text-negative"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function lu(e){let t,n;return t=new je({props:{src:it[e[4]],class:"w-6 h-6"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};16&n&&(s.src=it[e[4]]),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ru(e){let t,n;return t=new je({props:{src:st[e[4]],class:"w-6 h-6"}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};16&n&&(s.src=st[e[4]]),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function ou(e){let t;return{c(){t=le("Edit")},l(e){t=ie(e,"Edit")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function ni(e){let t,n,s,o;return n=new fl({props:{sources:e[1],source:e[0],sourcePlugin:e[2]}}),n.$on("sourceUpdated",e[9]),{c(){t=w("div"),Z(n.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0});var s=M(t);X(n.$$.fragment,s),s.forEach(p),this.h()},h(){E(t,"class","flex")},m(e,s){j(e,t,s),z(n,t,null),o=!0},p(e,t){const s={};2&t&&(s.sources=e[1]),1&t&&(s.source=e[0]),4&t&&(s.sourcePlugin=e[2]),n.$set(s)},i(e){o||(T(n.$$.fragment,e),e&&ke((()=>{o&&(s||(s=nt(t,dt,{},!0)),s.run(1))})),o=!0)},o(e){P(n.$$.fragment,e),e&&(s||(s=nt(t,dt,{},!1)),s.run(0)),o=!1},d(e){e&&p(t),J(n),e&&s&&s.end()}}}function au(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S,C=e[0].type+"",N=e[0].name+"";const A=[ru,lu,iu,su],O=[];function _(e,t){return 16&t&&(o=null),16&t&&(r=null),null==o&&(o=!!e[5](e[4])),o?0:(null==r&&(r=!!e[6](e[4])),r?1:e[2]?3:2)}a=_(e,-1),i=O[a]=A[a](e),x=new We({props:{variant:"ghost",disabled:!e[2],$$slots:{default:[ou]},$$scope:{ctx:e}}}),x.$on("click",e[8]);let L=e[3]&&ni(e);return{c(){t=w("div"),n=w("div"),s=w("div"),i.c(),l=B(),c=w("div"),u=w("div"),d=w("div"),f=w("p"),m=le(C),h=B(),$=w("h4"),v=le(N),b=B(),y=w("div"),Z(x.$$.fragment),I=B(),L&&L.c(),this.h()},l(e){t=k(e,"DIV",{class:!0});var o=M(t);n=k(o,"DIV",{class:!0});var r=M(n);s=k(r,"DIV",{class:!0});var a=M(s);i.l(a),a.forEach(p),l=q(r),c=k(r,"DIV",{class:!0});var g=M(c);u=k(g,"DIV",{class:!0});var w=M(u);d=k(w,"DIV",{class:!0});var E=M(d);f=k(E,"P",{class:!0});var T=M(f);m=ie(T,C),T.forEach(p),h=q(E),$=k(E,"H4",{class:!0});var P=M($);v=ie(P,N),P.forEach(p),E.forEach(p),w.forEach(p),b=q(g),y=k(g,"DIV",{class:!0});var S=M(y);X(x.$$.fragment,S),S.forEach(p),g.forEach(p),r.forEach(p),I=q(o),L&&L.l(o),o.forEach(p),this.h()},h(){E(s,"class","text-base-content h-full"),E(f,"class","text-base-content-muted font-mono text-xs"),E($,"class","text-base-content font-medium"),E(d,"class","flex flex-col text-sm"),E(u,"class","flex items-center text-base-content gap-4"),E(y,"class","flex justify-end gap-1"),E(c,"class","flex w-full justify-between items-center"),E(n,"class","flex items-center gap-4"),E(t,"class","border-b border-base-300 last:border-b-0 p-4")},m(e,o){j(e,t,o),g(t,n),g(n,s),O[a].m(s,null),g(n,l),g(n,c),g(c,u),g(u,d),g(d,f),g(f,m),g(d,h),g(d,$),g($,v),g(c,b),g(c,y),z(x,y,null),g(t,I),L&&L.m(t,null),S=!0},p(e,[n]){let o=a;a=_(e,n),a===o?O[a].p(e,n):(pe(),P(O[o],1,1,(()=>{O[o]=null})),ge(),i=O[a],i?i.p(e,n):(i=O[a]=A[a](e),i.c()),T(i,1),i.m(s,null)),(!S||1&n)&&C!==(C=e[0].type+"")&&ye(m,C),(!S||1&n)&&N!==(N=e[0].name+"")&&ye(v,N);const r={};4&n&&(r.disabled=!e[2]),1024&n&&(r.$$scope={dirty:n,ctx:e}),x.$set(r),e[3]?L?(L.p(e,n),8&n&&T(L,1)):(L=ni(e),L.c(),T(L,1),L.m(t,null)):L&&(pe(),P(L,1,1,(()=>{L=null})),ge())},i(e){S||(T(i),T(x.$$.fragment,e),T(L),S=!0)},o(e){P(i),P(x.$$.fragment,e),P(L),S=!1},d(e){e&&p(t),O[a].d(),J(x),L&&L.d()}}}function cu(e,t,n){let s,o,{source:r}=t,{sources:a}=t,{availableSourcePlugins:i}=t,l=!1;return e.$$set=e=>{"source"in e&&n(0,r=e.source),"sources"in e&&n(1,a=e.sources),"availableSourcePlugins"in e&&n(7,i=e.availableSourcePlugins)},e.$$.update=()=>{129&e.$$.dirty&&n(2,s=null==i?void 0:i[r.type]),4&e.$$.dirty&&n(4,o=null==s?void 0:s.package.package.evidence.icon)},[r,a,s,l,o,e=>typeof e<"u"&&e in st,e=>typeof e<"u"&&e in it,i,()=>n(3,l=!l),e=>n(0,r=e.detail)]}class uu extends be{constructor(e){super(),ve(this,e,cu,au,_e,{source:0,sources:1,availableSourcePlugins:7})}}function si(e,t,n){const s=e.slice();return s[9]=t[n],s}function fu(e){let t,n,s,o,r;function a(t){e[8](t)}let i={availableSourcePlugins:e[1],availablePackages:e[3],sources:e[0]};return void 0!==e[2]&&(i.addingSource=e[2]),n=new dl({props:i}),Ee.push((()=>Ge(n,"addingSource",a))),n.$on("newSource",e[4]),{c(){t=w("div"),Z(n.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0});var s=M(t);X(n.$$.fragment,s),s.forEach(p),this.h()},h(){E(t,"class","py-4 border rounded-md shadow-sm border-base-300 p-4")},m(e,s){j(e,t,s),z(n,t,null),r=!0},p(e,t){const o={};2&t&&(o.availableSourcePlugins=e[1]),8&t&&(o.availablePackages=e[3]),1&t&&(o.sources=e[0]),!s&&4&t&&(s=!0,o.addingSource=e[2],Ue((()=>s=!1))),n.$set(o)},i(e){r||(T(n.$$.fragment,e),e&&(o||ke((()=>{o=Ae(t,vt,{y:100}),o.start()}))),r=!0)},o(e){P(n.$$.fragment,e),r=!1},d(e){e&&p(t),J(n)}}}function du(e){let t,n,s,o,r,a,i,l,c,u='<p class="font-semibold text-base-content">No Sources</p> <p class="text-base-content-muted">Get started by adding your first source.</p>';return s=new je({props:{src:Gt,class:"text-base-300 h-14 w-14"}}),i=new We({props:{variant:"primary",size:"xl",class:"w-full",icon:Kn,iconPosition:"left",$$slots:{default:[hu]},$$scope:{ctx:e}}}),i.$on("click",e[7]),{c(){t=w("div"),n=w("div"),Z(s.$$.fragment),o=B(),r=w("div"),r.innerHTML=u,a=B(),Z(i.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0});var l=M(t);n=k(l,"DIV",{class:!0});var c=M(n);X(s.$$.fragment,c),o=q(c),r=k(c,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1n847fv"!==oe(r)&&(r.innerHTML=u),c.forEach(p),a=q(l),X(i.$$.fragment,l),l.forEach(p),this.h()},h(){E(r,"class","flex flex-col items-center text-sm"),E(n,"class","flex flex-col items-center gap-2"),E(t,"class","bg-base-200 rounded-xl flex flex-col gap-8 items-center p-8")},m(e,l){j(e,t,l),g(t,n),z(s,n,null),g(n,o),g(n,r),g(t,a),z(i,t,null),c=!0},p(e,t){const n={};4096&t&&(n.$$scope={dirty:t,ctx:e}),i.$set(n)},i(e){c||(T(s.$$.fragment,e),T(i.$$.fragment,e),e&&(l||ke((()=>{l=Ae(t,vt,{y:-100}),l.start()}))),c=!0)},o(e){P(s.$$.fragment,e),P(i.$$.fragment,e),c=!1},d(e){e&&p(t),J(s),J(i)}}}function mu(e){let t,n,s,o;const r=[gu,pu],a=[];function i(e,t){return e[2]?1:0}return t=i(e),n=a[t]=r[t](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,n){a[t].m(e,n),j(e,s,n),o=!0},p(e,o){let l=t;t=i(e),t===l?a[t].p(e,o):(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge(),n=a[t],n?n.p(e,o):(n=a[t]=r[t](e),n.c()),T(n,1),n.m(s.parentNode,s))},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),a[t].d(e)}}}function hu(e){let t;return{c(){t=le("New Source")},l(e){t=ie(e,"New Source")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function pu(e){let t,n,s,o,r;function a(t){e[6](t)}let i={availableSourcePlugins:e[1],availablePackages:e[3],sources:e[0]};return void 0!==e[2]&&(i.addingSource=e[2]),n=new dl({props:i}),Ee.push((()=>Ge(n,"addingSource",a))),n.$on("newSource",e[4]),{c(){t=w("div"),Z(n.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0});var s=M(t);X(n.$$.fragment,s),s.forEach(p),this.h()},h(){E(t,"class","py-4 border rounded-md shadow-sm border-base-300 p-4")},m(e,s){j(e,t,s),z(n,t,null),r=!0},p(e,t){const o={};2&t&&(o.availableSourcePlugins=e[1]),8&t&&(o.availablePackages=e[3]),1&t&&(o.sources=e[0]),!s&&4&t&&(s=!0,o.addingSource=e[2],Ue((()=>s=!1))),n.$set(o)},i(e){r||(T(n.$$.fragment,e),e&&(o||ke((()=>{o=Ae(t,vt,{y:100}),o.start()}))),r=!0)},o(e){P(n.$$.fragment,e),r=!1},d(e){e&&p(t),J(n)}}}function gu(e){let t,n,s,o,r,a=[],i=new Map,l=Ne(e[0]);const c=e=>{var t;return null==(t=e[9])?void 0:t.name};for(let t=0;t<l.length;t+=1){let n=si(e,l,t),s=c(n);i.set(s,a[t]=ii(s,n))}return o=new We({props:{size:"xl",icon:Kn,iconPosition:"left",class:"w-full",$$slots:{default:[_u]},$$scope:{ctx:e}}}),o.$on("click",e[5]),{c(){t=w("div");for(let e=0;e<a.length;e+=1)a[e].c();s=B(),Z(o.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0});var n=M(t);for(let e=0;e<a.length;e+=1)a[e].l(n);n.forEach(p),s=q(e),X(o.$$.fragment,e),this.h()},h(){E(t,"class","mb-4 rounded-md shadow-sm bg-gradient-to-br from-base-100 to-base-100/60 border")},m(e,n){j(e,t,n);for(let e=0;e<a.length;e+=1)a[e]&&a[e].m(t,null);j(e,s,n),z(o,e,n),r=!0},p(e,n){3&n&&(l=Ne(e[0]),pe(),a=Un(a,n,c,1,e,l,i,t,mr,ii,null,si),ge());const s={};4096&n&&(s.$$scope={dirty:n,ctx:e}),o.$set(s)},i(e){if(!r){for(let e=0;e<l.length;e+=1)T(a[e]);e&&(n||ke((()=>{n=Ae(t,vt,{y:-100}),n.start()}))),T(o.$$.fragment,e),r=!0}},o(e){for(let e=0;e<a.length;e+=1)P(a[e]);P(o.$$.fragment,e),r=!1},d(e){e&&(p(t),p(s));for(let e=0;e<a.length;e+=1)a[e].d();J(o,e)}}}function ii(e,t){let n,s,o;return s=new uu({props:{availableSourcePlugins:t[1],source:t[9],sources:t[0]}}),{key:e,first:null,c(){n=fe(),Z(s.$$.fragment),this.h()},l(e){n=fe(),X(s.$$.fragment,e),this.h()},h(){this.first=n},m(e,t){j(e,n,t),z(s,e,t),o=!0},p(e,n){t=e;const o={};2&n&&(o.availableSourcePlugins=t[1]),1&n&&(o.source=t[9]),1&n&&(o.sources=t[0]),s.$set(o)},i(e){o||(T(s.$$.fragment,e),o=!0)},o(e){P(s.$$.fragment,e),o=!1},d(e){e&&p(n),J(s,e)}}}function _u(e){let t;return{c(){t=le("New Source")},l(e){t=ie(e,"New Source")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function bu(e){let t,n,s,o;const r=[mu,du,fu],a=[];function i(e,t){var n;return(null==(n=e[0])?void 0:n.length)>0?0:e[2]?2:1}return n=i(e),s=a[n]=r[n](e),{c(){t=w("div"),s.c()},l(e){t=k(e,"DIV",{});var n=M(t);s.l(n),n.forEach(p)},m(e,s){j(e,t,s),a[n].m(t,null),o=!0},p(e,[o]){let l=n;n=i(e),n===l?a[n].p(e,o):(pe(),P(a[l],1,1,(()=>{a[l]=null})),ge(),s=a[n],s?s.p(e,o):(s=a[n]=r[n](e),s.c()),T(s,1),s.m(t,null))},i(e){o||(T(s),o=!0)},o(e){P(s),o=!1},d(e){e&&p(t),a[n].d()}}}function vu(e,t,n){let s,{availableSourcePlugins:o={}}=t,{sources:r=[]}=t,a=!1;return e.$$set=e=>{"availableSourcePlugins"in e&&n(1,o=e.availableSourcePlugins),"sources"in e&&n(0,r=e.sources)},e.$$.update=()=>{2&e.$$.dirty&&n(3,s=Object.values(o).reduce(((e,t)=>{const n=t.package.package;return e[n.name]||(e[n.name]=t),e}),{}))},[r,o,a,s,function(e){r.push(e.detail),n(0,r)},()=>n(2,a=!0),function(e){a=e,n(2,a)},()=>n(2,a=!0),function(e){a=e,n(2,a)}]}class yu extends be{constructor(e){super(),ve(this,e,vu,bu,_e,{availableSourcePlugins:1,sources:0})}}function li(e){let t,n;return{c(){t=tn("title"),n=le(e[0])},l(s){t=en(s,"title",{});var o=M(t);n=ie(o,e[0]),o.forEach(p)},m(e,s){j(e,t,s),g(t,n)},p(e,t){1&t&&ye(n,e[0])},d(e){e&&p(t)}}}function ku(e){let t,n,s,o=e[0]&&li(e),r=[{xmlns:"http://www.w3.org/2000/svg"},{viewBox:"0 0 32 32"},{fill:"currentColor"},{width:"100%"},{height:"100%"},{preserveAspectRatio:"xMidYMid meet"},e[1],e[2]],a={};for(let e=0;e<r.length;e+=1)a=ae(a,r[e]);return{c(){t=tn("svg"),o&&o.c(),n=tn("path"),s=tn("path"),this.h()},l(e){t=en(e,"svg",{xmlns:!0,viewBox:!0,fill:!0,width:!0,height:!0,preserveAspectRatio:!0});var r=M(t);o&&o.l(r),n=en(r,"path",{d:!0}),M(n).forEach(p),s=en(r,"path",{d:!0}),M(s).forEach(p),r.forEach(p),this.h()},h(){E(n,"d","M28,10V28H10V10H28m0-2H10a2,2,0,0,0-2,2V28a2,2,0,0,0,2,2H28a2,2,0,0,0,2-2V10a2,2,0,0,0-2-2Z"),E(s,"d","M4,18H2V4A2,2,0,0,1,4,2H18V4H4Z"),ns(t,a)},m(e,r){j(e,t,r),o&&o.m(t,null),g(t,n),g(t,s)},p(e,[s]){e[0]?o?o.p(e,s):(o=li(e),o.c(),o.m(t,n)):o&&(o.d(1),o=null),ns(t,a=Ve(r,[{xmlns:"http://www.w3.org/2000/svg"},{viewBox:"0 0 32 32"},{fill:"currentColor"},{width:"100%"},{height:"100%"},{preserveAspectRatio:"xMidYMid meet"},2&s&&e[1],4&s&&e[2]]))},i:re,o:re,d(e){e&&p(t),o&&o.d()}}}function wu(e,t,n){let s,o;const r=["title"];let a=Se(t,r),{title:i}=t;return e.$$set=e=>{n(4,t=ae(ae({},t),Ye(e))),n(2,a=Se(t,r)),"title"in e&&n(0,i=e.title)},e.$$.update=()=>{n(3,s=t["aria-label"]||t["aria-labelledby"]||i),n(1,o={"aria-hidden":!s||void 0,role:s?"img":void 0,focusable:0===Number(t.tabindex)||void 0})},t=Ye(t),[i,o,a,s]}class Cu extends be{constructor(e){super(),ve(this,e,wu,ku,_e,{title:0})}}function Tu(e){let t,n,s="Copy Project Environment Variables";return{c(){t=w("span"),t.textContent=s},l(e){t=k(e,"SPAN",{"data-svelte-h":!0}),"svelte-z6q5lt"!==oe(t)&&(t.textContent=s)},m(e,n){j(e,t,n)},i(e){e&&(n||ke((()=>{n=Ae(t,Xe,{}),n.start()})))},o:re,d(e){e&&p(t)}}}function Eu(e){let t,n,s="Copied";return{c(){t=w("span"),t.textContent=s},l(e){t=k(e,"SPAN",{"data-svelte-h":!0}),"svelte-v4ra8l"!==oe(t)&&(t.textContent=s)},m(e,n){j(e,t,n)},i(e){e&&(n||ke((()=>{n=Ae(t,Xe,{}),n.start()})))},o:re,d(e){e&&p(t)}}}function Su(e){let t;function n(e,t){return e[0]?Eu:Tu}let s=n(e),o=s(e);return{c(){o.c(),t=fe()},l(e){o.l(e),t=fe()},m(e,n){o.m(e,n),j(e,t,n)},p(e,r){s!==(s=n(e))&&(o.d(1),o=s(e),o&&(o.c(),T(o,1),o.m(t.parentNode,t)))},d(e){e&&p(t),o.d(e)}}}function Iu(e){let t,n;return t=new We({props:{type:"button",class:"w-full mt-4",size:"xl",$$slots:{default:[Su]},$$scope:{ctx:e}}}),t.$on("click",e[1]),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,[n]){const s={};17&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function Nu(e,t,n){let{sources:s}=t,o=!1;const r=function(){n(0,o=!1)};return e.$$set=e=>{"sources"in e&&n(2,s=e.sources)},[o,function(){const e=s.reduce(((e,t)=>[e,Object.entries(t.environmentVariables).map((([e,t])=>`${e}="${t.replace(/\\n/g,"\n")}"`)).join("\n")].join("\n")),"");navigator.clipboard.writeText(e),n(0,o=!0),setTimeout(r,2e3)},s]}class Au extends be{constructor(e){super(),ve(this,e,Nu,Iu,_e,{sources:2})}}function Ou(e){let t,n,s,o=(e[1]?"&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;":e[0])+"";return{c(){t=w("span"),n=new Ei(!1),this.h()},l(e){t=k(e,"SPAN",{});var s=M(t);n=Ti(s,!1),s.forEach(p),this.h()},h(){n.a=null},m(e,s){j(e,t,s),n.m(o,t)},p(e,t){3&t&&o!==(o=(e[1]?"&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;&middot;":e[0])+"")&&n.p(o)},i(e){e&&(s||ke((()=>{s=Ae(t,Xe,{}),s.start()})))},o:re,d(e){e&&p(t)}}}function Lu(e){let t,n,s="Copied";return{c(){t=w("span"),t.textContent=s},l(e){t=k(e,"SPAN",{"data-svelte-h":!0}),"svelte-18eylsc"!==oe(t)&&(t.textContent=s)},m(e,n){j(e,t,n)},p:re,i(e){e&&(n||ke((()=>{n=Ae(t,Xe,{}),n.start()})))},o:re,d(e){e&&p(t)}}}function Pu(e){let t,n,s,o,r,a,i,l;function c(e,t){return e[3]?Lu:Ou}let u=c(e),d=u(e);return r=new Cu({}),{c(){t=w("button"),n=w("div"),d.c(),s=B(),o=w("div"),Z(r.$$.fragment),this.h()},l(e){t=k(e,"BUTTON",{type:!0,class:!0});var a=M(t);n=k(a,"DIV",{class:!0});var i=M(n);d.l(i),i.forEach(p),s=q(a),o=k(a,"DIV",{class:!0});var l=M(o);X(r.$$.fragment,l),l.forEach(p),a.forEach(p),this.h()},h(){E(n,"class","flex w-3/4 overflow-hidden"),E(o,"class","w-4 h-4"),E(t,"type","button"),E(t,"class","rounded-md bg-base-200 border border-base-300 font-mono text-xs p-2 flex items-center justify-between hover:bg-base-200/50 gap-4"),tt(t,"copied",e[3])},m(c,u){j(c,t,u),g(t,n),d.m(n,null),g(t,s),g(t,o),z(r,o,null),a=!0,i||(l=ce(t,"click",e[4]),i=!0)},p(e,[s]){u===(u=c(e))&&d?d.p(e,s):(d.d(1),d=u(e),d&&(d.c(),T(d,1),d.m(n,null))),(!a||8&s)&&tt(t,"copied",e[3])},i(e){a||(T(d),T(r.$$.fragment,e),a=!0)},o(e){P(r.$$.fragment,e),a=!1},d(e){e&&p(t),d.d(),J(r),i=!1,l()}}}function Du(e,t,n){let{text:s}=t,{hideText:o=!1}=t,r=!1;const a=function(){n(3,r=!1)};let{copy:i=async e=>{try{r||(await navigator.clipboard.writeText(e),n(3,r=!0),setTimeout(a,2e3))}catch{}}}=t;return e.$$set=e=>{"text"in e&&n(0,s=e.text),"hideText"in e&&n(1,o=e.hideText),"copy"in e&&n(2,i=e.copy)},[s,o,i,r,()=>{void 0!==s&&i(s)}]}class ri extends be{constructor(e){super(),ve(this,e,Du,Pu,_e,{text:0,hideText:1,copy:2})}}function oi(e,t,n){const s=e.slice();return s[1]=t[n],s}function ai(e,t,n){const s=e.slice();return s[4]=t[n][0],s[5]=t[n][1],s}function ci(e){let t,n,s,o;return t=new ri({props:{text:e[4]}}),s=new ri({props:{text:e[5],hideText:!0}}),{c(){Z(t.$$.fragment),n=B(),Z(s.$$.fragment)},l(e){X(t.$$.fragment,e),n=q(e),X(s.$$.fragment,e)},m(e,r){z(t,e,r),j(e,n,r),z(s,e,r),o=!0},p(e,n){const o={};1&n&&(o.text=e[4]),t.$set(o);const r={};1&n&&(r.text=e[5]),s.$set(r)},i(e){o||(T(t.$$.fragment,e),T(s.$$.fragment,e),o=!0)},o(e){P(t.$$.fragment,e),P(s.$$.fragment,e),o=!1},d(e){e&&p(n),J(t,e),J(s,e)}}}function ui(e){var t;let n,s,o=Ne(Object.entries(null==(t=e[1])?void 0:t.environmentVariables)),r=[];for(let t=0;t<o.length;t+=1)r[t]=ci(ai(e,o,t));const a=e=>P(r[e],1,1,(()=>{r[e]=null}));return{c(){for(let e=0;e<r.length;e+=1)r[e].c();n=fe()},l(e){for(let t=0;t<r.length;t+=1)r[t].l(e);n=fe()},m(e,t){for(let n=0;n<r.length;n+=1)r[n]&&r[n].m(e,t);j(e,n,t),s=!0},p(e,t){var s;if(1&t){let i;for(o=Ne(Object.entries(null==(s=e[1])?void 0:s.environmentVariables)),i=0;i<o.length;i+=1){const s=ai(e,o,i);r[i]?(r[i].p(s,t),T(r[i],1)):(r[i]=ci(s),r[i].c(),T(r[i],1),r[i].m(n.parentNode,n))}for(pe(),i=o.length;i<r.length;i+=1)a(i);ge()}},i(e){if(!s){for(let e=0;e<o.length;e+=1)T(r[e]);s=!0}},o(e){r=r.filter(Boolean);for(let e=0;e<r.length;e+=1)P(r[e]);s=!1},d(e){e&&p(n),at(r,e)}}}function Mu(e){let t,n,s,o,r='<span class="font-sm">Key</span> <span class="font-sm">Value</span>',a=Ne(e[0]),i=[];for(let t=0;t<a.length;t+=1)i[t]=ui(oi(e,a,t));const l=e=>P(i[e],1,1,(()=>{i[e]=null}));return{c(){t=w("div"),t.innerHTML=r,n=B(),s=w("div");for(let e=0;e<i.length;e+=1)i[e].c();this.h()},l(e){t=k(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-lgjfnf"!==oe(t)&&(t.innerHTML=r),n=q(e),s=k(e,"DIV",{class:!0});var o=M(s);for(let e=0;e<i.length;e+=1)i[e].l(o);o.forEach(p),this.h()},h(){E(t,"class","flex justify-between mb-2 font-medium text-sm text-base-content"),E(s,"class","grid grid-cols-2 gap-x-6 gap-y-4")},m(e,r){j(e,t,r),j(e,n,r),j(e,s,r);for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(s,null);o=!0},p(e,[t]){if(1&t){let n;for(a=Ne(e[0]),n=0;n<a.length;n+=1){const o=oi(e,a,n);i[n]?(i[n].p(o,t),T(i[n],1)):(i[n]=ui(o),i[n].c(),T(i[n],1),i[n].m(s,null))}for(pe(),n=a.length;n<i.length;n+=1)l(n);ge()}},i(e){if(!o){for(let e=0;e<a.length;e+=1)T(i[e]);o=!0}},o(e){i=i.filter(Boolean);for(let e=0;e<i.length;e+=1)P(i[e]);o=!1},d(e){e&&(p(t),p(n),p(s)),at(i,e)}}}function Vu(e,t,n){let{sources:s}=t;return e.$$set=e=>{"sources"in e&&n(0,s=e.sources)},[s]}class ju extends be{constructor(e){super(),ve(this,e,Vu,Mu,_e,{sources:0})}}function Fu(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S,C,N,A,O,_,L,D,V,F,H,K,U,R="Evidence Cloud",Y='<div class="flex items-center justify-center text-base-content font-bold border border-base-300 w-8 h-8 rounded-full shadow-sm tabular-nums">1</div> <h4 class="font-bold text-base-content">Check your project into version control</h4>',G="<p>Evidence Cloud deploys your project from its Github repository. As you make changes to your\n\t\t\t\tproject and commit them to main, Evidence cloud will update your deployed project.</p>",W='<div class="flex items-center justify-center text-base-content font-bold border border-base-300 w-8 h-8 rounded-full shadow-sm tabular-nums">2</div> <h4 class="font-bold text-base-content">Sign in to Evidence Cloud</h4>',Q='<p>Sign into Evidence Cloud using your GitHub account and add a new project. Follow the steps\n\t\t\t\tto connect to your Github repository.</p> <a href="https://evidence.app" target="_blank" class="inline-flex items-center justify-center rounded-md font-medium whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-base-300 disabled:pointer-events-none disabled:opacity-50 mt-4 w-full h-10 px-10 text-sm w-full bg-base-content text-base-100 shadow-sm hover:bg-base-content/90 active:bg-base-content/80">Sign In</a>',ee='<div class="flex items-center justify-center text-base-content font-bold border border-base-300 w-8 h-8 rounded-full shadow-sm tabular-nums">3</div> <h4 class="font-bold text-base-content">Set your Project Environment Variables</h4>',te="While you are setting up your cloud project, you'll be prompted for your environment\n\t\t\t\tvariables to connect Evidence cloud to your sources. Copy them here.",ne='<p class="text-base-content-muted">To use different connection settings your other deployment environment,\n\t\t\t\t\t<a class="underline underline-offset-2" href="https://docs.evidence.dev/deployment/environments" target="_blank">set different environment variable values in cloud</a>\n\t\t\t\t\t.</p>',se='<div class="flex items-center justify-center text-base-content font-bold border border-base-300 w-8 h-8 rounded-full shadow-sm tabular-nums">4</div> <h4 class="font-bold text-base-content">Done</h4>',re="<p>When you make changes to your project locally, push them to main, and Evidence cloud will\n\t\t\t\tupdate your deployed project.</p>",ae="Other Environments",ie='Documentation on deploying Evidence projects to a variety of cloud environments is available <a href="https://docs.evidence.dev/deployment/overview/" class="markdown" target="_blank">here.</a> For all deployment environments, you will need to set the environment variables using the key\n\t\t\tvalue pairs below.',le='<p class="text-base-content-muted">To use different connection settings your deployment environment,\n\t\t\t\t<a class="underline underline-offset-2" href="https://docs.evidence.dev/deployment/environments" target="_blank">set different environment variable values in your deployment environment</a>\n\t\t\t\t.</p>';return y=new Au({props:{sources:e[0]}}),F=new ju({props:{sources:e[0]}}),{c(){t=w("section"),n=w("div"),s=w("h3"),s.textContent=R,o=B(),r=w("div"),r.innerHTML=Y,a=B(),i=w("div"),i.innerHTML=G,l=B(),c=w("div"),c.innerHTML=W,u=B(),d=w("div"),d.innerHTML=Q,f=B(),m=w("div"),m.innerHTML=ee,h=B(),$=w("div"),v=w("p"),v.textContent=te,b=B(),Z(y.$$.fragment),x=B(),I=w("div"),I.innerHTML=ne,S=B(),C=w("div"),C.innerHTML=se,N=B(),A=w("div"),A.innerHTML=re,O=B(),_=w("h3"),_.textContent=ae,L=B(),D=w("p"),D.innerHTML=ie,V=B(),Z(F.$$.fragment),H=B(),K=w("div"),K.innerHTML=le,this.h()},l(e){t=k(e,"SECTION",{class:!0});var g=M(t);n=k(g,"DIV",{class:!0});var w=M(n);s=k(w,"H3",{class:!0,"data-svelte-h":!0}),"svelte-aahs3f"!==oe(s)&&(s.textContent=R),o=q(w),r=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-g44hse"!==oe(r)&&(r.innerHTML=Y),a=q(w),i=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-14f4l4r"!==oe(i)&&(i.innerHTML=G),l=q(w),c=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-hjxrdp"!==oe(c)&&(c.innerHTML=W),u=q(w),d=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1pktmmp"!==oe(d)&&(d.innerHTML=Q),f=q(w),m=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1et324z"!==oe(m)&&(m.innerHTML=ee),h=q(w),$=k(w,"DIV",{class:!0});var E=M($);v=k(E,"P",{"data-svelte-h":!0}),"svelte-n96f75"!==oe(v)&&(v.textContent=te),b=q(E),X(y.$$.fragment,E),x=q(E),I=k(E,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1kow9oa"!==oe(I)&&(I.innerHTML=ne),E.forEach(p),S=q(w),C=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1xhvdlg"!==oe(C)&&(C.innerHTML=se),N=q(w),A=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1rsdnpk"!==oe(A)&&(A.innerHTML=re),O=q(w),_=k(w,"H3",{class:!0,"data-svelte-h":!0}),"svelte-oks2a"!==oe(_)&&(_.textContent=ae),L=q(w),D=k(w,"P",{class:!0,"data-svelte-h":!0}),"svelte-1lzqb8o"!==oe(D)&&(D.innerHTML=ie),V=q(w),X(F.$$.fragment,w),H=q(w),K=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-17e3b2"!==oe(K)&&(K.innerHTML=le),w.forEach(p),g.forEach(p),this.h()},h(){E(s,"class","text-base-content text-lg font-semibold mt-0 mb-5"),E(r,"class","flex gap-4 items-center"),E(i,"class","pl-8 ml-[calc(1rem-0.5px)] pt-1 pb-10 border-l border-base-200"),E(c,"class","flex gap-4 items-center"),E(d,"class","pl-8 ml-[calc(1rem-0.5px)] pt-1 pb-10 border-l border-base-200"),E(m,"class","flex gap-4 items-center"),E(I,"class","mt-4"),E($,"class","pl-8 ml-[calc(1rem-0.5px)] pt-1 pb-10 border-l border-base-200"),E(C,"class","flex gap-4 items-center"),E(A,"class","pl-8 ml-[calc(1rem-0.5px)] pt-1 pb-4 border-l border-base-200"),E(_,"class","text-base-content text-lg font-semibold mt-0 mb-4 mt-8"),E(D,"class","text-base-content mb-4 text-pretty"),E(K,"class","mt-6"),E(n,"class","pb-4"),E(t,"class","w-full pt-2 pb-10")},m(e,p){j(e,t,p),g(t,n),g(n,s),g(n,o),g(n,r),g(n,a),g(n,i),g(n,l),g(n,c),g(n,u),g(n,d),g(n,f),g(n,m),g(n,h),g(n,$),g($,v),g($,b),z(y,$,null),g($,x),g($,I),g(n,S),g(n,C),g(n,N),g(n,A),g(n,O),g(n,_),g(n,L),g(n,D),g(n,V),z(F,n,null),g(n,H),g(n,K),U=!0},p(e,[t]){const n={};1&t&&(n.sources=e[0]),y.$set(n);const s={};1&t&&(s.sources=e[0]),F.$set(s)},i(e){U||(T(y.$$.fragment,e),T(F.$$.fragment,e),U=!0)},o(e){P(y.$$.fragment,e),P(F.$$.fragment,e),U=!1},d(e){e&&p(t),J(y),J(F)}}}function qu(e,t,n){let{sources:s}=t;return e.$$set=e=>{"sources"in e&&n(0,s=e.sources)},[s]}class Bu extends be{constructor(e){super(),ve(this,e,qu,Fu,_e,{sources:0})}}function fi(e,t,n){const s=e.slice();return s[2]=t[n],s[3]=t,s[4]=n,s}function di(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y=e[2].formatTag+"",x=e[2].formatCode+"",T=At(e[2])+"";function P(){e[1].call(c,e[3],e[4])}return{c(){t=w("tr"),n=w("td"),s=le(y),o=B(),r=w("td"),a=le(x),i=B(),l=w("td"),c=w("input"),f=B(),m=w("td"),h=le(T),$=B(),this.h()},l(e){t=k(e,"TR",{});var u=M(t);n=k(u,"TD",{});var d=M(n);s=ie(d,y),d.forEach(p),o=q(u),r=k(u,"TD",{});var g=M(r);a=ie(g,x),g.forEach(p),i=q(u),l=k(u,"TD",{});var v=M(l);c=k(v,"INPUT",{id:!0,placeholder:!0,class:!0}),v.forEach(p),f=q(u),m=k(u,"TD",{class:!0});var b=M(m);h=ie(b,T),b.forEach(p),$=q(u),u.forEach(p),this.h()},h(){E(c,"id",u="id_format_row"+e[2].formatTag),E(c,"placeholder",d=e[2].exampleInput||Ot(e[2].valueType)),E(c,"class","rounded shadow-sm border border-base-300 px-2 py-1 text-sm w-full bg-base-100 focus:ring-base-300 focus:border-base-300 focus:outline-none focus:ring-1"),E(m,"class","text-right max-w-0")},m(u,d){j(u,t,d),g(t,n),g(n,s),g(t,o),g(t,r),g(r,a),g(t,i),g(t,l),g(l,c),we(c,e[2].userInput),g(t,f),g(t,m),g(m,h),g(t,$),v||(b=[ce(c,"input",P),ce(c,"blur",(function(){ln(e[2].userInput=void 0)&&(e[2].userInput=void 0).apply(this,arguments)}))],v=!0)},p(t,n){e=t,1&n&&y!==(y=e[2].formatTag+"")&&ye(s,y),1&n&&x!==(x=e[2].formatCode+"")&&ye(a,x),1&n&&u!==(u="id_format_row"+e[2].formatTag)&&E(c,"id",u),1&n&&d!==(d=e[2].exampleInput||Ot(e[2].valueType))&&E(c,"placeholder",d),1&n&&c.value!==e[2].userInput&&we(c,e[2].userInput),1&n&&T!==(T=At(e[2])+"")&&ye(h,T)},d(e){e&&p(t),v=!1,Je(b)}}}function Hu(e){let t,n,s,o='<th class="max-w-14 text-left font-medium">Format Name</th> <th class="min-w-20 text-left font-medium">Format Code</th> <th class="min-w-20 text-left font-medium">Example Input</th> <th class="min-w-20 text-right font-medium">Example Output</th>',r=Ne(e[0]),a=[];for(let t=0;t<r.length;t+=1)a[t]=di(fi(e,r,t));return{c(){t=w("table"),n=w("thead"),n.innerHTML=o,s=B();for(let e=0;e<a.length;e+=1)a[e].c();this.h()},l(e){t=k(e,"TABLE",{class:!0});var r=M(t);n=k(r,"THEAD",{class:!0,"data-svelte-h":!0}),"svelte-1byu907"!==oe(n)&&(n.innerHTML=o),s=q(r);for(let e=0;e<a.length;e+=1)a[e].l(r);r.forEach(p),this.h()},h(){E(n,"class","text-sm py-2"),E(t,"class","w-full border-separate [border-spacing:0.5rem_0.5rem] -mx-2")},m(e,o){j(e,t,o),g(t,n),g(t,s);for(let e=0;e<a.length;e+=1)a[e]&&a[e].m(t,null)},p(e,[n]){if(1&n){let s;for(r=Ne(e[0]),s=0;s<r.length;s+=1){const o=fi(e,r,s);a[s]?a[s].p(o,n):(a[s]=di(o),a[s].c(),a[s].m(t,null))}for(;s<a.length;s+=1)a[s].d(1);a.length=r.length}},i:re,o:re,d(e){e&&p(t),at(a,e)}}}function Ru(e,t,n){let{formats:s}=t;return e.$$set=e=>{"formats"in e&&n(0,s=e.formats)},[s,function(e,t){e[t].userInput=this.value,n(0,s)}]}class es extends be{constructor(e){super(),ve(this,e,Ru,Hu,_e,{formats:0})}}function mi(e,t,n){const s=e.slice();return s[5]=t[n],s[6]=t,s[7]=n,s}function Ku(e,t,n){const s=e.slice();return s[8]=t[n],s}function Uu(e){let t,n,s=e[8].displayName+"";return{c(){t=w("option"),n=le(s),this.h()},l(e){t=k(e,"OPTION",{name:!0,id:!0});var o=M(t);n=ie(o,s),o.forEach(p),this.h()},h(){E(t,"name",e[8].primaryCode),E(t,"id",e[8].primaryCode),t.__value=e[8].primaryCode,we(t,t.__value)},m(e,s){j(e,t,s),g(t,n)},p:re,d(e){e&&p(t)}}}function hi(e){let t,n,s,o,r,a,i='<th class="max-w-14 text-left font-medium">Format Name</th> <th class="min-w-20 text-left font-medium">Format Code</th> <th class="min-w-20 text-left font-medium">Example Input</th> <th class="min-w-20 text-right font-medium">Example Output</th>',l=[],c=new Map,u=Ne(e[0].filter(e[3]));const d=e=>e[5].formatTag;for(let t=0;t<u.length;t+=1){let n=mi(e,u,t),s=d(n);c.set(s,l[t]=pi(s,n))}return{c(){t=w("div"),n=w("table"),s=w("thead"),s.innerHTML=i,o=B();for(let e=0;e<l.length;e+=1)l[e].c();this.h()},l(e){t=k(e,"DIV",{});var r=M(t);n=k(r,"TABLE",{class:!0});var a=M(n);s=k(a,"THEAD",{class:!0,"data-svelte-h":!0}),"svelte-1i2g6gp"!==oe(s)&&(s.innerHTML=i),o=q(a);for(let e=0;e<l.length;e+=1)l[e].l(a);a.forEach(p),r.forEach(p),this.h()},h(){E(s,"class","text-sm py-2"),E(n,"class","w-full border-separate [border-spacing:0.5rem_0.5rem] -mx-2")},m(e,r){j(e,t,r),g(t,n),g(n,s),g(n,o);for(let e=0;e<l.length;e+=1)l[e]&&l[e].m(n,null);a=!0},p(e,t){3&t&&(u=Ne(e[0].filter(e[3])),l=Un(l,t,d,1,e,u,c,n,hr,pi,null,mi))},i(e){if(!a){for(let e=0;e<u.length;e+=1)T(l[e]);e&&ke((()=>{a&&(r||(r=nt(t,dt,{},!0)),r.run(1))})),a=!0}},o(e){e&&(r||(r=nt(t,dt,{},!1)),r.run(0)),a=!1},d(e){e&&p(t);for(let e=0;e<l.length;e+=1)l[e].d();e&&r&&r.end()}}}function pi(e,t){let n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,T,P,I=t[5].formatTag+"",S=t[5].formatCode+"",C=At(t[5])+"";function N(){t[4].call(f,t[6],t[7])}return{key:e,first:null,c(){n=w("tr"),s=w("td"),o=le(I),a=B(),i=w("td"),l=le(S),u=B(),d=w("td"),f=w("input"),$=B(),v=w("td"),b=le(C),x=B(),this.h()},l(e){n=k(e,"TR",{});var t=M(n);s=k(t,"TD",{});var r=M(s);o=ie(r,I),r.forEach(p),a=q(t),i=k(t,"TD",{});var c=M(i);l=ie(c,S),c.forEach(p),u=q(t),d=k(t,"TD",{});var m=M(d);f=k(m,"INPUT",{id:!0,placeholder:!0,class:!0}),m.forEach(p),$=q(t),v=k(t,"TD",{class:!0});var h=M(v);b=ie(h,C),h.forEach(p),x=q(t),t.forEach(p),this.h()},h(){E(f,"id",m="id_format_row"+t[5].formatTag),E(f,"placeholder",h=t[5].exampleInput||Ot(t[5].valueType)),E(f,"class","rounded shadow-sm border border-base-300 px-2 py-1 text-sm w-full bg-base-100 focus:ring-base-300 focus:border-base-300 focus:outline-none focus:ring-1"),E(v,"class","text-right max-w-0"),this.first=n},m(e,r){j(e,n,r),g(n,s),g(s,o),g(n,a),g(n,i),g(i,l),g(n,u),g(n,d),g(d,f),we(f,t[5].userInput),g(n,$),g(n,v),g(v,b),g(n,x),T||(P=[ce(f,"input",N),ce(f,"blur",(function(){ln(t[5].userInput=void 0)&&(t[5].userInput=void 0).apply(this,arguments)}))],T=!0)},p(e,n){t=e,3&n&&I!==(I=t[5].formatTag+"")&&ye(o,I),3&n&&S!==(S=t[5].formatCode+"")&&ye(l,S),3&n&&m!==(m="id_format_row"+t[5].formatTag)&&E(f,"id",m),3&n&&h!==(h=t[5].exampleInput||Ot(t[5].valueType))&&E(f,"placeholder",h),3&n&&f.value!==t[5].userInput&&we(f,t[5].userInput),3&n&&C!==(C=At(t[5])+"")&&ye(b,C)},i(e){e&&(r||ke((()=>{r=Ae(s,Xe,{}),r.start()}))),e&&(c||ke((()=>{c=Ae(i,Xe,{}),c.start()}))),e&&(y||ke((()=>{y=Ae(v,Xe,{}),y.start()})))},o:re,d(e){e&&p(n),T=!1,Je(P)}}}function Gu(e){let t,n,s,o,r,a,i,l,c="Choose a currency",u=Ne(yr),d=[];for(let t=0;t<u.length;t+=1)d[t]=Uu(Ku(e,u,t));let f="Choose a currency"!=e[1]&&hi(e);return{c(){t=w("div"),n=w("select"),s=w("option"),s.textContent=c,o=le("\n\t\t$");for(let e=0;e<d.length;e+=1)d[e].c();r=B(),f&&f.c(),a=fe(),this.h()},l(e){t=k(e,"DIV",{class:!0});var i=M(t);n=k(i,"SELECT",{class:!0});var l=M(n);s=k(l,"OPTION",{"data-svelte-h":!0}),"svelte-877q5p"!==oe(s)&&(s.textContent=c),o=ie(l,"\n\t\t$");for(let e=0;e<d.length;e+=1)d[e].l(l);l.forEach(p),i.forEach(p),r=q(e),f&&f.l(e),a=fe(),this.h()},h(){s.__value="Choose a currency",we(s,s.__value),E(n,"class","w-full rounded-md shadow-sm border border-base-300 px-3 h-9 py-2 text-sm bg-base-100 focus:ring-base-300 focus:border-base-300 focus:outline-none focus:ring-1 cursor-pointer mt-1 mb-2"),void 0===e[1]&&ke((()=>e[2].call(n))),E(t,"class","flex justify-center px-1")},m(c,u){j(c,t,u),g(t,n),g(n,s),g(n,o);for(let e=0;e<d.length;e+=1)d[e]&&d[e].m(n,null);St(n,e[1],!0),j(c,r,u),f&&f.m(c,u),j(c,a,u),i||(l=ce(n,"change",e[2]),i=!0)},p(e,[t]){2&t&&St(n,e[1]),"Choose a currency"!=e[1]?f?(f.p(e,t),2&t&&T(f,1)):(f=hi(e),f.c(),T(f,1),f.m(a.parentNode,a)):f&&(pe(),P(f,1,1,(()=>{f=null})),ge())},i(e){T(f)},o(e){P(f)},d(e){e&&(p(t),p(r),p(a)),at(d,e),f&&f.d(e),i=!1,l()}}}function Yu(e,t,n){let{formats:s}=t,o="Choose a currency";return e.$$set=e=>{"formats"in e&&n(0,s=e.formats)},[s,o,function(){o=jn(this),n(1,o)},e=>e.parentFormat===o,function(e,t){e[t].userInput=this.value,n(0,s),n(1,o)}]}class Wu extends be{constructor(e){super(),ve(this,e,Yu,Gu,_e,{formats:0})}}function gi(e,t,n){const s=e.slice();return s[4]=t[n],s[5]=t,s[6]=n,s}function Ju(e){let t;return{c(){t=le("Delete")},l(e){t=ie(e,"Delete")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function _i(e,t){let n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S,C,N,A=t[4].formatTag+"",O=t[4].formatCode+"",_=At(t[4])+"",L=re;function D(){t[2].call(u,t[5],t[6])}return y=new We({props:{type:"button",variant:"ghost",size:"sm",$$slots:{default:[Ju]},$$scope:{ctx:t}}}),y.$on("click",(function(){return t[3](t[4])})),{key:e,first:null,c(){n=w("tr"),s=w("td"),o=le(A),r=B(),a=w("td"),i=le(O),l=B(),c=w("td"),u=w("input"),m=B(),h=w("td"),$=le(_),v=B(),b=w("td"),Z(y.$$.fragment),x=B(),this.h()},l(e){n=k(e,"TR",{});var t=M(n);s=k(t,"TD",{});var d=M(s);o=ie(d,A),d.forEach(p),r=q(t),a=k(t,"TD",{});var f=M(a);i=ie(f,O),f.forEach(p),l=q(t),c=k(t,"TD",{});var g=M(c);u=k(g,"INPUT",{id:!0,placeholder:!0,class:!0}),g.forEach(p),m=q(t),h=k(t,"TD",{class:!0});var w=M(h);$=ie(w,_),w.forEach(p),v=q(t),b=k(t,"TD",{class:!0});var E=M(b);X(y.$$.fragment,E),E.forEach(p),x=q(t),t.forEach(p),this.h()},h(){E(u,"id",d="id_format_row"+t[4].formatTag),E(u,"placeholder",f=t[4].exampleInput||Ot(t[4].valueType)),E(u,"class","rounded shadow-sm border border-base-300 px-2 py-1 text-sm w-full bg-base-100 focus:ring-base-300 focus:border-base-300 focus:outline-none focus:ring-1"),E(h,"class","text-right max-w-0"),E(b,"class","flex justify-end"),this.first=n},m(e,d){j(e,n,d),g(n,s),g(s,o),g(n,r),g(n,a),g(a,i),g(n,l),g(n,c),g(c,u),we(u,t[4].userInput),g(n,m),g(n,h),g(h,$),g(n,v),g(n,b),z(y,b,null),g(n,x),S=!0,C||(N=[ce(u,"input",D),ce(u,"blur",(function(){ln(t[4].userInput=void 0)&&(t[4].userInput=void 0).apply(this,arguments)}))],C=!0)},p(e,n){t=e,(!S||1&n)&&A!==(A=t[4].formatTag+"")&&ye(o,A),(!S||1&n)&&O!==(O=t[4].formatCode+"")&&ye(i,O),(!S||1&n&&d!==(d="id_format_row"+t[4].formatTag))&&E(u,"id",d),(!S||1&n&&f!==(f=t[4].exampleInput||Ot(t[4].valueType)))&&E(u,"placeholder",f),1&n&&u.value!==t[4].userInput&&we(u,t[4].userInput),(!S||1&n)&&_!==(_=At(t[4])+"")&&ye($,_);const s={};128&n&&(s.$$scope={dirty:n,ctx:t}),y.$set(s)},r(){I=n.getBoundingClientRect()},f(){Nr(n),L()},a(){L(),L=Ir(n,I,ha,{})},i(e){S||(T(y.$$.fragment,e),S=!0)},o(e){P(y.$$.fragment,e),S=!1},d(e){e&&p(n),J(y),C=!1,Je(N)}}}function zu(e){let t,n,s,o,r='<th class="max-w-18 text-left font-medium">Format Name</th> <th class="max-w-18 text-left font-medium">Format Code</th> <th class="min-w-20 text-left font-medium">Example Input</th> <th class="min-w-20 text-right font-medium">Example Output</th> <th class="max-w-8 text-right"></th>',a=[],i=new Map,l=Ne(e[0]);const c=e=>e[4].formatTag;for(let t=0;t<l.length;t+=1){let n=gi(e,l,t),s=c(n);i.set(s,a[t]=_i(s,n))}return{c(){t=w("table"),n=w("thead"),n.innerHTML=r,s=B();for(let e=0;e<a.length;e+=1)a[e].c();this.h()},l(e){t=k(e,"TABLE",{class:!0});var o=M(t);n=k(o,"THEAD",{class:!0,"data-svelte-h":!0}),"svelte-1epeemt"!==oe(n)&&(n.innerHTML=r),s=q(o);for(let e=0;e<a.length;e+=1)a[e].l(o);o.forEach(p),this.h()},h(){E(n,"class","text-sm py-2"),E(t,"class","w-full border-separate [border-spacing:0.5rem_0.5rem] -mx-2")},m(e,r){j(e,t,r),g(t,n),g(t,s);for(let e=0;e<a.length;e+=1)a[e]&&a[e].m(t,null);o=!0},p(e,[n]){if(3&n){l=Ne(e[0]),pe();for(let e=0;e<a.length;e+=1)a[e].r();a=Un(a,n,c,1,e,l,i,t,pr,_i,null,gi);for(let e=0;e<a.length;e+=1)a[e].a();ge()}},i(e){if(!o){for(let e=0;e<l.length;e+=1)T(a[e]);o=!0}},o(e){for(let e=0;e<a.length;e+=1)P(a[e]);o=!1},d(e){e&&p(t);for(let e=0;e<a.length;e+=1)a[e].d()}}}function Xu(e,t,n){let{formats:s}=t,{deleteHandler:o}=t;return e.$$set=e=>{"formats"in e&&n(0,s=e.formats),"deleteHandler"in e&&n(1,o=e.deleteHandler)},[s,o,function(e,t){e[t].userInput=this.value,n(0,s)},e=>o(e)]}class Zu extends be{constructor(e){super(),ve(this,e,Xu,zu,_e,{formats:0,deleteHandler:1})}}function bi(e,t,n){const s=e.slice();return s[14]=t[n],s}function vi(e){let t,n,s,o;return n=new Gn({props:{$$slots:{default:[$u]},$$scope:{ctx:e}}}),{c(){t=w("div"),Z(n.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0});var s=M(t);X(n.$$.fragment,s),s.forEach(p),this.h()},h(){E(t,"class","my-4")},m(e,s){j(e,t,s),z(n,t,null),o=!0},p(e,t){const s={};131073&t&&(s.$$scope={dirty:t,ctx:e}),n.$set(s)},i(e){o||(T(n.$$.fragment,e),e&&ke((()=>{o&&(s||(s=nt(t,dt,{},!0)),s.run(1))})),o=!0)},o(e){P(n.$$.fragment,e),e&&(s||(s=nt(t,dt,{},!1)),s.run(0)),o=!1},d(e){e&&p(t),J(n),e&&s&&s.end()}}}function Qu(e){let t,n;return t=new Zu({props:{formats:e[0].customFormats,deleteHandler:e[6]}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};1&n&&(s.formats=e[0].customFormats),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function $u(e){let t,n;return t=new Tt({props:{title:"Saved Custom Formats",$$slots:{default:[Qu]},$$scope:{ctx:e}}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};131073&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function yi(e){let t,n,s,o=e[14]+"";return{c(){t=w("option"),n=le(o),s=B(),this.h()},l(e){t=k(e,"OPTION",{});var r=M(t);n=ie(r,o),s=q(r),r.forEach(p),this.h()},h(){t.__value=e[14],we(t,t.__value)},m(e,o){j(e,t,o),g(t,n),g(t,s)},p:re,d(e){e&&p(t)}}}function xu(e){let t;return{c(){t=le("Add Custom Format")},l(e){t=ie(e,"Add Custom Format")},m(e,n){j(e,t,n)},d(e){e&&p(t)}}}function ef(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S,C,N,A,O,_,L="Value Type",D="Format Name",V="Format Code",F=e[0].customFormats&&e[0].customFormats.length>0&&vi(e),H=Ne(e[5]),K=[];for(let t=0;t<H.length;t+=1)K[t]=yi(bi(e,H,t));return I=new We({props:{type:"submit",size:"lg",disabled:!(e[1]&&e[2]),$$slots:{default:[xu]},$$scope:{ctx:e}}}),{c(){F&&F.c(),t=B(),n=w("form"),s=w("div"),o=w("div"),r=w("label"),r.textContent=L,a=B(),i=w("select");for(let e=0;e<K.length;e+=1)K[e].c();l=B(),c=w("div"),u=w("label"),u.textContent=D,d=B(),f=w("input"),m=B(),h=w("div"),$=w("label"),$.textContent=V,v=B(),b=w("input"),x=B(),Z(I.$$.fragment),S=B(),C=w("div"),N=new Ei(!1),this.h()},l(e){F&&F.l(e),t=q(e),n=k(e,"FORM",{autocomplete:!0,class:!0});var g=M(n);s=k(g,"DIV",{class:!0});var y=M(s);o=k(y,"DIV",{class:!0});var w=M(o);r=k(w,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-1ynqoxn"!==oe(r)&&(r.textContent=L),a=q(w),i=k(w,"SELECT",{id:!0,class:!0});var E=M(i);for(let e=0;e<K.length;e+=1)K[e].l(E);E.forEach(p),w.forEach(p),l=q(y),c=k(y,"DIV",{class:!0});var T=M(c);u=k(T,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-oanagg"!==oe(u)&&(u.textContent=D),d=q(T),f=k(T,"INPUT",{id:!0,type:!0,placeholder:!0,class:!0}),T.forEach(p),m=q(y),h=k(y,"DIV",{class:!0});var P=M(h);$=k(P,"LABEL",{for:!0,class:!0,"data-svelte-h":!0}),"svelte-1vghyh5"!==oe($)&&($.textContent=V),v=q(P),b=k(P,"INPUT",{id:!0,type:!0,placeholder:!0,class:!0}),P.forEach(p),x=q(y),X(I.$$.fragment,y),S=q(y),C=k(y,"DIV",{class:!0});var A=M(C);N=Ti(A,!1),A.forEach(p),y.forEach(p),g.forEach(p),this.h()},h(){E(r,"for","valueType"),E(r,"class","text-sm font-medium text-base-content"),E(i,"id","valueType"),E(i,"class","flex-1 border border-base-300 bg-base-100 shadow-sm text-sm h-9 bg-transparent px-3 py-2 transition-colors focus-visible:outline-none rounded-md cursor-pointer"),void 0===e[3]&&ke((()=>e[9].call(i))),E(o,"class","flex flex-col gap-2"),E(u,"for","formatTag"),E(u,"class","text-sm font-medium text-base-content"),E(f,"id","formatTag"),E(f,"type","text"),E(f,"placeholder","myformat"),E(f,"class","flex-1 border border-base-300 bg-base-100 shadow-sm text-sm h-9 bg-transparent px-3 py-2 transition-colors focus-visible:outline-none rounded-md"),E(c,"class","flex flex-col gap-2"),E($,"for","formatCode"),E($,"class","text-sm font-medium text-base-content"),E(b,"id","formatCode"),E(b,"type","text"),E(b,"placeholder",y="date"===e[3]?"mm/dd/yyyy":"$#,##0.0"),E(b,"class","flex-1 border border-base-300 bg-base-100 shadow-sm text-sm h-9 bg-transparent px-3 py-2 transition-colors focus-visible:outline-none rounded-md"),E(h,"class","flex flex-col gap-2"),N.a=null,E(C,"class","text-negative text-sm"),E(s,"class","flex flex-col gap-4"),E(n,"autocomplete","off"),E(n,"class","my-6")},m(p,y){F&&F.m(p,y),j(p,t,y),j(p,n,y),g(n,s),g(s,o),g(o,r),g(o,a),g(o,i);for(let e=0;e<K.length;e+=1)K[e]&&K[e].m(i,null);St(i,e[3],!0),g(s,l),g(s,c),g(c,u),g(c,d),g(c,f),we(f,e[1]),g(s,m),g(s,h),g(h,$),g(h,v),g(h,b),we(b,e[2]),g(s,x),z(I,s,null),g(s,S),g(s,C),N.m(e[4],C),A=!0,O||(_=[ce(i,"change",e[9]),ce(f,"input",e[10]),ce(b,"input",e[11]),ce(n,"submit",Ci(e[7]))],O=!0)},p(e,[n]){if(e[0].customFormats&&e[0].customFormats.length>0?F?(F.p(e,n),1&n&&T(F,1)):(F=vi(e),F.c(),T(F,1),F.m(t.parentNode,t)):F&&(pe(),P(F,1,1,(()=>{F=null})),ge()),32&n){let t;for(H=Ne(e[5]),t=0;t<H.length;t+=1){const s=bi(e,H,t);K[t]?K[t].p(s,n):(K[t]=yi(s),K[t].c(),K[t].m(i,null))}for(;t<K.length;t+=1)K[t].d(1);K.length=H.length}40&n&&St(i,e[3]),2&n&&f.value!==e[1]&&we(f,e[1]),(!A||40&n&&y!==(y="date"===e[3]?"mm/dd/yyyy":"$#,##0.0"))&&E(b,"placeholder",y),4&n&&b.value!==e[2]&&we(b,e[2]);const s={};6&n&&(s.disabled=!(e[1]&&e[2])),131072&n&&(s.$$scope={dirty:n,ctx:e}),I.$set(s),(!A||16&n)&&N.p(e[4])},i(e){A||(T(F),T(I.$$.fragment,e),A=!0)},o(e){P(F),P(I.$$.fragment,e),A=!1},d(e){e&&(p(t),p(n)),F&&F.d(e),at(K,e),J(I),O=!1,Je(_)}}}function tf(e,t,n){let{builtInFormats:s=[]}=t,{customFormattingSettings:o={}}=t;const r=["number","date"];let a="",i="",l="number",c="";return e.$$set=e=>{"builtInFormats"in e&&n(8,s=e.builtInFormats),"customFormattingSettings"in e&&n(0,o=e.customFormattingSettings)},[o,a,i,l,c,r,async function(e){let t=await(await fetch(Ln("/api/customFormattingSettings.json"),{method:"DELETE",body:JSON.stringify({formatTag:e.formatTag})})).json();t&&n(0,o=t)},async function(){let e=function(){var e;let t=[];/^[a-zA-Z][a-zA-Z0-9]*$/.test(a)||t.push(`"${a}" is not a valid format name. The format name should always start with a letter and only contain letters and numbers.`);let n,r,c=10;"date"===l&&(c=new Date);try{n=kr.format(i,c)}catch(e){r=e}return n||t.push(`Format "${i}" is invalid for type "${l}".`),r&&t.push(r),(s.find((e=>e.formatTag===a))||null!=(e=o.customFormats)&&e.find((e=>e.formatTag===a)))&&t.push(`The format name "${a}"" is already assigned to an existing format.`),t}();if(e&&e.length>0)n(4,c=e.join("<br/>"));else{let e=await(await fetch(Ln("/api/customFormattingSettings.json"),{method:"POST",body:JSON.stringify({newCustomFormat:{formatTag:a,formatCode:i,valueType:l}})})).json();e?(n(0,o=e),n(1,a=""),n(2,i=""),n(3,l="number"),n(4,c="")):n(4,c=`Unable to create new custom format ${a}`)}},s,function(){l=jn(this),n(3,l),n(5,r)},function(){a=this.value,n(1,a)},function(){i=this.value,n(2,i)}]}class nf extends be{constructor(e){super(),ve(this,e,tf,ef,_e,{builtInFormats:8,customFormattingSettings:0})}}function sf(e){let t,n;return t=new es({props:{formats:Yt.filter(uf)}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function lf(e){let t,n;return t=new Wu({props:{formats:Yt.filter(ff)}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function rf(e){let t,n;return t=new es({props:{formats:Yt.filter(df)}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function of(e){let t,n;return t=new es({props:{formats:Yt.filter(mf)}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p:re,i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function af(e){let t,n,s,o,r,a,i,l;return t=new Tt({props:{title:"Dates",$$slots:{default:[sf]},$$scope:{ctx:e}}}),s=new Tt({props:{title:"Currencies",$$slots:{default:[lf]},$$scope:{ctx:e}}}),r=new Tt({props:{title:"Numbers",$$slots:{default:[rf]},$$scope:{ctx:e}}}),i=new Tt({props:{title:"Percentages",$$slots:{default:[of]},$$scope:{ctx:e}}}),{c(){Z(t.$$.fragment),n=B(),Z(s.$$.fragment),o=B(),Z(r.$$.fragment),a=B(),Z(i.$$.fragment)},l(e){X(t.$$.fragment,e),n=q(e),X(s.$$.fragment,e),o=q(e),X(r.$$.fragment,e),a=q(e),X(i.$$.fragment,e)},m(e,c){z(t,e,c),j(e,n,c),z(s,e,c),j(e,o,c),z(r,e,c),j(e,a,c),z(i,e,c),l=!0},p(e,n){const o={};16&n&&(o.$$scope={dirty:n,ctx:e}),t.$set(o);const a={};16&n&&(a.$$scope={dirty:n,ctx:e}),s.$set(a);const l={};16&n&&(l.$$scope={dirty:n,ctx:e}),r.$set(l);const c={};16&n&&(c.$$scope={dirty:n,ctx:e}),i.$set(c)},i(e){l||(T(t.$$.fragment,e),T(s.$$.fragment,e),T(r.$$.fragment,e),T(i.$$.fragment,e),l=!0)},o(e){P(t.$$.fragment,e),P(s.$$.fragment,e),P(r.$$.fragment,e),P(i.$$.fragment,e),l=!1},d(e){e&&(p(n),p(o),p(a)),J(t,e),J(s,e),J(r,e),J(i,e)}}}function cf(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S,C,N,A,O,_,L,D,V,F,H="Using Formats",K="In the Value component, you can use the <code>fmt</code> prop",U="In charts, you can use the <code>xFmt</code> and <code>yFmt</code> props",R="You can also set formats within your SQL queries using SQL format tags. Use these by aliasing\n\t\t\tyour column names and appending a format. For example:",Y="Builtin Formats",G="All built-in formats are listed below for reference.",W="Custom Formats",Q='Add new formats to your project. Custom formats use <a class="markdown" target="_blank" rel="noreferrer" href="https://support.microsoft.com/en-us/office/number-format-codes-5026bbd6-04bc-48cd-bf33-80f18b4eae68">excel-style format codes</a> and are saved in your project.';return i=new kn({props:{source:e[3],language:"svelte"}}),d=new kn({props:{source:e[2],language:"svelte"}}),$=new kn({props:{source:e[1],language:"sql"}}),C=new Gn({props:{single:!0,$$slots:{default:[af]},$$scope:{ctx:e}}}),V=new nf({props:{builtInFormats:Yt,customFormattingSettings:e[0]}}),{c(){t=w("section"),n=w("div"),s=w("h3"),s.textContent=H,o=B(),r=w("p"),r.innerHTML=K,a=B(),Z(i.$$.fragment),l=B(),c=w("p"),c.innerHTML=U,u=B(),Z(d.$$.fragment),f=B(),m=w("p"),m.textContent=R,h=B(),Z($.$$.fragment),v=B(),b=w("div"),y=w("h3"),y.textContent=Y,x=B(),I=w("p"),I.textContent=G,S=B(),Z(C.$$.fragment),N=B(),A=w("div"),O=w("h3"),O.textContent=W,_=B(),L=w("p"),L.innerHTML=Q,D=B(),Z(V.$$.fragment),this.h()},l(e){t=k(e,"SECTION",{class:!0});var g=M(t);n=k(g,"DIV",{});var w=M(n);s=k(w,"H3",{class:!0,"data-svelte-h":!0}),"svelte-1pu7as6"!==oe(s)&&(s.textContent=H),o=q(w),r=k(w,"P",{class:!0,"data-svelte-h":!0}),"svelte-122umf8"!==oe(r)&&(r.innerHTML=K),a=q(w),X(i.$$.fragment,w),l=q(w),c=k(w,"P",{class:!0,"data-svelte-h":!0}),"svelte-si2kxx"!==oe(c)&&(c.innerHTML=U),u=q(w),X(d.$$.fragment,w),f=q(w),m=k(w,"P",{class:!0,"data-svelte-h":!0}),"svelte-vo6frm"!==oe(m)&&(m.textContent=R),h=q(w),X($.$$.fragment,w),w.forEach(p),v=q(g),b=k(g,"DIV",{});var E=M(b);y=k(E,"H3",{class:!0,"data-svelte-h":!0}),"svelte-4px9uh"!==oe(y)&&(y.textContent=Y),x=q(E),I=k(E,"P",{"data-svelte-h":!0}),"svelte-1uuy1j1"!==oe(I)&&(I.textContent=G),S=q(E),X(C.$$.fragment,E),E.forEach(p),N=q(g),A=k(g,"DIV",{});var T=M(A);O=k(T,"H3",{class:!0,"data-svelte-h":!0}),"svelte-dhuomx"!==oe(O)&&(O.textContent=W),_=q(T),L=k(T,"P",{"data-svelte-h":!0}),"svelte-14o116d"!==oe(L)&&(L.innerHTML=Q),D=q(T),X(V.$$.fragment,T),T.forEach(p),g.forEach(p),this.h()},h(){E(s,"class","text-base-content text-lg font-semibold mb-2"),E(r,"class","markdown"),E(c,"class","markdown"),E(m,"class","markdown"),E(y,"class","text-base-content text-lg font-semibold mb-2"),E(O,"class","text-base-content text-lg font-semibold mb-2"),E(t,"class","flex flex-col gap-6")},m(e,p){j(e,t,p),g(t,n),g(n,s),g(n,o),g(n,r),g(n,a),z(i,n,null),g(n,l),g(n,c),g(n,u),z(d,n,null),g(n,f),g(n,m),g(n,h),z($,n,null),g(t,v),g(t,b),g(b,y),g(b,x),g(b,I),g(b,S),z(C,b,null),g(t,N),g(t,A),g(A,O),g(A,_),g(A,L),g(A,D),z(V,A,null),F=!0},p(e,[t]){const n={};16&t&&(n.$$scope={dirty:t,ctx:e}),C.$set(n);const s={};1&t&&(s.customFormattingSettings=e[0]),V.$set(s)},i(e){F||(T(i.$$.fragment,e),T(d.$$.fragment,e),T($.$$.fragment,e),T(C.$$.fragment,e),T(V.$$.fragment,e),F=!0)},o(e){P(i.$$.fragment,e),P(d.$$.fragment,e),P($.$$.fragment,e),P(C.$$.fragment,e),P(V.$$.fragment,e),F=!1},d(e){e&&p(t),J(i),J(d),J($),J(C),J(V)}}}const uf=e=>"date"===e.formatCategory,ff=e=>"currency"===e.formatCategory,df=e=>"number"===e.formatCategory,mf=e=>"percent"===e.formatCategory;function hf(e,t,n){let{customFormattingSettings:s}=t;return e.$$set=e=>{"customFormattingSettings"in e&&n(0,s=e.customFormattingSettings)},[s,"select \n  growth as growth_pct, -- formatted as a percentage\n  sales as sales_usd    -- formatted as US dollars\nfrom table","<LineChart\n\tdata={sales_data}\n\tx=date\n\ty=sales\n\tyFmt=euro\n/>","<Value data={sales_data} column=sales fmt='$#,##0' />"]}class pf extends be{constructor(e){super(),ve(this,e,hf,cf,_e,{customFormattingSettings:0})}}function gf(e){let t,n,s,o,r,a,i,l,c="Sharing anonymous CLI usage data is one of the best ways you can support Evidence.";function u(t){e[3](t)}let d={id:"telemetry-toggle"};return void 0!==e[0]&&(d.checked=e[0]),a=new Yn({props:d}),Ee.push((()=>Ge(a,"checked",u))),a.$on("change",e[1]),{c(){t=w("p"),t.textContent=c,n=B(),s=w("form"),o=w("label"),r=le("Share anonymous usage data\n\t\t\t\t\t"),Z(a.$$.fragment),this.h()},l(e){t=k(e,"P",{class:!0,"data-svelte-h":!0}),"svelte-1ysee3z"!==oe(t)&&(t.textContent=c),n=q(e),s=k(e,"FORM",{id:!0});var i=M(s);o=k(i,"LABEL",{for:!0,class:!0});var l=M(o);r=ie(l,"Share anonymous usage data\n\t\t\t\t\t"),X(a.$$.fragment,l),l.forEach(p),i.forEach(p),this.h()},h(){E(t,"class","markdown mb-1 text-pretty"),E(o,"for","telemetry-toggle"),E(o,"class","flex justify-between gap-2 items-center pt-4 mt-4 font-medium"),E(s,"id","telemetry")},m(e,i){j(e,t,i),j(e,n,i),j(e,s,i),g(s,o),g(o,r),z(a,o,null),l=!0},p(e,t){const n={};!i&&1&t&&(i=!0,n.checked=e[0],Ue((()=>i=!1))),a.$set(n)},i(e){l||(T(a.$$.fragment,e),l=!0)},o(e){P(a.$$.fragment,e),l=!1},d(e){e&&(p(t),p(n),p(s)),J(a)}}}function _f(e){let t,n;return t=new Tt({props:{title:"Options",$$slots:{default:[gf]},$$scope:{ctx:e}}}),{c(){Z(t.$$.fragment)},l(e){X(t.$$.fragment,e)},m(e,s){z(t,e,s),n=!0},p(e,n){const s={};17&n&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){n||(T(t.$$.fragment,e),n=!0)},o(e){P(t.$$.fragment,e),n=!1},d(e){J(t,e)}}}function bf(e){let t,n,s;return n=new Gn({props:{$$slots:{default:[_f]},$$scope:{ctx:e}}}),{c(){t=w("div"),Z(n.$$.fragment)},l(e){t=k(e,"DIV",{});var s=M(t);X(n.$$.fragment,s),s.forEach(p)},m(e,o){j(e,t,o),z(n,t,null),s=!0},p(e,[t]){const s={};17&t&&(s.$$scope={dirty:t,ctx:e}),n.$set(s)},i(e){s||(T(n.$$.fragment,e),s=!0)},o(e){P(n.$$.fragment,e),s=!1},d(e){e&&p(t),J(n)}}}function vf(e,t,n){let{settings:s}=t,o="yes"===(s.send_anonymous_usage_stats??"yes");return e.$$set=e=>{"settings"in e&&n(2,s=e.settings)},[o,async function(){n(2,s.send_anonymous_usage_stats=o?"yes":"no",s),await fetch(Ln("/api/settings.json"),{method:"POST",body:JSON.stringify({settings:s})})},s,function(e){o=e,n(0,o)}]}class yf extends be{constructor(e){super(),ve(this,e,vf,bf,_e,{settings:2})}}const kf=async({fetch:e,data:t})=>({...t,settings:{},gitIgnore:""}),Ff=Object.freeze(Object.defineProperty({__proto__:null,load:kf},Symbol.toStringTag,{value:"Module"}));function wf(e){let t,n="Settings are only available in development mode.";return{c(){t=w("p"),t.textContent=n},l(e){t=k(e,"P",{"data-svelte-h":!0}),"svelte-591hpj"!==oe(t)&&(t.textContent=n)},m(e,n){j(e,t,n)},p:re,i:re,o:re,d(e){e&&p(t)}}}function Cf(e){let t,n,s,o,r,a,i,l,c,u,d,f,m,h,$,v,b,y,x,I,S,C,N,A,O,_,L,D='<div class="max-w-7xl px-6 sm:px-8 md:px-12 mx-auto"><a href="/" class="block text-sm text-base-content-muted mb-2">← Home</a> <h1 class="text-xl text-base-content font-bold">Project Settings</h1></div>',V='<div class="flex flex-col gap-4 text-sm text-base-content-muted"><a href="#sources" class="hover:text-base-content transition-colors">Sources</a> <a href="#deployment" class="hover:text-base-content transition-colors">Deployment</a> <a href="#formatting" class="hover:text-base-content transition-colors">Value Formatting</a> <a href="#telemetry" class="hover:text-base-content transition-colors">Telemetry</a></div>',F='<h2 class="text-2xl text-base-content font-semibold mb-4 text-pretty">Sources</h2> <p class="text-base-content text-base mb-2">Sources connect your Evidence project to databases, local files, and APIs. Each source\n\t\t\t\t\t\t\tcreates a directory in your project under <code class="markdown">/sources</code> where\n\t\t\t\t\t\t\tyou can add queries.\n\t\t\t\t\t\t\t<a href="https://docs.evidence.dev/core-concepts/data-sources/" target="_blank" class="markdown">Learn more about sources.</a></p>',H='<h2 class="text-2xl text-base-content font-semibold mb-4">Deployment</h2> <p class="text-base-content text-base mb-2">Evidence projects can be deployed to a variety of cloud environments. The easiest way\n\t\t\t\t\t\t\tto deploy your project with authentication, scheduled updates, and a custom domain is\n\t\t\t\t\t\t\twith Evidence Cloud.\n\t\t\t\t\t\t\t<a href="https://docs.evidence.dev/deployment/overview/" target="_blank" class="markdown">Learn more about deployment.</a></p>',K='<h2 class="text-2xl text-base-content font-semibold mb-4">Value Formatting</h2> <p class="text-base-content text-base mb-2">Evidence supports built-in formats and Excel-style formats. You can apply these\n\t\t\t\t\t\t\tformats using component props or SQL format tags.\n\t\t\t\t\t\t\t<a href="https://docs.evidence.dev/core-concepts/formatting/" target="_blank" class="markdown">Learn more about formatting.</a></p>',U='<h2 class="text-2xl text-base-content font-semibold mb-4">Telemetry</h2> <p class="text-base-content text-base mb-2">The Evidence CLI collects anonymous usage data to help us understand how often the\n\t\t\t\t\t\t\ttool is being used. <a href="https://github.com/evidence-dev/evidence/tree/next/packages/lib/telemetry" target="_blank" class="markdown">View telemetry source code.</a></p>';return d=new yu({props:{availableSourcePlugins:e[3],sources:e[2]}}),v=new Bu({props:{settings:e[0],sources:e[2]}}),S=new pf({props:{customFormattingSettings:e[1]}}),_=new yf({props:{settings:e[0]}}),{c(){t=w("div"),t.innerHTML=D,n=B(),s=w("div"),o=w("div"),r=w("div"),r.innerHTML=V,a=B(),i=w("div"),l=w("section"),c=w("div"),c.innerHTML=F,u=B(),Z(d.$$.fragment),f=B(),m=w("section"),h=w("div"),h.innerHTML=H,$=B(),Z(v.$$.fragment),b=B(),y=w("section"),x=w("div"),x.innerHTML=K,I=B(),Z(S.$$.fragment),C=B(),N=w("section"),A=w("div"),A.innerHTML=U,O=B(),Z(_.$$.fragment),this.h()},l(e){t=k(e,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-1uonzt0"!==oe(t)&&(t.innerHTML=D),n=q(e),s=k(e,"DIV",{class:!0});var g=M(s);o=k(g,"DIV",{class:!0});var w=M(o);r=k(w,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-joiizt"!==oe(r)&&(r.innerHTML=V),a=q(w),i=k(w,"DIV",{class:!0});var E=M(i);l=k(E,"SECTION",{id:!0,class:!0});var T=M(l);c=k(T,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-ofdx38"!==oe(c)&&(c.innerHTML=F),u=q(T),X(d.$$.fragment,T),T.forEach(p),f=q(E),m=k(E,"SECTION",{id:!0,class:!0});var P=M(m);h=k(P,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-axgnvm"!==oe(h)&&(h.innerHTML=H),$=q(P),X(v.$$.fragment,P),P.forEach(p),b=q(E),y=k(E,"SECTION",{id:!0,class:!0});var j=M(y);x=k(j,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-ehtw6i"!==oe(x)&&(x.innerHTML=K),I=q(j),X(S.$$.fragment,j),j.forEach(p),C=q(E),N=k(E,"SECTION",{id:!0,class:!0});var B=M(N);A=k(B,"DIV",{class:!0,"data-svelte-h":!0}),"svelte-16wghy7"!==oe(A)&&(A.innerHTML=U),O=q(B),X(_.$$.fragment,B),B.forEach(p),E.forEach(p),w.forEach(p),g.forEach(p),this.h()},h(){E(t,"class","fixed top-12 left-0 right-0 bg-base-100 z-40 py-6 border-b border-base-200 bg-base-100/90 backdrop-blur"),E(r,"class","fixed w-60 top-48 hidden lg:block"),E(c,"class","mb-6"),E(l,"id","sources"),E(l,"class","scroll-mt-48"),E(h,"class","mb-6"),E(m,"id","deployment"),E(m,"class","scroll-mt-[9.5rem] border-t border-base-300 pt-8"),E(x,"class","mb-6"),E(y,"id","formatting"),E(y,"class","scroll-mt-[9.5rem] border-t border-base-300 pt-8"),E(A,"class","mb-6"),E(N,"id","telemetry"),E(N,"class","scroll-mt-[9.5rem] border-t border-base-300 pt-8"),E(i,"class","flex flex-col lg:ml-60 lg:px-8 gap-12 w-full overflow-x-auto"),E(o,"class","w-full relative flex overflow-x-hidden"),E(s,"class","flex pt-28")},m(e,p){j(e,t,p),j(e,n,p),j(e,s,p),g(s,o),g(o,r),g(o,a),g(o,i),g(i,l),g(l,c),g(l,u),z(d,l,null),g(i,f),g(i,m),g(m,h),g(m,$),z(v,m,null),g(i,b),g(i,y),g(y,x),g(y,I),z(S,y,null),g(i,C),g(i,N),g(N,A),g(N,O),z(_,N,null),L=!0},p(e,t){const n={};8&t&&(n.availableSourcePlugins=e[3]),4&t&&(n.sources=e[2]),d.$set(n);const s={};1&t&&(s.settings=e[0]),4&t&&(s.sources=e[2]),v.$set(s);const o={};2&t&&(o.customFormattingSettings=e[1]),S.$set(o);const r={};1&t&&(r.settings=e[0]),_.$set(r)},i(e){L||(T(d.$$.fragment,e),T(v.$$.fragment,e),T(S.$$.fragment,e),T(_.$$.fragment,e),L=!0)},o(e){P(d.$$.fragment,e),P(v.$$.fragment,e),P(S.$$.fragment,e),P(_.$$.fragment,e),L=!1},d(e){e&&(p(t),p(n),p(s)),J(d),J(v),J(S),J(_)}}}function Tf(e){let t,n,s,o;const r=[Cf,wf],a=[];return t=1,n=a[1]=r[1](e),{c(){n.c(),s=fe()},l(e){n.l(e),s=fe()},m(e,t){a[1].m(e,t),j(e,s,t),o=!0},p(e,[t]){n.p(e,t)},i(e){o||(T(n),o=!0)},o(e){P(n),o=!1},d(e){e&&p(s),a[1].d(e)}}}function Ef(e,t,n){let{data:s}=t,{settings:o,customFormattingSettings:r,sources:a,plugins:i}=s;return e.$$set=e=>{"data"in e&&n(4,s=e.data)},e.$$.update=()=>{16&e.$$.dirty&&n(0,({settings:o,customFormattingSettings:r,sources:a,plugins:i}=s),o,(n(1,r),n(4,s)),(n(2,a),n(4,s)),(n(3,i),n(4,s)))},[o,r,a,i,s]}class qf extends be{constructor(e){super(),ve(this,e,Ef,Tf,_e,{data:4})}}export{qf as component,Ff as universal};