import{s as Y,d as h,G as ne,i as k,e as Q,H as re,l as V,b as S,h as C,q as le,k as B,j as q,m as R,n as D,v as Z,F as ie,t as ae,w as oe,x as A,y as T,p as ue,I as fe,J as ce,r as N,K as me}from"../chunks/scheduler.D0cbHTIG.js";import{S as $,i as x,t as E,a as p,g as O,c as j,d as I,m as H,b as P,e as z,f as F}from"../chunks/index.YnsWT1Qn.js";import{s as de,b as _e,g as he,E as G,a as J,c as be,D as ge,d as K}from"../chunks/VennDiagram.svelte_svelte_type_style_lang.CjNovnpU.js";import"../chunks/entry.sFL81Vq-.js";import{b as L}from"../chunks/inferColumnTypes.Bz8pcAS_.js";import{B as M}from"../chunks/Button.BvUdNbiN.js";function W(e){let s,t,n,a,r;return t=new M({props:{size:"sm",outline:!0,icon:e[1]?G:J,$$slots:{default:[ye]},$$scope:{ctx:e}}}),t.$on("click",e[12]),a=new M({props:{size:"sm",variant:"positive",outline:!0,icon:be,$$slots:{default:[we]},$$scope:{ctx:e}}}),a.$on("click",e[13]),{c(){s=R("div"),z(t.$$.fragment),n=D(),z(a.$$.fragment),this.h()},l(e){s=C(e,"DIV",{class:!0});var r=q(s);P(t.$$.fragment,r),n=B(r),P(a.$$.fragment,r),r.forEach(h),this.h()},h(){S(s,"class","absolute bottom-2 right-2 z-10 flex gap-2")},m(e,l){k(e,s,l),H(t,s,null),Q(s,n),H(a,s,null),r=!0},p(e,s){const n={};2&s&&(n.icon=e[1]?G:J),131074&s&&(n.$$scope={dirty:s,ctx:e}),t.$set(n);const r={};131072&s&&(r.$$scope={dirty:s,ctx:e}),a.$set(r)},i(e){r||(p(t.$$.fragment,e),p(a.$$.fragment,e),r=!0)},o(e){E(t.$$.fragment,e),E(a.$$.fragment,e),r=!1},d(e){e&&h(s),I(t),I(a)}}}function pe(e){let s;return{c(){s=T("Show Results")},l(e){s=A(e,"Show Results")},m(e,t){k(e,s,t)},d(e){e&&h(s)}}}function ke(e){let s;return{c(){s=T("Hide Results")},l(e){s=A(e,"Hide Results")},m(e,t){k(e,s,t)},d(e){e&&h(s)}}}function ye(e){let s;function t(e,s){return e[1]?ke:pe}let n=t(e),a=n(e);return{c(){a.c(),s=N()},l(e){a.l(e),s=N()},m(e,t){a.m(e,t),k(e,s,t)},p(e,r){n!==(n=t(e))&&(a.d(1),a=n(e),a&&(a.c(),a.m(s.parentNode,s)))},d(e){e&&h(s),a.d(e)}}}function we(e){let s;return{c(){s=T("Submit")},l(e){s=A(e,"Submit")},m(e,t){k(e,s,t)},d(e){e&&h(s)}}}function U(e){let s,t,n=e[9].error+"";return{c(){s=R("pre"),t=T(n),this.h()},l(e){s=C(e,"PRE",{class:!0});var a=q(s);t=A(a,n),a.forEach(h),this.h()},h(){S(s,"class","text-negative text-xs font-mono")},m(e,n){k(e,s,n),Q(s,t)},p(e,s){512&s&&n!==(n=e[9].error+"")&&oe(t,n)},d(e){e&&h(s)}}}function X(e){let s,t,n,a;return t=new ge({props:{data:e[9]}}),{c(){s=R("div"),z(t.$$.fragment)},l(e){s=C(e,"DIV",{});var n=q(s);P(t.$$.fragment,n),n.forEach(h)},m(e,n){k(e,s,n),H(t,s,null),a=!0},p(e,s){const n={};512&s&&(n.data=e[9]),t.$set(n)},i(e){a||(p(t.$$.fragment,e),e&&ue((()=>{a&&(n||(n=F(s,K,{},!0)),n.run(1))})),a=!0)},o(e){E(t.$$.fragment,e),e&&(n||(n=F(s,K,{},!1)),n.run(0)),a=!1},d(e){e&&h(s),I(t),e&&n&&n.end()}}}function Ee(e){let s,t,n,a,r,l,o,i,c,u,d="SQL Console",m=e[9].error&&!e[2]&&!!e[4],$=!e[3]&&W(e),f=m&&U(e),g=e[1]&&X(e);return{c(){s=R("h1"),s.textContent=d,t=D(),n=R("section"),a=R("div"),$&&$.c(),l=D(),f&&f.c(),o=D(),g&&g.c(),this.h()},l(e){s=C(e,"H1",{class:!0,"data-svelte-h":!0}),"svelte-7ylf69"!==le(s)&&(s.textContent=d),t=B(e),n=C(e,"SECTION",{class:!0,role:!0});var r=q(n);a=C(r,"DIV",{class:!0});var i=q(a);$&&$.l(i),i.forEach(h),l=B(r),f&&f.l(r),o=B(r),g&&g.l(r),r.forEach(h),this.h()},h(){S(s,"class","markdown"),S(a,"class","w-full relative rounded-sm border border-base-300 min-h-[8rem] cursor-text **:[&.cm-editor]:min-h-[8rem] **:[&.cm-editor]:rounded-sm"),S(n,"class","px-0 py-2 flex flex-col gap-2 min-h-[8rem]"),S(n,"role","none")},m(d,h){k(d,s,h),k(d,t,h),k(d,n,h),Q(n,a),$&&$.m(a,null),e[14](a),Q(n,l),f&&f.m(n,null),Q(n,o),g&&g.m(n,null),i=!0,c||(u=[re(r=de.call(null,a,{...e[5],theme:e[8]})),V(n,"click",e[15]),V(n,"keydown",e[16])],c=!0)},p(e,[s]){e[3]?$&&(O(),E($,1,1,(()=>{$=null})),j()):$?($.p(e,s),8&s&&p($,1)):($=W(e),$.c(),p($,1),$.m(a,null)),r&&me(r.update)&&288&s&&r.update.call(null,{...e[5],theme:e[8]}),532&s&&(m=e[9].error&&!e[2]&&!!e[4]),m?f?f.p(e,s):(f=U(e),f.c(),f.m(n,o)):f&&(f.d(1),f=null),e[1]?g?(g.p(e,s),2&s&&p(g,1)):(g=X(e),g.c(),p(g,1),g.m(n,null)):g&&(O(),E(g,1,1,(()=>{g=null})),j())},i(e){i||(p($),p(g),i=!0)},o(e){E($),E(g),i=!1},d(a){a&&(h(s),h(t),h(n)),$&&$.d(),e[14](null),f&&f.d(),g&&g.d(),c=!1,ne(u)}}}function Se(e,s,t){let n,a,r=Z,l=()=>(r(),r=fe(p,(e=>t(9,a=e))),p);e.$$.on_destroy.push((()=>r()));let o,i,{hideErrors:c=!1}=s,{initialQuery:u="select 'ABC' as category, 123 as num, 26400000 as sales_usd"}=s,{showResults:d=!0}=s,{disabled:h=!1}=s,m=u,$=m,{data:p=L(m)}=s;l(),ie((async()=>{p&&p.fetch(),t(5,i={initialState:u,disabled:h,schema:await _e(),onChange:e=>{e.docChanged&&$.trim()!==e.state.doc.toString().trim()&&t(6,$=e.state.doc.toString())},onSubmit:()=>(t(4,m=$.trim()),m.endsWith(";")&&t(4,m=m.substring(0,m.length-1)),t(1,d=!0),!0)})}));const{theme:f}=he();return ae(e,f,(e=>t(8,n=e))),e.$$set=e=>{"hideErrors"in e&&t(2,c=e.hideErrors),"initialQuery"in e&&t(11,u=e.initialQuery),"showResults"in e&&t(1,d=e.showResults),"disabled"in e&&t(3,h=e.disabled),"data"in e&&l(t(0,p=e.data))},e.$$.update=()=>{17&e.$$.dirty&&m&&(l(t(0,p=L(m))),p.fetch()),40&e.$$.dirty&&i&&t(5,i.disabled=h,i)},[p,d,c,h,m,i,$,o,n,a,f,u,()=>t(1,d=!d),()=>{t(4,m=$),t(1,d=!0)},function(e){ce[e?"unshift":"push"]((()=>{o=e,t(7,o)}))},()=>null==o?void 0:o.focus(),e=>"Enter"===e.key&&(null==o?void 0:o.focus())]}class Ce extends ${constructor(e){super(),x(this,e,Se,Ee,Y,{hideErrors:2,initialQuery:11,showResults:1,disabled:3,data:0})}}function Re(e){let s,t;return s=new Ce({}),{c(){z(s.$$.fragment)},l(e){P(s.$$.fragment,e)},m(e,n){H(s,e,n),t=!0},p:Z,i(e){t||(p(s.$$.fragment,e),t=!0)},o(e){E(s.$$.fragment,e),t=!1},d(e){I(s,e)}}}class He extends ${constructor(e){super(),x(this,e,null,Re,Y,{})}}export{He as component};